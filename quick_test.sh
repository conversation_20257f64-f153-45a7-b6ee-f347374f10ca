#!/bin/bash

# 快速测试脚本 - 演示新的目录结构功能

echo "🚀 快速测试 extract_screencaps_folders.sh"
echo "=========================================="

# 清理之前的测试
rm -rf test_demo extracted_screencaps

# 创建简单的测试结构
echo "📁 创建测试目录结构..."
mkdir -p test_demo

# 场景1: 普通测试目录
mkdir -p test_demo/video_test/screencaps
echo "video1.mp4" > test_demo/video_test/screencaps/video1.mp4
echo "screenshot1.png" > test_demo/video_test/screencaps/screenshot1.png

# 场景2: 项目目录
mkdir -p test_demo/my_project/screencaps
echo "project_img1.jpg" > test_demo/my_project/screencaps/project_img1.jpg
echo "project_img2.png" > test_demo/my_project/screencaps/project_img2.png
mkdir -p test_demo/my_project/screencaps/subfolder
echo "sub_image.png" > test_demo/my_project/screencaps/subfolder/sub_image.png

# 场景3: 深层嵌套
mkdir -p test_demo/deep/nested/path/app_test/screencaps
echo "deep_screenshot.png" > test_demo/deep/nested/path/app_test/screencaps/deep_screenshot.png

echo ""
echo "📋 当前测试目录结构："
echo "test_demo/"
echo "├── video_test/screencaps/          (2个文件)"
echo "├── my_project/screencaps/          (3个文件，含子目录)"
echo "└── deep/nested/path/app_test/screencaps/  (1个文件)"

echo ""
echo "🔍 预览模式 - 看看会创建什么结构："
cd test_demo
../extract_screencaps_folders.sh -d

echo ""
echo "✅ 实际执行复制："
../extract_screencaps_folders.sh

echo ""
echo "📊 查看结果："
if [ -d "extracted_screencaps" ]; then
    echo "生成的目录结构："
    tree extracted_screencaps 2>/dev/null || find extracted_screencaps -type d | sed 's|[^/]*/|  |g'
    
    echo ""
    echo "文件列表："
    find extracted_screencaps -type f | sort
else
    echo "❌ 未找到输出目录"
fi

cd ..

echo ""
echo "🎯 预期结果应该是："
echo "extracted_screencaps/"
echo "├── video_test/"
echo "│   └── screencaps/"
echo "│       ├── video1.mp4"
echo "│       └── screenshot1.png"
echo "├── my_project/"
echo "│   └── screencaps/"
echo "│       ├── project_img1.jpg"
echo "│       ├── project_img2.png"
echo "│       └── subfolder/"
echo "│           └── sub_image.png"
echo "└── app_test/"
echo "    └── screencaps/"
echo "        └── deep_screenshot.png"

echo ""
echo "🧹 清理测试文件："
echo "rm -rf test_demo"
echo ""
echo "✨ 测试完成！"
