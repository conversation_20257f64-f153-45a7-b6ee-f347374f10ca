#!/bin/bash
if [ $# -lt 1 ]; then
    echo "error.. need at least 1 IP argument"
    exit 1
fi

function get_stats(){
	echo 'monitor ip: '$1
	device_serialno=$1:5555
	device_ip=$1:8088
	thread_num=`adb -s $device_serialno shell ps -t adbd|grep adbd|wc -l`
	thread_num=`echo $thread_num|awk '{print int($0)}'`
	echo "adb thread is: $thread_num"
	if [ $thread_num -ge 12 ];then
		echo "restart adbd......................."
		curl $device_ip/restart
	fi
}

function start_adbdm(){
	echo 'minitor adbdm: '$1
	adb_st=`adb -s $1:5555 shell ls -d`
	# shellcheck disable=SC2076
	if [[ $adb_st =~ "." ]];then
		adbdm_stats=`adb -s $1:5555 shell ps|grep adbdm`
		if [[ $adbdm_stats =~ "com.xiaomi.mitv.adbdm" ]]; then
			echo "...adbdm is ok..."
		else
			echo "......adbdm not start,start adbdm......"
/usr/bin/expect  << EOF
		set timeout 10
		spawn adb -s $1:5555 shell
		expect "shell"
		send "su\r"
		expect "root"
		send "am startservice -n com.xiaomi.mitv.adbdm/com.xiaomi.mitv.adbdm.AdbdCtrl\r"
		expect eof
EOF
		fi
	else
		echo 'device error! disconnect '$ipaddr
                curl $1:8088/restart
                adb disconnect $ipaddr
	fi
}
		
echo "monitor IP list:"
echo $@
while [ true ]
do
sleep 30
for ipaddr in "$@"
do
    echo 'scan port 5555 for IP:' $ipaddr
    #connect_try=`nc -w 1 $ipaddr 5555 && echo 1 || echo 0`
    #connect_try=`nmap -v $ipaddr -p 5555 | grep \"5555/tcp\ open\" > /dev/null && echo 1 || echo 0`
    #connect_try=`nmap $ipaddr -p 5555 | grep \"open\" > /dev/null && echo 1 || echo 0`
    #connect_try=nmap $ipaddr -p 5555 | grep "5555/tcp\ open" > /dev/null && echo 1 || echo 0
    
    ret=`nmap $ipaddr -p 5555`
    if [[ $ret =~ "5555/tcp open" ]]; then
        connect_try="1"
    else
        connect_try="0"        
    fi
    if [ $connect_try = "1" ]; then
        echo 'discovered open port 5555/tcp on IP:' $ipaddr
        sn=$ipaddr:5555
        adb_ret=`adb devices`
        if [[ $adb_ret =~ $sn ]]; then
            echo 'have!'
            #if found in 'adb devices' list. check the device state device or offline
            state_ret=`adb -s $ipaddr:5555 get-state`
            if [[ $state_ret == "device" ]]; then
		adb -s $ipaddr:5555 shell ls -d &
		commandid=$!
		sleep 3
		state=`ps --no-heading $commandid | wc -l`
		if [[ $state = "0" ]];then
		    get_stats $ipaddr
		    start_adbdm $ipaddr
		else
		    echo 'device error! disconnect '$ipaddr
		    kill -s 9 $commandid
                    curl $ipaddr:8088/restart
                    adb disconnect $ipaddr
		fi
                
            elif [[ $state_ret == "offline" ]]; then
                echo 'device offline! disconnect '$ipaddr
                curl $ipaddr:8088/restart
                adb disconnect $ipaddr
            else
                echo 'unknown device state! disconnect '$ipaddr
                curl $ipaddr:8088/restart
                adb disconnect $ipaddr
            fi
        else
            echo 'not have'
            adb_connect_ret=`adb connect $ipaddr`
            ok_msg='connected to '$ipaddr:5555
            echo $ok_msg
            #find or contains
            if [[ $adb_connect_ret =~ $ok_msg ]]; then
                echo "connect $ipaddr ok!"
            else
                echo "connect $ipaddr failed!"
            fi
        fi
    else
        sn=$ipaddr:5555
        adb_ret=`adb devices`
        if [[ $adb_ret =~ $sn ]]; then
            adb disconnect $ipaddr
            echo 'disconnect ' $sn
        fi    
    fi
    echo "###################################################"
done
done

