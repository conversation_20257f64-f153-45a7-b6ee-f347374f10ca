script/testcases/simple.py:SimpleModelTest.testSimpleHome
script/testcases/simple.py:SimpleModelTest.testSwithChannel
script/testcases/simple.py:SimpleModelTest.testplayvideo
script/testcases/simple.py:SimpleModelTest.testLaunchAndExitMediaExplorer
script/testcases/simple.py:SimpleModelTest.testSimsearch
script/testcases/simple.py:SimpleModelTest.testLaunchAndExitSetting
script/testcases/simple.py:SimpleModelTest.testPlayPatchVideo
script/testcases/simple.py:SimpleModelTest.testlaunchSmartshare
script/testcases/testAppstoreApk.py:AppstroeApkTest.testLutongnetNldmx
script/testcases/testAppstoreApk.py:AppstroeApkTest.testGongfubbWksz
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromYouku
script/testcases/testAppstoreApk.py:AppstroeApkTest.testBaoShengKtv
script/testcases/testAppstoreApk.py:AppstroeApkTest.testChangBaSd
script/testcases/testAppstoreApk.py:AppstroeApkTest.testFittime
script/testcases/testAppstoreApk.py:AppstroeApkTest.testLutongnetOtt
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromAiQiYi
script/testcases/testAppstoreApk.py:AppstroeApkTest.testBilibili
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPptvTvsports
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromDianshimao
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromYunshiting
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromMangguo