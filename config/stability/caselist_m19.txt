script/testcases_m19/applicationtest.py:test1.testhome
script/testcases_m19/applicationtest.py:test1.testGooglesignin
script/testcases_m19/applicationtest.py:test1.testSlingdownload
script/testcases_m19/applicationtest.py:test1.testSlingSignin
script/testcases_m19/applicationtest.py:test1.testnetflixSignin
script/testcases_m19/applicationtest.py:test1.testnetflix
script/testcases_m19/applicationtest.py:test1.testsling
script/testcases_m19/applicationtest.py:test1.testcbs
script/testcases_m19/applicationtest.py:test1.testlaunchapp
script/testcases_m19/applicationtest.py:test1.testhome
script/testcases_m19/applicationtest.py:test1.testfavoriteapps
script/testcases_m19/applicationtest.py:test1.testChannelslist_netflix
script/testcases_m19/applicationtest.py:test1.testChannelslist_redbull
script/testcases_m19/applicationtest.py:test1.testChannelslist_GooglePlayMusic
script/testcases_m19/applicationtest.py:test1.testChannelslist_YouTube
script/testcases_m19/applicationtest.py:test1.testChannelslist_PlayMovies
script/testcases_m19/applicationtest.py:test1.testRedbullPlay
script/testcases_m19/applicationtest.py:test1.testYoutubePlay
script/testcases_m19/applicationtest.py:test1.testGooglePlayMovies
script/testcases_m19/applicationtest.py:test1.testLaunchAndExitLitvTV
script/testcases_m19/applicationtest.py:test1.testLiveChannelsChannels
script/testcases_m19/applicationtest.py:test1.testLiveChannelsProgramGuid
script/testcases_m19/applicationtest.py:test1.testLiveChannelsClosedCaptions
script/testcases_m19/applicationtest.py:test1.testLiveChannelsPIP
script/testcases_m19/applicationtest.py:test1.testLiveChannelsTVOptionsSettings
script/testcases_m19/applicationtest.py:test1.testLiveChannelsChannelsourcePLutoTV
script/testcases_m19/applicationtest.py:test1.testLiveChannelsChannelsourceplayMovies
script/testcases_m19/applicationtest.py:test1.testLiveChannelsGetmoresources
script/testcases_m19/applicationtest.py:test1.testLiveChannelsGoogleAll
script/testcases_m19/applicationtest.py:test1.testGoogleSearch
script/testcases_m19/applicationtest.py:test1.testGooglePlayStore
script/testcases_m19/applicationtest.py:test1.testGooglePlayGames
script/testcases_m19/applicationtest.py:test1.testSettingsNetwork
script/testcases_m19/applicationtest.py:test1.testSettingsgooglecast
script/testcases_m19/applicationtest.py:test1.testSettingsDisplay
script/testcases_m19/applicationtest.py:test1.testSwitchDisplayMode
script/testcases_m19/applicationtest.py:test1.testSwitchColorSpace
script/testcases_m19/applicationtest.py:test1.testScreenPosition
script/testcases_m19/applicationtest.py:test1.testSettingsSound
script/testcases_m19/applicationtest.py:test1.testSettingsSwitchSound
script/testcases_m19/applicationtest.py:test1.testSettingsApps
script/testcases_m19/applicationtest.py:test1.testSettingsScreensaver
script/testcases_m19/applicationtest.py:test1.testSettingsStorageandreset
script/testcases_m19/applicationtest.py:test1.testSettingsAbout
script/testcases_m19/applicationtest.py:test1.testSettingsDateandtime
script/testcases_m19/applicationtest.py:test1.testSettingsLanguage
script/testcases_m19/applicationtest.py:test1.testSettingsKeyboard
script/testcases_m19/applicationtest.py:test1.testSettingsHomescreen
script/testcases_m19/applicationtest.py:test1.testSettingsSearch
script/testcases_m19/applicationtest.py:test1.testSettingsGoogle
script/testcases_m19/applicationtest.py:test1.testSettingsSpeech
script/testcases_m19/applicationtest.py:test1.testSettingsPlaybacksettings
script/testcases_m19/applicationtest.py:test1.testSettingsAccessibility
script/testcases_m19/applicationtest.py:test1.testSettingsHDMICEC
script/testcases_m19/applicationtest.py:test1.testSettingsAddaccessory
script/testcases_m19/applicationtest.py:test1.testSettingsLocation
script/testcases_m19/applicationtest.py:test1.testSettingsSecurityrestrictions
script/testcases_m19/applicationtest.py:test1.testSettingsUsageDiagnostics
script/testcases_m19/applicationtest.py:test1.testSettingsAddAcount
