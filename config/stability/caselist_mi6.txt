#script/testcases/autorun.py:AutoRunTest.testPushAutoRun
#script/testcases/autorun.py:AutoRunTest.testrefresh
#script/testcases/autorun.py:AutoRunTest.testonlinevideo
#script/testcases/autorun.py:AutoRunTest.testappstore
#script/testcases/autorun.py:AutoRunTest.testgamecenter
#script/testcases/autorun.py:AutoRunTest.testlaunchsystemapp
#script/testcases/autorun.py:AutoRunTest.testsearch
script/testcases/systemapp.py:SystemAppTest.testApplicationswitch
script/testcases/systemapp.py:SystemAppTest.testlaunchHomeswitchTab
script/testcases/systemapp.py:SystemAppTest.testfamilycamera
script/testcases/randomOp.py:RandomTest.testRandom
script/testcases/atvsource.py:ATVSource.testSwithChannel
script/testcases/randomOp.py:RandomTest.testRefresh
script/testcases/onlinevideo.py:OnlineVideoTest.testPlayOnlineVideo
#script/testcases/monkey.py:MonkeyTest.testCalendar
#script/testcases/monkey.py:MonkeyTest.testTweather
#script/testcases/monkey.py:MonkeyTest.testUmifrontend
#script/testcases/monkey.py:MonkeyTest.testShop
#script/testcases/monkey.py:MonkeyTest.testGallery
script/testcases/onlinevideo.py:OnlineVideoTest.testplaylocalmusic
script/testcases/onlinevideo.py:OnlineVideoTest.testplayvideo
script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitMediaExplorer
script/testcases/systemapp.py:SystemAppTest.testSearch
script/testcases/systemapp.py:SystemAppTest.testSwitchSong
script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitCloudGallery
#script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitFM
script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitSetting
script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitMediaExplorer
script/testcases/systemapp.py:SystemAppTest.testlaunchWeather
script/testcases/systemapp.py:SystemAppTest.testlaunchCalendar
script/testcases/systemapp.py:SystemAppTest.testlaunchTVshop
script/testcases/systemapp.py:SystemAppTest.testLaunchHandbook
script/testcases/systemapp.py:SystemAppTest.testlaunchNotification
script/testcases/systemapp.py:SystemAppTest.testlaunchGallery
#script/testcases/systemapp.py:SystemAppTest.testlaunchSmartshare
script/testcases/systemapp.py:SystemAppTest.testlaunchHome
script/testcases/systemapp.py:SystemAppTest.testPlayDuokanVideo
script/testcases/systemapp.py:SystemAppTest.testPlayPatchVideo
#script/testcases/gamecenter.py:GameCenterTest.testLaunchAndExitAppStore
#script/testcases/gamecenter.py:GameCenterTest.testInstallAndUninstallGame
#script/testcases/systemapp.py:SystemAppTest.testlaunchTVmanager
script/testcases/testAppstoreApk.py:AppstroeApkTest.testLutongnetNldmx
script/testcases/testAppstoreApk.py:AppstroeApkTest.testGongfubbWksz
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromYouku
script/testcases/testAppstoreApk.py:AppstroeApkTest.testBaoShengKtv
script/testcases/testAppstoreApk.py:AppstroeApkTest.testChangBaSd
script/testcases/testAppstoreApk.py:AppstroeApkTest.testFittime
script/testcases/testAppstoreApk.py:AppstroeApkTest.testLutongnetOtt
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromAiQiYi
script/testcases/testAppstoreApk.py:AppstroeApkTest.testBilibili
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPptvTvsports
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromDianshimao
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromYunshiting
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromMangguo