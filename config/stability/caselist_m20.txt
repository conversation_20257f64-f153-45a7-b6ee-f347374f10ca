script/testcases/monkey.py:MonkeyTest.testPatch
script/testcases/systemapp.py:SystemAppTest.testApplicationswitch
script/testcases/systemapp.py:SystemAppTest.testlaunchHomeswitchTab
script/testcases/randomOp.py:RandomTest.testRandom
script/testcases/systemapp.py:SystemAppTest.testSearch
script/testcases/monkey.py:MonkeyTest.testMediaexplorer
script/testcases/monkey.py:MonkeyTest.testVideoDaily
script/testcases/monkey.py:MonkeyTest.testKtcp
script/testcases/monkey.py:MonkeyTest.testBili
script/testcases/monkey.py:MonkeyTest.testAqy
script/testcases/monkey.py:MonkeyTest.testYk
script/testcases/monkey.py:MonkeyTest.testMg
script/testcases/monkey.py:MonkeyTest.testChildren
script/testcases/monkey.py:MonkeyTest.testCctvNew
script/testcases/monkey.py:MonkeyTest.testNetease
script/testcases/monkey.py:MonkeyTest.testMore
script/testcases/onlinevideo.py:OnlineVideoTest.testPlayOnlineVideo
script/testcases/systemapp.py:SystemAppTest.testPlayPatchVideo
script/testcases/onlinevideo.py:OnlineVideoTest.testplaylocalmusic
script/testcases/onlinevideo.py:OnlineVideoTest.testplayvideo
script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitMediaExplorer
script/testcases/atvsource.py:ATVSource.testSwitchChannel
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromAiQiYi
script/testcases/testAppstoreApk.py:AppstroeApkTest.testBilibili
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromYouku
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromYunshiting
script/testcases/testAppstoreApk.py:AppstroeApkTest.testPlayVideoFromMangguo
script/testcases/testAppstoreApk.py:AppstroeApkTest.test_KuaiTv
script/testcases/testAppstoreApk.py:AppstroeApkTest.test_qqmusic
script/testcases/monkey.py:MonkeyTest.testVoice
script/testcases/monkey.py:MonkeyTest.testTweather
script/testcases/monkey.py:MonkeyTest.testGallery
script/testcases/monkey.py:MonkeyTest.testAppstore
script/testcases/monkey.py:MonkeyTest.testSmart
script/testcases/monkey.py:MonkeyTest.testNotification
script/testcases/monkey.py:MonkeyTest.testGalleryphoto
#script/testcases/systemapp.py:SystemAppTest.testlaunchCalendar
#script/testcases/systemapp.py:SystemAppTest.test_SmartHome1
#script/testcases/systemapp.py:SystemAppTest.test_SmartHome2
#script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitSetting
#script/testcases/systemapp.py:SystemAppTest.testLaunchAndExitMediaExplorer
#script/testcases/gamecenter.py:GameCenterTest.testLaunchAndExitAppStore
#script/testcases/gamecenter.py:GameCenterTest.testInstallAndUninstallGame
#script/testcases/systemapp.py:SystemAppTest.testlaunchTVmanager
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testLutongnetNldmx
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testGongfubbWksz
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testBaoShengKtv
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testChangBaSd
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testFittime
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testLutongnetOtt
#script/testcases/testAppstoreApk.py:AppstroeApkTest.testPptvTvsports
#script/testcases/monkey.py:MonkeyTest.testSetting
#script/testcases/systemapp.py:SystemAppTest.testSwitchSong
#script/testcases/autorun.py:AutoRunTest.testPushAutoRun
#script/testcases/autorun.py:AutoRunTest.testrefresh
#script/testcases/autorun.py:AutoRunTest.testonlinevideo
#script/testcases/autorun.py:AutoRunTest.testappstore
#script/testcases/autorun.py:AutoRunTest.testgamecenter
#script/testcases/autorun.py:AutoRunTest.testlaunchsystemapp
#script/testcases/autorun.py:AutoRunTest.testsearch
#script/testcases/monkey.py:MonkeyTest.testwps
#script/testcases/monkey.py:MonkeyTest.testchangba
#script/testcases/monkey.py:MonkeyTest.testJJ
#script/testcases/testAppstoreApk.py:AppstroeApkTest.test_TVhome
#script/testcases/monkey.py:MonkeyTest.testAlarm
#script/testcases/monkey.py:MonkeyTest.testShop
#script/testcases/monkey.py:MonkeyTest.testHandBook