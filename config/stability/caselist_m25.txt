script/testcases_m25/applicationtest.py:test1.testhome
script/testcases_m25/applicationtest.py:test1.testDisplaySound
script/testcases_m25/applicationtest.py:test1.testRename
script/testcases_m25/applicationtest.py:test1.testGooglesignin
script/testcases_m25/applicationtest.py:test1.testSlingdownload
script/testcases_m25/applicationtest.py:test1.testnetflixSignin
script/testcases_m25/applicationtest.py:test1.testnetflix
script/testcases_m25/applicationtest.py:test1.testprimevideo
script/testcases_m25/applicationtest.py:test1.testlaunchapp
script/testcases_m25/applicationtest.py:test1.testhome
script/testcases_m25/applicationtest.py:test1.testfavoriteapps
script/testcases_m25/applicationtest.py:test1.testYoutubePlay
script/testcases_m25/applicationtest.py:test1.testGoogleSearch
script/testcases_m25/applicationtest.py:test1.testGooglePlayStore
script/testcases_m25/applicationtest.py:test1.testGooglePlayGames
script/testcases_m25/applicationtest.py:test1.testSettingsNetwork
script/testcases_m25/applicationtest.py:test1.testSettingsgooglecast
script/testcases_m25/applicationtest.py:test1.testSettingsDisplay
script/testcases_m25/applicationtest.py:test1.testSwitchDisplayMode
script/testcases_m25/applicationtest.py:test1.testSwitchColorSpace
script/testcases_m25/applicationtest.py:test1.testScreenPosition
script/testcases_m25/applicationtest.py:test1.testSettingsSound
script/testcases_m25/applicationtest.py:test1.testSettingsSwitchSound
script/testcases_m25/applicationtest.py:test1.testSettingsApps
script/testcases_m25/applicationtest.py:test1.testSettingsScreensaver
script/testcases_m25/applicationtest.py:test1.testSettingsStorageandreset
script/testcases_m25/applicationtest.py:test1.testSettingsAbout
script/testcases_m25/applicationtest.py:test1.testSettingsDateandtime
script/testcases_m25/applicationtest.py:test1.testSettingsLanguage
script/testcases_m25/applicationtest.py:test1.testSettingsKeyboard
script/testcases_m25/applicationtest.py:test1.testSettingsPowerEnergy
script/testcases_m25/applicationtest.py:test1.testSettingsAccessibility
script/testcases_m25/applicationtest.py:test1.testSettingsHDMICEC
script/testcases_m25/applicationtest.py:test1.testSettingsAddaccessory
script/testcases_m25/applicationtest.py:test1.testSettingsAddAcount
script/testcases_m25/applicationtest.py:test1.testSettingsHelpFeedback
script/testcases_m25/applicationtest.py:test1.testMediaPlayer