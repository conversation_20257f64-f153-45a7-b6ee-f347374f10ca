#!/bin/bash

# 简化版screencaps文件夹提取脚本
# 专门用于直接复制整个screencaps文件夹

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认配置
CURRENT_DIR=$(pwd)
EXTRACT_DIR="$CURRENT_DIR/extracted_screencaps"
DRY_RUN=false

# 显示帮助信息
show_help() {
    echo -e "${BLUE}screencaps文件夹提取脚本${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  $0 [选项]"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo "  -h, --help          显示此帮助信息"
    echo "  -o, --output DIR    指定输出目录 (默认: ./extracted_screencaps)"
    echo "  -d, --dry-run       预览模式，不实际复制文件夹"
    echo ""
    echo -e "${YELLOW}功能:${NC}"
    echo "  - 递归查找所有名为 'screencaps' 的文件夹"
    echo "  - 保持上一级目录结构：parent_dir/screencaps/"
    echo "  - 自动处理重名冲突"
    echo "  - 保持screencaps文件夹内部结构不变"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  $0                                    # 复制所有screencaps文件夹"
    echo "  $0 -o /tmp/screenshots               # 指定输出目录"
    echo "  $0 -d                               # 预览模式"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output)
            EXTRACT_DIR="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 主函数
main() {
    echo -e "${BLUE}=== screencaps文件夹提取脚本 ===${NC}"
    echo -e "${YELLOW}当前目录: $CURRENT_DIR${NC}"
    echo -e "${YELLOW}输出目录: $EXTRACT_DIR${NC}"
    echo -e "${YELLOW}模式: 复制整个文件夹${NC}"
    
    if [ "$DRY_RUN" = true ]; then
        echo -e "${PURPLE}*** 预览模式 - 不会实际复制文件夹 ***${NC}"
    fi
    
    echo ""
    
    # 创建输出目录
    if [ "$DRY_RUN" = false ]; then
        mkdir -p "$EXTRACT_DIR"
        if [ $? -ne 0 ]; then
            echo -e "${RED}无法创建输出目录: $EXTRACT_DIR${NC}"
            exit 1
        fi
    fi
    
    # 计数器
    total_files=0
    total_folders=0
    total_size=0

    # 用于跟踪已使用的目录名称，避免重复
    declare -a used_names
    
    # 查找所有screencaps文件夹
    echo -e "${BLUE}正在搜索screencaps文件夹...${NC}"
    
    # 存储找到的文件夹路径
    declare -a screencaps_dirs
    
    while IFS= read -r -d '' screencaps_dir; do
        if [ -d "$screencaps_dir" ]; then
            screencaps_dirs+=("$screencaps_dir")
        fi
    done < <(find "$CURRENT_DIR" -name "screencaps" -type d -print0)
    
    if [ ${#screencaps_dirs[@]} -eq 0 ]; then
        echo -e "${RED}未找到任何screencaps文件夹！${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}找到 ${#screencaps_dirs[@]} 个screencaps文件夹${NC}"
    echo ""
    
    # 处理每个文件夹
    for i in "${!screencaps_dirs[@]}"; do
        screencaps_dir="${screencaps_dirs[$i]}"
        folder_num=$((i + 1))
        
        echo -e "${GREEN}处理文件夹 $folder_num: $screencaps_dir${NC}"
        
        # 检查文件夹是否为空
        if [ -z "$(ls -A "$screencaps_dir")" ]; then
            echo -e "${YELLOW}  -> 文件夹为空，跳过${NC}"
            echo ""
            continue
        fi
        
        # 计算文件夹信息
        file_count=$(find "$screencaps_dir" -type f | wc -l)
        folder_size=$(du -sh "$screencaps_dir" 2>/dev/null | cut -f1)
        
        echo -e "${BLUE}  -> 包含 $file_count 个文件${NC}"
        echo -e "${BLUE}  -> 文件夹大小: $folder_size${NC}"
        
        # 获取screencaps的上一级目录名称
        parent_dir=$(dirname "$screencaps_dir")
        parent_name=$(basename "$parent_dir")

        # 生成目标文件夹名称（使用上一级目录名）
        if [ ${#screencaps_dirs[@]} -eq 1 ]; then
            target_folder_name="$parent_name"
        else
            # 如果有重名，添加序号
            target_folder_name="${parent_name}_${folder_num}"
        fi

        # 检查是否有重名，如果有则添加序号
        original_target_name="$target_folder_name"
        counter=1
        while [[ " ${used_names[@]} " =~ " ${target_folder_name} " ]]; do
            target_folder_name="${original_target_name}_${counter}"
            counter=$((counter + 1))
        done

        # 记录已使用的名称
        used_names+=("$target_folder_name")

        # 目标路径：输出目录/上级目录名/screencaps
        target_parent_path="$EXTRACT_DIR/$target_folder_name"
        target_screencaps_path="$target_parent_path/screencaps"
        
        echo -e "${CYAN}  -> 上级目录: $parent_name${NC}"
        echo -e "${CYAN}  -> 目标结构: $target_folder_name/screencaps/${NC}"
        
        # 复制文件夹
        if [ "$DRY_RUN" = false ]; then
            echo -e "${CYAN}  -> 正在创建目录结构: $target_folder_name/screencaps/${NC}"

            # 创建上级目录
            mkdir -p "$target_parent_path"
            if [ $? -ne 0 ]; then
                echo -e "  ${RED}✗ 创建上级目录失败: $target_parent_path${NC}"
                continue
            fi

            # 复制screencaps文件夹到上级目录下
            cp -r "$screencaps_dir" "$target_screencaps_path"

            if [ $? -eq 0 ]; then
                echo -e "  ${GREEN}✓ 文件夹复制成功${NC}"
                total_folders=$((total_folders + 1))
                total_files=$((total_files + file_count))

                # 验证复制结果
                copied_file_count=$(find "$target_screencaps_path" -type f | wc -l)
                if [ $file_count -eq $copied_file_count ]; then
                    echo -e "  ${GREEN}✓ 文件数量验证通过 ($copied_file_count/$file_count)${NC}"
                else
                    echo -e "  ${YELLOW}⚠ 文件数量不匹配 ($copied_file_count/$file_count)${NC}"
                fi

                # 显示最终路径
                echo -e "  ${BLUE}✓ 最终路径: $target_folder_name/screencaps/${NC}"
            else
                echo -e "  ${RED}✗ 文件夹复制失败${NC}"
            fi
        else
            echo -e "  ${GREEN}✓ [预览] 将创建: $target_folder_name/screencaps/${NC}"
            total_folders=$((total_folders + 1))
            total_files=$((total_files + file_count))
        fi
        
        echo ""
    done
    
    # 输出统计信息
    echo -e "${BLUE}=== 复制完成 ===${NC}"
    echo -e "${GREEN}总共处理 ${#screencaps_dirs[@]} 个screencaps文件夹${NC}"
    echo -e "${GREEN}成功复制 $total_folders 个文件夹${NC}"
    echo -e "${GREEN}总共包含 $total_files 个文件${NC}"
    
    if [ "$DRY_RUN" = false ]; then
        echo -e "${YELLOW}所有文件夹已保存到: $EXTRACT_DIR${NC}"
        
        # 显示输出目录内容
        if [ $total_folders -gt 0 ] && [ -d "$EXTRACT_DIR" ]; then
            echo ""
            echo -e "${BLUE}输出目录内容:${NC}"
            ls -la "$EXTRACT_DIR" | while read line; do
                if [[ $line == d* ]]; then
                    echo -e "  ${GREEN}$line${NC}"
                fi
            done
            
            # 显示总大小
            total_size=$(du -sh "$EXTRACT_DIR" 2>/dev/null | cut -f1)
            echo ""
            echo -e "${CYAN}输出目录总大小: $total_size${NC}"
        fi
    else
        echo -e "${PURPLE}*** 这是预览模式的结果 ***${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}脚本执行完成！${NC}"
}

# 执行主函数
main
