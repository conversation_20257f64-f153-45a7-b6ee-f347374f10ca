#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
测试仅导入时是否会执行get_tv_device_id
"""

import sys
sys.path.append('script/testcases')

print("=== 开始导入测试 ===")
print("导入前...")

# 仅导入，不调用任何函数
from adb_command import adb_home, adb_back, get_tv_device_id, TV_DEVICE_ID

print("导入后...")
print(f"导入时已自动读取TV设备ID: {TV_DEVICE_ID}")

print("\n=== 验证get_tv_device_id函数 ===")
device_id = get_tv_device_id()
print(f"get_tv_device_id()返回: {device_id}")

print("\n=== 现在调用adb函数，应该直接使用已读取的设备ID ===")
print("调用adb_home()时会自动使用TV_DEVICE_ID，无需再次读取配置")

print("\n✅ 结论：导入时就自动读取了TV设备ID，之后调用函数直接使用")
