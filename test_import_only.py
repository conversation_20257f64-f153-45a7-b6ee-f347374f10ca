#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
测试仅导入时是否会执行get_tv_device_id
"""

import sys
sys.path.append('script/testcases')

print("=== 开始导入测试 ===")
print("导入前...")

# 仅导入，不调用任何函数
from adb_command import adb_home, adb_back, get_tv_device_id

print("导入后...")
print("如果你看到这条消息，说明导入时没有执行get_tv_device_id()")
print("因为如果执行了，你会看到'从配置文件读取到TV设备ID'的消息")

print("\n=== 现在手动调用get_tv_device_id ===")
device_id = get_tv_device_id()
print(f"现在才会看到配置读取信息，设备ID: {device_id}")

print("\n✅ 结论：导入时不会执行get_tv_device_id()，只有在实际调用时才会执行")
