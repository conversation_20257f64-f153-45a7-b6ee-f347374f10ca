#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
测试最终的行为：导入时自动获取TV_ID，调用时直接使用
"""

import sys
sys.path.append('script/testcases')

print("=== 第一次导入 ===")
from adb_command import adb_home
print("第一次导入完成")

print("\n=== 第二次导入 ===")  
from adb_command import adb_back, TV_DEVICE_ID
print("第二次导入完成")
print(f"TV_DEVICE_ID已可用: {TV_DEVICE_ID}")

print("\n=== 第三次导入 * ===")
from adb_command import *
print("第三次导入完成")

print(f"\n=== 现在调用adb函数 ===")
print(f"TV_DEVICE_ID = {TV_DEVICE_ID}")
print("调用adb_home()...")
# adb_home()  # 注释掉避免实际执行adb命令
print("adb_home()会自动使用设备ID: 70070206600000030")

print("\n✅ 完美！导入时就获取了TV_ID，调用时直接使用，无需重复读取配置")
