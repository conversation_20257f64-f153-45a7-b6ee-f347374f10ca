# -*- coding:utf-8 -*-

import os
import sys
import time


class UpdateApps:
    def __init__(self,device_id):
        self.device_id = device_id
        self.update_success_tips = "全部应用都已是最新版本"
        self.update_page_mark = "自动更新设置"
        self.app_manager_page_mark = "应用管理"

    def update_3_apps(self):
        app_manager_activity = "com.xiaomi.mitv.appstore/com.xiaomi.mitv.appstore.appmanager.AppManagerActivity"
        current_path = os.getcwd()
        xml_path = os.path.join(current_path, "window_dump.xml")

        os.system("adb -s {} shell input keyevent 3".format(self.device_id))
        time.sleep(3)
        for retry in range(10):
            page_info = self.open_update_page(app_manager_activity, xml_path)
            if page_info is False:
                continue
            elif self.update_success_tips in page_info:
                print("Update success ", self.update_success_tips)
                break
            else:
                time.sleep(5)
                self.open_homepage()
        else:
            print("3 APP Update failed, Boot UpManagerActivity failed...")

    def open_update_page(self, appmanager_activity, xml_path):
        for retry in range(5):
            os.system("adb -s {} shell am start -n {}".format(self.device_id, appmanager_activity))
            time.sleep(5)
            app_manager_info = self.dump_window_xml(xml_path)
            if not app_manager_info:
                time.sleep(5)
                self.open_homepage()
                continue
            elif app_manager_info not in self.app_manager_page_mark:
                time.sleep(5)
                self.open_homepage()
                continue

            os.system("adb -s {} shell input keyevent 22".format(self.device_id))
            time.sleep(2)
            os.system("adb -s {} shell input keyevent 23".format(self.device_id))
            time.sleep(5)
            current_page_info = self.dump_window_xml(xml_path)
            if not current_page_info:
                time.sleep(5)
                self.open_homepage()
                continue
            elif current_page_info == self.update_page_mark:
                self.press_update_all_button()
                time.sleep(15)
                return self.update_page_mark
            elif current_page_info in self.update_success_tips:
                print("Check to update success ", current_page_info)
                return self.update_success_tips
            self.open_homepage()
            time.sleep(5)
        return False

    def press_update_all_button(self):
        os.system("adb -s {} shell input keyevent 22".format(self.device_id))
        time.sleep(2)
        os.system("adb -s {} shell input keyevent 21".format(self.device_id))
        time.sleep(2)
        os.system("adb -s {} shell input keyevent 23".format(self.device_id))
        time.sleep(2)

    def open_homepage(self):
        os.system("adb -s {} shell input keyevent 4".format(self.device_id))
        time.sleep(2)
        os.system("adb -s {} shell input keyevent 4".format(self.device_id))
        time.sleep(2)
        os.system("adb -s {} shell input keyevent 3".format(self.device_id))
        time.sleep(3)

    def current_page_info(self, xml_file):
        with open(xml_file, 'r') as fr:
            lines = fr.readlines()
        for line in lines:
            if self.update_success_tips in line:
                return self.update_success_tips
            elif self.update_page_mark in line:
                return self.update_page_mark
            elif self.app_manager_page_mark in line:
                return self.app_manager_page_mark
        return False

    def dump_window_xml(self, xml_file):
        if os.path.exists(xml_file):
            os.remove(xml_file)
        os.system("adb -s {} shell uiautomator dump".format(self.device_id))
        time.sleep(2)
        os.system("adb -s {} pull /mnt/sdcard/window_dump.xml {}".format(self.device_id, xml_file))
        time.sleep(2)
        if os.path.exists(xml_file):
            print("window dump pull to local successfully")
            page_info = self.current_page_info(xml_file)
            if page_info:
                return page_info


if __name__ == '__main__':
    device_id = "*************"
    uapp = UpdateApps(device_id=device_id)
    uapp.update_3_apps()

