#! /usr/bin/env python
# -*- coding:UTF-8 -*-
import json
import os
import sys
import requests
import traceback
from mfile.upload_to_fds import *

data = {"APP_ID": "cli_a501545fbcf8d062",
        "APP_SECRET": "xpdhefMstFSKgqNnXF2r8gVCvDpW6V7T"}

tenant_url = "https://open.f.mioffice.cn/open-apis/auth/v3/tenant_access_token/internal"
headers = {"Content-Type": "application/json; charset=utf-8"}
res = requests.post(tenant_url, headers=headers, json=data)
TOKEN = str(res.json().get("tenant_access_token"))
HEADERS = {"Authorization": "Bearer %s" % TOKEN}

LIST_RECORD_URL = "https://open.f.mioffice.cn/open-apis/bitable/v1/apps/%s/tables/%s/records"
TABLE_ID = "tblwGoGbgULJtRn3"
APP_TOKEN = "bask4M72pBWvLbKgmlXJwDCtUYb"


def list_records(token, table_id):
    list_url = LIST_RECORD_URL % (token, table_id)
    res = requests.get(list_url, headers=HEADERS)
    current_records = res.json().get("data").get("items")
    jira_project_dict = {}
    for line in current_records:
        try:
            jira_key = line["fields"]["jira库"].lstrip()
            product_name = line["fields"]["产品名"]
            if product_name:
                jira_project_dict[product_name] = jira_key
        except Exception as e:
            pass
    return jira_project_dict



jira_project_dict = list_records(APP_TOKEN, TABLE_ID)
# print(jira_project_dict)
# try:
#     print(jira_project_dict["freeguy"])
# except Exception as e:
#     print(traceback.print_exc())


if __name__ == '__main__':
    # todo save to json
    with open("jira_project.json", "w") as f:
        json.dump(jira_project_dict,f,ensure_ascii=False)
    # print(jira_project_dict)
    # todo upload json file to fds
    fds = Fds()
    fds.upload_to_fds("jira_project.json", "jira_project.json","monkeytest")
