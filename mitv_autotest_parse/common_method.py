#! /usr/bin/env python
# -*- coding:UTF-8 -*-
import os
import subprocess
import time
import re
import shutil


work_dir = os.path.abspath(os.path.dirname(__file__))
print("work dir:",work_dir)

from mdb.app_info import DevAppDataCollection
from mjira.tv_jira import Jira
from mfile.upload_to_fds import *
#########################define global variable#####################
try:
    # from upload_engine import *
    from lark_file_record import *
except:
    # from .upload_engine import *
    from .lark_file_record import *

# ks_engine = KsEngine()
# mongo_collection = DevAppDataCollection()
# jira_db = mongo_collection._collection(collection_name="jira_db",db_name="autotest")

addr = "http://jira.n.xiaomi.com"
os.environ['DJANGO_SETTINGS_MODULE'] = 'settings'
jira_key = "MITVAUTO"     # 提交到jira的项目名

fds_Link = "http://cnbj1-fds.api.xiaomi.net/mitv-autotest/"


class Prop(object):
    def __init__(self, logFolder,deviceId):
        self.logFolder = logFolder
        self.deviceId = deviceId
        self.__prop = self.prop2dict()

    def prop2dict(self):
        try:
            with open(self.logFolder + "/getprop.txt", "r") as __prop_obj:
                props = __prop_obj.readlines()
            if len(props) < 10:  # props 应该不会小于10行
                # todo 重新connect tv再生成一次getprop.txt文件
                cmd_line = "adb connect " + self.deviceId
                obj = subprocess.Popen([cmd_line], shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,stderr=subprocess.PIPE)
                obj.wait()
                state = subprocess.Popen("adb -s %s shell ls -d" % self.deviceId, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
                state.wait()
                out = state.stdout.read().strip()
                if out != b'.':
                    raise IOError("can not connect to device {} ".format(self.deviceId))
                cmd_line = 'adb -s ' + self.deviceId + ' shell getprop > ' + self.logFolder + '/getprop.txt'  # 把getprop.txt保存到project命名的文件夹内
                subprocess.Popen([cmd_line], shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
                # 再次读取这个getprop.txt文件，如果还是小于10行，那么这次的测试直接报错结束，不再往后生成报告
                # 有一个延迟
                time.sleep(3)
                with open(self.logFolder + "/getprop.txt", "r") as __prop_obj:
                    props = __prop_obj.readlines()
                    print ("len props:", len(props))
                    if len(props) < 10:  # props 应该不会小于10行
                        raise IOError("cannot get the right getprop.txt")
        except Exception as e:
            print(e)
            return None
        props_dict = {}
        for __line in props:
            __line = __line.strip()
            __line = __line.replace("[", "").replace("]", "").split(": ")
            if len(__line) == 2:
                props_dict[__line[0]] = __line[1]
            else:
                pass
        return props_dict

    def getprop(self, prop):
        try:
            return self.__prop[prop]
        except:
            print("e" * 10 + "  prop:%s not exists  " % prop + "e" * 10)
            return ""

    def get_tv_model(self):
        """
        读取tv的机型信息
        根据 @刘佳子 提供信息，获取机型字段按照以下优先级获取
        ro.boot.model_name > tv.model_name > ro.boot.platform_name > ro.product.model > ro.boot.assm_mn
        """
        if self.getprop("ro.boot.model_name"):
            return self.getprop("ro.boot.model_name")
        elif self.getprop("tv.model_name"):
            return self.getprop("tv.model_name")
        elif self.getprop("ro.boot.platform_name"):
            return self.getprop("ro.boot.platform_name")
        elif self.getprop("ro.product.model"):
            return self.getprop("ro.product.model")
        else:
            if self.getprop("ro.boot.assm_mn") and len(self.getprop("ro.boot.assm_mn")[:4]) >= 4:
                return self.getprop("ro.boot.assm_mn")[:4]

    def get_version(self):
        if len(self.getprop("ro.build.software.version").split(".")) > 3:  #海外
            new_version = self.getprop("ro.build.software.version")
        elif len(self.getprop("ro.build.version.incremental").split(".")) > 1: # koki
            new_version = self.getprop("ro.build.version.incremental")
        elif len(self.getprop("ro.build.display.id").split(".")) > 3:  #国内
            new_version = self.getprop("ro.build.display.id")
        else:
            new_version = "无法获取到设备的正确版本号"
            # raise ValueError("无法获取到设备的正确版本号。")
        return new_version

    def get_tv_product_model(self):
        product_model = self.getprop("ro.product.model")
        if product_model and product_model.startswith("MiTV-"):
            if product_model[5] == "A":
                return "Amlogic"
            elif product_model[5] == "M":
                return "MTK"
        else:
            print("produce name:{},unknown TV Model Name".format(product_model))
            return "Unknown"

class HTML_Pictures():     # 只会在monkey测试会画图
    def __init__(self,testtype="monkey"):
        self.mem_template = os.path.join(work_dir,"htmlTemplate/cpu-memory-template/mem.html")
        self.cpu_template = os.path.join(work_dir,"htmlTemplate/cpu-memory-template/cpu.html")
        self.fds = Fds()
        self.testtype = testtype

    def run(self,folder,android_version):
        for file in os.listdir(folder):
            if file.endswith("mem.csv"):   # 内存
                # 解析mem.csv
                apk_name, time_content, data_content = self.parse_memcsv(folder,file)
                apk_name = file.split("/")[-1].replace(".csv","")
                html_file = self.inset_html_data(self.mem_template,folder,apk_name,time_content,data_content)
                # html_file是路径
                src_dir, des_dir = html_file, html_file
                url_list = des_dir.split("/")
                for i in url_list:
                    if "_testResult_" in i:
                        start_index = url_list.index(i)
                        des_dir = "/".join(url_list[start_index:])
                        break
                try:
                    self.fds.upload_to_fds(src_dir, des_dir, "mitv-autotest/{}".format(self.testtype))  # 本地文件，fds上保存路径
                except GalaxyFDSClientException as e:
                    print(e)
                except IOError as e:
                    print(e)

            elif file.endswith("cpu.csv"):  # cpu
                # 解析cpu.csv
                apk_name, time_content, data_content = self.parse_cpucsv(android_version=android_version, folder=folder,csvfile=file)
                apk_name = file.split("/")[-1].replace(".csv", "")
                html_file = self.inset_html_data(self.cpu_template,folder,apk_name,time_content,data_content)
                src_dir, des_dir = html_file, html_file
                url_list = des_dir.split("/")
                for i in url_list:
                    if "_testResult_" in i:
                        start_index = url_list.index(i)
                        des_dir = "/".join(url_list[start_index:])
                        break
                try:
                    self.fds.upload_to_fds(src_dir, des_dir, "mitv-autotest/{}".format(self.testtype))  # 本地文件，fds上保存路径
                except GalaxyFDSClientException as e:
                    print(e)
                except IOError as e:
                    print(e)

        self.Gen_png(folder,upload2fds=True)

    def apps_switch(self,result_folder,android_version):
        save_folder = os.path.join(result_folder,"mem_cpu_files")   # 最后的大图保存的位置
        if not os.path.exists(save_folder):
            os.mkdir(save_folder)
        meminfo_dict = {}
        cpuinfo_dict = {}
        caseLogList = []
        if os.path.exists(os.path.join(result_folder, "parse.list")):
            parselist = open(os.path.join(result_folder, "parse.list"), "r")
            for line in parselist.readlines():
                line = line.strip()
                caseLogList.append(line)
        print("caseLogList:",caseLogList)

        for casefolder in caseLogList:
            folder = os.path.join(result_folder,casefolder)
            if not os.path.exists(os.path.join(folder,"mem_cpu_files")):  #
                continue
            for file in os.listdir(os.path.join(folder,"mem_cpu_files")):
                if file.endswith(".mem.csv"):   # 内存
                    apk_name, time_content, data_content = self.parse_memcsv(os.path.join(folder,"mem_cpu_files"),file)
                    apk_name = file.split("/")[-1].replace(".csv","")
                    if apk_name not in meminfo_dict.keys():
                        meminfo_dict[apk_name] = {"time_content":'[',"data_content":"["}
                    meminfo_dict[apk_name]["time_content"] += time_content[1:-1]
                    meminfo_dict[apk_name]["data_content"] += data_content[1:-1]
                if file.endswith(".cpu.csv"):  # cpu
                    # 解析cpu.csv
                    apk_name, time_content, data_content = self.parse_cpucsv(android_version=android_version, folder=os.path.join(folder,"mem_cpu_files"),csvfile=file)
                    apk_name = file.split("/")[-1].replace(".csv", "")
                    if apk_name not in cpuinfo_dict.keys():
                        cpuinfo_dict[apk_name] = {"time_content":'[',"data_content":"["}
                    cpuinfo_dict[apk_name]["time_content"] += time_content[1:-1]
                    cpuinfo_dict[apk_name]["data_content"] += data_content[1:-1]

        for apk_name in meminfo_dict.keys():
            time_content = meminfo_dict[apk_name]["time_content"] + "]"
            data_content = meminfo_dict[apk_name]["data_content"] + "]"
            self.inset_html_data(self.mem_template,save_folder,apk_name,time_content,data_content)

        for apk_name in cpuinfo_dict.keys():
            time_content = cpuinfo_dict[apk_name]["time_content"] + "]"
            data_content = cpuinfo_dict[apk_name]["data_content"] + "]"
            self.inset_html_data(self.cpu_template,save_folder,apk_name,time_content,data_content)
        self.Gen_png(save_folder,upload2fds=True)

    def inset_html_data(self,template,folder,apk_name, time_content, data_content):
        """
        生成对应的html文件
        :param template: html模板，有cpu和mem两种
        :param folder:
        :param csvfile:
        :param apk_name: 包名
        :param time_content: 时间戳
        :param data_content: 数据
        :return:
        """
        html_file = os.path.join(folder, apk_name + ".html")
        shutil.copy(template,html_file)
        # insert data to html
        html_mem = open(html_file, "r", errors='ignore')
        html_content = html_mem.read()
        html_mem.close()

        position = html_content.find("categories: ")
        if position != -1:
            html_content = html_content[:position + 12] + time_content + html_content[position + 12:]
            html_mem = open(html_file, "w")
            html_mem.write(html_content)
            html_mem.close()

        position1 = html_content.find("name: ")
        if position1 != -1:
            html_content = html_content[:position1 + 6] + "'" + apk_name + "'" + html_content[position1 + 6:]
            html_mem = open(html_file, "w")
            html_mem.write(html_content)
            html_mem.close()

        position2 = html_content.find("data: ")
        if position2 != -1:
            html_content = html_content[:position2 + 6] + data_content + html_content[position2 + 6:]
            html_mem = open(html_file, "w")
            html_mem.write(html_content)
            html_mem.close()
        return html_file

    def parse_memcsv(self,folder, csvfile):
        """
        解析mem.csv文件
        文件内容格式：2023-07-26-20:08:25 1643 1252504K 64380K 11122K 9364K 19068K 7710K 7448K 1752K xiaomi.tvservice
        :param folder: 文件夹路径
        :param csvfile: CSV文件名
        :return:
        """
        try:
            memlog = open(os.path.join(folder, csvfile), "r")
            lines = memlog.readlines()
        except IOError as e:
            lines = ""
        time_data = "["
        pss_data = "["
        apk_name = ""
        for line in lines:
            # print(line)
            if line.find(" ") == -1:
                continue
            timestamp = line.split(" ")[0]
            pss_value = line.split(" ")[4].strip('K')
            apk_name = line.split(" ")[-1]
            if timestamp.strip == "":
                continue
            # print timestamp
            time_data += "'{}',".format(timestamp.strip())
            pss_data += "{},".format(pss_value.strip())
        time_data += "]"
        pss_data += "]"
        apk_name = apk_name.strip()
        return apk_name, time_data, pss_data

    def parse_cpucsv(self,android_version, folder, csvfile):
        """
        解析cpu文件
        2023-07-26-20:06:03 590 system 18 -2 2.0G 208M 111M S 35.1 7.3 41:54.42 system_server
        :param android_version: 安卓版本
        :param folder:
        :param csvfile:
        :return:
        """
        # android_version = self.getprop("ro.build.version.release")  # todo
        # 安卓版本目前是有5.1.1，6.0.1，9，10，11
        android_version = android_version.split('.')[0]  # 只取第一位
        if int(android_version) == 5 or int(android_version) == 6:
            cpuindex = 3  #
        else:
            cpuindex = 9

        try:
            cpulog = open(os.path.join(folder, csvfile), "r", errors='ignore')
            lines = cpulog.readlines()
        except IOError as e:
            lines = ""

        time_cpu = "["
        cpu_data = "["
        apk_name = ""
        for line in lines:
            if line.find(" ") == -1:
                continue
            timestamp = line.split(" ")[0]
            if timestamp.strip == "":
                continue
            # print timestamp
            time_cpu += "'{}',".format(timestamp.strip())

            try:
                cpu_value = line.split(" ")[cpuindex].strip('%')
            except IndexError:
                continue

            if cpu_value.strip() == "":
                continue
            if re.findall('[a-zA-Z]{1,}', cpu_value.strip()):
                continue
            cpu_data += "{},".format(cpu_value.strip())

            apk_name = line.split(" ")[-1]

        time_cpu += ']'
        cpu_data += "]"
        apk_name = apk_name.strip()
        return apk_name, time_cpu, cpu_data

    def Gen_png(self,folder,upload2fds=None):
        """
        根据html文件生成png文件,上传到fds
        :param folder:
        :param html_url:
        :param upload2fds: 是否上传fds，否：False，否则填fds的路径
        :return:
        """
        print("make png......")
        print("folder:",folder)
        for html_file in os.listdir(folder):
            if html_file.endswith(".html"):
                html_dir = os.path.join(folder,html_file)
                father_path = "file://" + os.path.join(os.path.dirname(os.path.realpath(html_dir)),html_file)
                pic_dir = os.path.join(folder,html_file.replace("html",'png'))
                os.system(
                    "xvfb-run --auto-servernum --server-args=\"-screen 0, 1920x1080x24\" cutycapt --url=" + father_path + " --min-width=800 --min-height=400 --delay=5000 --out=" + pic_dir)
                time.sleep(3)
                print("make " + pic_dir + " OK.")
                if upload2fds:
                    try:
                        des_dir = pic_dir
                        url_list = des_dir.split("/")
                        for i in url_list:
                            if "_testResult_" in i:
                                start_index = url_list.index(i)
                                des_dir = "/".join(url_list[start_index:])
                                break
                        # self.fds.upload_to_fds(pic_dir, des_dir, "monkeytest")   #本地文件，fds上保存路径
                        self.fds.upload_to_fds(pic_dir, des_dir, "mitv-autotest/{}".format(self.testtype))   #本地文件，fds上保存路径
                    except GalaxyFDSClientException as e:
                        print(e)
                    except IOError as e:
                        print(e)

class Component():
    """
    根据jira上MITVAUTO项目的模块读取对应的模块名字以及对应的包名、项目名，
    使用两个dict映射匹配唯一的模块名以及对应的负责人
    dict1：cpms >> key:模块名, value: [pkg1,pkg2,...]
    dict2:assigne_owner >> key:模块名, value:{project_name1:owner1, project_name2:owner2,}    #嵌套的dict
    """
    def __init__(self,mitvauto_project):
        self.mitvauto = mitvauto_project
        self.cmps = {}
        self.assigne_owner = {}
        self.get_cmps()    # 执行了之后才有component的信息

    def get_cmps(self):
        """
        读取jira上MITVAUTO的模块信息
        :return:
        """
        for m_i in self.mitvauto.raw["components"]:
            cmp_name = m_i["name"]
            try:
                description = m_i['description']     # 读取description
                if "];[" in description:
                    pkgs = description.split("];[")[0][1:]
                    owners = description.split("];[")[1][:-1]
                else:
                    pkgs = description
                    owners = None
                # todo 解析pkgs
                pkgs_list = list(filter(lambda x: len(x) > 2, pkgs.split(";")))  # list
                self.cmps[cmp_name] = pkgs_list
                # todo 解析owners
                if owners:
                    onwers_list = list(filter(lambda x: len(x) > 2, owners.split(";")))
                    owners_dict = {}
                    for p_o in onwers_list:
                        project_name, owner = p_o.split(":")[0], p_o.split(":")[1]
                        owners_dict[project_name] = owner
                    self.assigne_owner[cmp_name] = owners_dict
            except:
                pass

        # top30的第三方应用
        self.top30apps = ["com.ktcp.video","com.xiaodianshi.tv.yst","com.gitvdemo.video","com.cibn.tv","com.ixigua.android.tv.wasu",
                      "com.dianshijia.newlive","com.gitv.dvb.live","com.hunantv.license","com.kwai.tv.yst","com.tencent.qqmusictv",
                      "com.hpplay.happyplay.aw","com.lutongnet.nldmxXiaomiChildren","com.ds.launcher","cn.miguvideo.migutv",
                      "com.xiaojie.tv","com.huya.nftv","com.xiaojing.tv","com.baidu.netdisk.tv","com.moretv.android","com.netease.cloudmusic.tv",
                      "com.kugou.android.tv","com.newtv.cboxtv","com.xiaomi.mitvshow","com.douyu.xl.douyutv","cn.wps.moffice_i18n_TV",
                      "com.changba.sd","cn.jj.tv","com.lutongnet.nldmx","com.dangbei.dbmusic","com.ysgctv.vip","com.cmgame.gamehalltv","com.tencent.karaoketv","com.hongen.app.word.ott"]
        jira_appstore_backup = "air.com.gongfubb.wksz.tv;com.baosheng.ktv;com.changba.sd;com.cibn.tv;com.fittime.tv.m;com.gitvdemo.video;com.hunantv.license;com.ktcp.csvideo;com.ktcp.video;com.ktcp.video:daemon;com.ktcp.video:push;com.ktcp.video:upgrade;com.ktcp.video:webview;;com.lutongnet.nldmx;com.lutongnet.nldmx:P00;com.lutongnet.ott.health;com.moretv.android;com.pptv.tvsports;com.xiaodianshi.tv.yst;com.xiaodianshi.tv.yst:stats;.multiscreen;cn.miguvideo.migutv;com.tvrun.run;"

        # 不上报的包
        # autotest
        autotest = ['com.xiaomi.saber', 'com.xiaomi.mitvcase', 'app_process', 'tv.panda.test.monkey', 'grep',
                    '/system/bin/adbd', "top", "ps", 'com.xiaomi.tvtes', "com.xy51.mwsg.mitv", "media.codec",
                    "com.github.uiautomator", "/data/local/tmp/iopp"]
        self.cmps["autotest"] = autotest
        # ignore packages
        ignore_packages = ["com.xiaomi.mibox.gamecenter","com.ktcp.csvideo","com.lutongnet.nldmx","com.lutongnet.nldmx:P00","com.xiaomi.tvqs","busybox"]
        if "ignore_packages" in self.cmps.keys():
            ignore_packages += self.cmps["ignore_packages"]
        if os.path.exists("tools/ignore_packages"):
            with open("tools/ignore_packages", "r") as __file:
                __ignore_packages = __file.readlines()
            for __package in __ignore_packages:
                ignore_packages.append(__package.strip())
        elif os.path.exists("ignore_packages"):
            with open("ignore_packages", "r") as __file:
                __ignore_packages = __file.readlines()
            for __package in __ignore_packages:
                ignore_packages.append(__package.strip())
        else:
            print("not found ignore_packages")

        self.cmps["ignore_packages"] = ignore_packages
        self.cmps["APP-第三方应用"] += self.top30apps

        print("components:",self.cmps.keys())
        # print(self.assigne_owner)

    def get_component(self,package):
        """
        分配一个模块名,在分配之前先执行过滤的规则
        :param package: 包名
        :return: component 模块名
        """
        # if package in self.cmps["autotest"]:     # 先走过滤的逻辑，如果过滤的包和上报的包有交集，会按照过滤处理
        #     return "autotest",package
        # elif package in self.cmps["ignore_packages"]:     # 过滤的包没有做模糊匹配处理
        #     return "ignore_packages",package
        # elif 'com.google.android' in package:    # 过滤掉Google的应用
        #     return 'GTVS',package
        package = package.split(":")[0]

        if 'google' in package or "com.android.vending" in package:  # Google的应用(不过滤了)
            return 'BSP-GTVS', package
        
        for pkg in self.top30apps:
            if pkg in package:
                return "APP-第三方应用",pkg
        
        for pkg in self.cmps["autotest"]:
            if pkg == package:
                return "autotest", pkg

        for pkg in self.cmps["ignore_packages"]:
            if pkg == package:
                return "ignore_packages", pkg

        if "video_" in package:   # 小米播放器有关的异常
            return "BSP-小米播放器-MTK",package

        if "audio_" in package:
            return "BSP-小米播放器-audio",package

        component = "BSP-default"  # 如果匹配不上，则分配为默认模块
        app_name = "BSP-default"   # 对应的包名
        for named, pkgs in self.cmps.items():
            # if package in pkg:
            for pkg in pkgs:  # 反过来匹配，模糊匹配，解析出来的包名可能有冗余
                if pkg in package:
                    component = named   # 匹配模块
                    app_name = pkg   # 对应不带冗余的包名
        if component == "BSP-default":     # 如果匹配不到模块，则分配给BSP-稳定性，后续get_assignee走单给项目负责人
            print("can't assign a component to this package,assign to BSP-稳定性")
            # component = "BSP-稳定性"    # 这个时候return的app_name是BSP-default
            app_name = package    # 匹配不到，则指定的component为BSP-default，app_name为解析出来的包名
        print("appname",app_name)
        return component,app_name

    def get_assignee(self,component,project_name):
        """
        对于需要指派负责人的模块，分配一个负责人
        :param component: 模块名
        :param project_name: 项目名
        :return: assignee 或 无返回值
        """
        if component in self.assigne_owner.keys():
            # assignee = "xudongmei1"  # 默认分配给徐冬梅    # 如果没匹配上，应该assign给该模块的负责人
            owners_dict = self.assigne_owner[component]
            for named, owner in owners_dict.items():
                named = "/" + named + "/"
                if "/" + project_name + "/" in named:  # 完全匹配，不同项目分支对应负责人可能会不同
                    assignee = owner
                    return assignee
        return None   # 其他的不指定issue负责人

def is_uiautomator(issuefile):
    try:
        lines = open(issuefile).readlines()
    except:
        try:
            decode_logcat(issuefile)
            lines = open(issuefile).readlines()
        except:
            return False  # 如果还是解析不了就不解析了
    for line in lines:
        if "UiAutomation" in line:
            return True
    else:
        return False

def decode_logcat(file):
    """
    log解码成utf8格式
    解码后的logcat file路径记为logcat_file
    :param logcat_path:
    :return:logcat_file
    可解码任何文件（包括tombstone）
    """
    allline = []
    with open(file, "rb") as log:
        for index, line in enumerate(log):
            line = line.decode('utf-8', 'ignore')
            allline.append(line)
    with open(file, "w") as p:
        p.write("".join(allline))
    return file

def get_package(issuefile):
    print("issuefile:",issuefile)
    try:
        lines = open(issuefile).readlines()
    except:
        try:
            decode_logcat(issuefile)
            lines = open(issuefile).readlines()
        except:
            return
    for line in lines:
        match = re.findall(".*Component:(.*).*", line.strip().replace('\r', ''))
        for x in match:
            package = match[0]
            return package

def is_TOP_pkg(issuefile):
    """
    judge FC issue 是否是前台应用被杀
    :return:
    """
    try:
        lines = open(issuefile).readlines()
    except:
        try:
            decode_logcat(issuefile)
            lines = open(issuefile).readlines()
        except:
            return False  # 如果还是解析不了就不解析了
    for line in lines:
        if "has died: TOP" in line:
            return True
    else:
        return False

def get_app_ch_name(pkgname):
    """
    获取用例中文名称，后续写入resultdata["appname"]
    :return:
    """
    pkgname = pkgname.strip()  # 去除头尾的空格/换行符
    print("get_app_Chinese name:{}".format(pkgname))

    if pkgname == "com.mitv.tvhome":
        return "桌面"

    elif pkgname == "com.xiaomi.mitv.appstore":
        return "应用商城"

    elif pkgname == "com.xiaomi.mibox.gamecenter":
        return "游戏中心"

    elif pkgname == "com.xiaomi.tweather":
        return "天气"

    elif pkgname == "com.xiaomi.mitv.settings":
        return "设置"

    elif pkgname == "com.xiaomi.mitv.shop":
        return "电视商城"

    elif pkgname == "com.xiaomi.mitv.tvmanager":
        return "电视管家"

    elif pkgname == "com.xiaomi.mitv.calendar":
        return "日历"

    elif pkgname == "com.xiaomi.mitv.smartshare":
        return "无线投屏"

    elif pkgname == "com.xiaomi.mitv.systemui":
        return "systemui"

    elif pkgname == "com.mitv.gallery":
        return "相册"

    elif pkgname == "com.xiaomi.mitv.shop.mihome":
        return "米家"

    elif pkgname == "com.xiaomi.mitv.handbook":
        return "用户手册"

    elif pkgname == "com.duokan.videodaily":
        return "视频头条"

    elif pkgname == "com.xiaomi.mitv.tvvideocall":
        return "视频通话"

    elif pkgname == "com.mitv.alarmcenter":
        return "定时提醒"

    elif pkgname == 'com.xiaomi.smarthome.tv':
        return '智能助手'

    elif pkgname == 'com.xiaomi.tv.gallery':
        return "时尚画报"

    elif pkgname == 'com.xiaomi.voicecontrol':
        return '调节音量'

    elif pkgname == 'com.ktcp.video':
        return '云视听极光'

    elif pkgname == "com.xiaodianshi.tv.yst":
        return '云视听小电视'

    elif pkgname == "com.gitvdemo.video":
        return "银河奇异果"

    elif pkgname == 'com.cibn.tv':
        return "CIBN酷喵"

    elif pkgname == 'com.ixigua.android.tv.wasu':
        return "华数鲜时光"

    elif pkgname == "com.tencent.qqmusictv":
        return "QQ音乐"

    elif pkgname == 'com.dianshijia.newlive':
        return "电视家 3.0"

    elif pkgname == "com.hunantv.license":
        return '芒果TV'

    elif pkgname == "com.kugou.android.tv":
        return "酷狗音乐"

    elif pkgname == 'com.huya.nftv':
        return "云视听虎电竞"

    elif pkgname == "com.kwai.tv.yst":
        return '云视听快TV'

    elif pkgname == "com.moretv.android":
        return '云视听电视猫'

    elif pkgname == "com.changba.sd":
        return "唱吧"

    elif pkgname == 'com.ktcp.csvideo':
        return "NewTV极光"

    elif pkgname == "com.newtv.cboxtv":
        return "cctv新视听"

    elif pkgname == "com.xiaomi.wakeupservice":
        return "小爱远场唤醒"

    elif pkgname == "com.xiaomi.mitv.upgrade":
        return "电视系统更新"

    elif pkgname == "com.xiaomi.mitv.tvmanager:centralservice":
        return "电视管家（centralservice）"

    elif pkgname == "com.xiaomi.mitv.remotecontroller.service":
        return "电视遥控服务"

    elif pkgname == "com.xiaomi.tvqs":
        return "TVQS"

    elif pkgname == "com.android.bluetooth":
        return "蓝牙"

    else:  # 如果找不到对应的中文名称，那就还是用pkgname，海外电视的apk都没有中文名
        return pkgname

def add_log(logname,writeline,output = False):
    if output == True:
        # print(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'),logname,writeline)
        print(logname,writeline)
    log_file = open(logname,"a+",encoding='utf-8')
    log_file.write(writeline)
    log_file.write("\n")
    log_file.close()

def get_testchname(filepath,testType):
    device_type = "整机"
    network_ = "有线"
    if os.path.isfile(filepath):   # 以文件中的为准
        with open(filepath, "r") as test_info_file:
            test_info = test_info_file.readlines()
            testType = test_info[0].strip()
            testtype_chname = test_info[1].strip()
            # todo 根据第三行查看是否是主板还是整机
            try:
                device_type = test_info[2].strip()
            except Exception as e:pass     # 如果没写入直接不管，默认为整机压测
            # todo 根据第四行判断是有线网络还是WiFi
            try:
                network_ = test_info[3].split("network:")[1].strip()
            except Exception as e:pass   # 如果没写入，默认为使用有线网络压测
    else:
        if testType == "monkey":
            testtype_chname = "monkey测试"
        elif testType == "apps-switch":
            testtype_chname = "全局monkey测试"
        elif testType == "MIUITVmonkey":
            testtype_chname = "MIUITV monkey测试"
        elif testType == "Top22APPmonkey":
            testtype_chname = "Top22三方应用 monkey测试"
        elif testType == "stability":
            testtype_chname = "稳定性测试"
        elif testType == "stability-overseas":
            testtype_chname = "海外版稳定性测试"
        elif testType == "miplayer":
            testtype_chname = "小米播放器压测"
        elif testType == "HDMI-stability":
            testtype_chname = "信号源稳定性测试"
        elif testType == "Display-stability":
            testtype_chname = "Display稳定性测试"
        elif testType == "photonengine":
            testtype_chname = "光子引擎测试"
        else:
            testtype_chname = "自动化测试"
    print("testType:",testType)
    print("testtype_chname:",testtype_chname)
    print("整机or主板：",device_type)
    return testType,testtype_chname,device_type,network_

if __name__ == '__main__':

    # login = Login_Jira()
    # jira = login.login_jira()  # 登录jira
    jira = Jira().login_jira()
    """
    mitvauto = jira.project("MITVAUTO")
    #
    cmp = Component(mitvauto)
    # cmp.get_cmps()
    print(cmp.cmps['APP-第三方应用'])
    package = "audio_pa"
    product = "maverick"
    __component, __app_name = cmp.get_component(package)  # 分配一个模块，一定会有返回值，如果匹配不到，会返回BSP-default模块
    assignee = cmp.get_assignee(__component, product)
    print("__component:",__component)
    print("__app_name:",__app_name)
    print(assignee)
    pass
    """
    # bug_info = jira_db.find_one({'bugid': "MITVAUTO-8526"})
    # print(bug_info)
    issue = jira.issue("MITVAUTO-10775")
    # i_s = str(issue.fields.status)
    i_s = issue.fields.status.raw['name']
    print(i_s)
    print(11111)
    # jira.transition_issue(issue, '701')

    process = jira.transitions("MITVAUTO-10775")
    aaa = "关闭问题"
    for pro in process:
        p_name = pro['name']
        p_id = str(pro['id'])
        print(p_name,p_id)
        if p_name == aaa:
            print(p_id)
            break


    # print(type(i_s))
    # if i_s == "已关闭":
    #     print("close")
    # elif i_s == "已解决":
    #     print("resolve")
    #     jira.transition_issue(issue,'2')
    #     # issue.close()
    # else:
    #     print("open")
"""
旧版读取模块的方法
def get_component(package,product,android_version):  #
    '''
    issue_dict的模块component字段
    :param package: issuefile component
    :param product: ro.build.product
    :param android_version: ro.build.version.release
    :return:
    '''
    # package = self.get_package().split(":")[0]
    print("get component package:", package)
    if package in autotest:
        return "autotest"
    elif package in ignore_packages:
        return "appstore_packages"
    elif 'com.google.android' in package:
        return 'GTVS'
    elif package in external_app:
        return "APP-第三方应用"

    # 在此适配各个项目中component的名称不同出现的问题
    # product = self.getprop("ro.build.product")
    # print("project is %s" % product)
    # print("*" * 60)
    # android_version = self.getprop("ro.build.version.release")


    if product == 'pulpfiction':
        for named, pkg in cmps_pulpfiction.items():
            if package in pkg:
                return named
        return 'Framework'

    if product == 'matrix':
        for named, pkg in cmps_matrix.items():
            if package in pkg:
                return named
        return 'Framework'

    if product == 'xmen':
        for named, pkg in cmps_xmen.items():
            if package in pkg:
                return named
        return 'Framework'

    if product in ['vforvendetta', "vforvendettas"]:
        for named, pkg in cmps_vforvendetta.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    # if product == 'dangal' and android_version == "8.1.0":
    if product in ['dangal', "dangalFHD", "dangalUHD"] and "8" in android_version:
        for named, pkg in cmps_dangal.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    # if product == 'dangal' and android_version == "9":
    if product in ['dangal', "dangalFHD", "dangalUHD"] and "9" in android_version:
        for named, pkg in cmps_dangalP.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    if product in ['dangal', "dangalFHD", "dangalUHD"] and "11" in android_version:
        for named, pkg in cmps_dangalR.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    if product == 'oneday':
        for named, pkg in cmps_oneday.items():
            if package in pkg:
                return named
        return 'Third-party Apps'

    if product == 'zorro':
        for named, pkg in cmps_zorro.items():
            if package in pkg:
                return named
        return 'OS-FileSystem'

    if product == 'once':
        for named, pkg in cmps_once.items():
            if package in pkg:
                return named
        return 'GTVS'

    if product in ['machuca', 'volver','amelie','nino'] and android_version == "11":
        for named, pkg in cmps_machuca_r.items():
            if package in pkg:
                return named
        return 'OS-系统底层功能/稳定性'

    if str(product).find('amelie') != -1 or str(product).find('nino') != -1:
        for named, pkg in cmps_amelie.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    if product in ['machuca', 'volver'] and android_version == "9":
        for named, pkg in cmps_machuca.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    if str(product).find('waterlooBridge') != -1:
        for named, pkg in cmps_waterloobridge.items():
            if package in pkg:
                return named
        return 'OS-SYSTEM'

    if str(product).find('magnolia') != -1:
        for named, pkg in cmps_MagnoliaP.items():
            if package in pkg:
                return named
        return 'OS-稳定性'

    if str(product).find('jobs') != -1:
        for named, pkg in cmps_Jobs.items():
            if package in pkg:
                return named
        return 'OS-boot'

    if str(product).find('furyroad') != -1:
        print("*" * 60)
        print(product)
        print("*" * 60)
        for named, pkg in cmps_Furyroad.items():
            if package in pkg:
                return named
        return 'OS-boot'

    if str(product).find('starwars') != -1:
        for named, pkg in cmps_starwars.items():
            if package in pkg:
                return named
        return 'BootLoader和Kernel'

    if str(product).find('aquaman') != -1:
        for named, pkg in cmps_aquaman.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product).find('alita') != -1:
        for named, pkg in cmps_alita.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('dofus') != -1:
        for named, pkg in cmps_dofus.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('kingarthur') != -1:
        for named, pkg in cmps_kingarthur.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('hugo') != -1:
        for named, pkg in cmps_hugo.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('transformers') != -1:
        for named, pkg in cmps_transformers.items():
            if package in pkg:
                return named
        return 'OS-Boot'

    if str(product).find('frozen') != -1:
        for named, pkg in cmps_frozen.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('walle') != -1:
        for named, pkg in cmps_walle.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('tschick') != -1:
        for named, pkg in cmps_tschick.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('kingkung') != -1:
        for named, pkg in cmps_kingkung.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('mulan') != -1:
        for named, pkg in cmps_mulan.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find('baymax') != -1:
        for named, pkg in cmps_baymax.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if product == 'inception':
        return "android-system-app"

    if str(product) in ["croods","croods_tarzan","croods_sa","tarzan"]:
        for named, pkg in cmps_Croods.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product) in ["martian","martian_eu"]:
        for named, pkg in cmps_martian.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product) in ["hermano","hermano_sa","hermano_eu"]:
        for named, pkg in cmps_Hermano.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product).find('rango') != -1:
        for named, pkg in cmps_Rango.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product).find('soul') != -1:
        for named, pkg in cmps_soul.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product).find('jaws') != -1:
        for named, pkg in cmps_jaws.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    # if str(product).find("venom") != -1:
    if str(product) in ["venom","venom_us","venom_sa","flipped","flipped_sa"]:
        for named, pkg in cmps_Venom.items():
            if package in pkg:
                return named
        return 'BSP-Stability'

    if str(product).find('freeguy') != -1:
        for named, pkg in cmps_freeguy.items():
            if package in pkg:
                return named
        return 'BSP-稳定性'

    if str(product).find("irobot") != -1:
        for named, pkg in cmps_irobot.items():
            if package in pkg:
                return named
        return 'BSP-Stability'
"""