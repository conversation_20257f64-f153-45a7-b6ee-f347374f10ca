import os
import sys


def add_log_(logname,writeline):
    log_file = open(logname,"a+",encoding='utf-8')
    log_file.write(writeline)
    log_file.write("\n")
    log_file.close()

def run_parselist(addr):
    parselist = os.path.join(addr,"parse.list")
    for s in os.listdir(addr):
        if os.path.isdir(os.path.join(addr,s)):
            print(s)
            add_log_(parselist,s)



if __name__ == '__main__':
    addr = sys.argv[1]
    print("addr:",addr)
    run_parselist(addr)

# python3.8 make_parselist.py "${DEVICEID}" $result_folder
# python3.8 mitv_autotest_parse/Reporter.py "${DEVICEID}" $result_folder $TESTTYPE $BUILD_URL $BUILD_USER $Address