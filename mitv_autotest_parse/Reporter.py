#! /usr/bin/env python
# -*- coding:UTF-8 -*-
from __future__ import division
import json
import os

from fds.model.fds_object_metadata import FDSObjectMetadata
import linecache
import copy
from sys import argv
try:
    from common_method import *
except Exception as e:
    from .common_method import *

import urllib3
urllib3.disable_warnings()
from django.template.loader import get_template
from django.template import Context
from mfile.upload_to_fds import *
from feishu.feishu import FeiShu
from mjira.tv_jira import Jira

fds_filestore = "https://cloud.d.xiaomi.net/#/product/file-store/objects/mitv-autotest/"
# mongo_collection = DevAppDataCollection()
# jira_db = mongo_collection._collection(collection_name="jira_db",db_name="autotest")

print("+++++++++++++++pymongo+++++++++++++++++")

class Generate():
    def __init__(self,deviceID,logFolder,testType,build_url = "",build_user="unknown",address="unknown address"):
        self.id = deviceID
        self.logFolder = logFolder
        self.testType, self.testtype_chname,self.device_type,self.network_ = get_testchname(os.path.join(self.logFolder, "test_info"),testType)
        self.address = address
        self.build_url = build_url
        self.build_user = build_user
        self.feishu_user = ['heyingmei']
        self.feishu = FeiShu()
        self.html_pic = HTML_Pictures(self.testType)

    def generating(self):
        self.prop = Prop(self.logFolder, self.id)
        self.getprop = self.prop.getprop      # 电视的基本信息
        self.android_version = self.getprop("ro.build.version.release")
        self.resultSheet = []    # case detail
        self.exetime_sum = 0
        self.error_count = 0  # 如果有提交bug失败的话，可以从这个项体现出来
        self.mtbf_error = 0  # 参与mtbf计算的error次数
        self.summarySheet = {'notrun': 0,"error":0, 'fail': 0, 'pass': 0, 'exed': 0, 'tb': 0, 'nc': 0, 'anr': 0, 'fc': 0,
                             'reboot': 0, 'picErr': 0, 'playerErr': 0, 'hdmiErr': 0,"dead_system":0,"wifi":0,"smartshare":0,"speedui":0}   # summary data
        self.renderData = {}

        self.jira = Jira().login_jira()  # 登录jira,有时限，等需要解析上报时再登录
        mitvauto = self.jira.project("MITVAUTO")
        mongo_collection = DevAppDataCollection()
        self.jira_db = mongo_collection._collection(collection_name="jira_db", db_name="autotest")
        print("===============pymongo+++++++++++++++++")
        self.components = Component(mitvauto)
        # todo 电视上已安装的app
        self.installed_3apps=""
        if os.path.isfile(os.path.join(self.logFolder,"record_3apps")):
            self.installed_3apps = open(os.path.join(self.logFolder,"record_3apps")).read().rstrip()
        self.fds = Fds()
        self.jenkinsLink = self.get_jenkinsLink()
        self.issue_pkg = {}     # 根据bug.list中读取jira的pkg，决定趋势图是展开还是不展开
        # if self.testType != "apps-switch":
        self.parseCaseFolder()    # case detail # self.resultSheet apps-switch不需要解析casedetail，否则上报数据太长了
        self.SummaryData()       # summary data  # self.summarySheet （包含bug.list）
        # todo 写到报告的data中
        self.renderData['summaryData'] = self.summarySheet  # report`s summary sheet
        with open(os.path.join(self.logFolder,"summaryReport.json"),"w") as j_file:   # Report的summary
            json.dump(self.summarySheet,j_file,ensure_ascii=False)
        print("check report template by testType:",self.testType)
        # todo apps-switch画大图
        if self.testType == "apps-switch":
            self.t = get_template("apps-switch-template/apps-switch-child.html")
        elif self.testType == "monkey" or self.testType == "MIUITVmonkey" or self.testType == "Top22APPmonkey" or "monkey" in self.testType.lower():
            self.t = get_template("monkey-template/child.html")
        elif self.testType == "stability":
            self.t = get_template("stability-template/child.html")
        elif self.testType == "stability-overseas":
            self.t = get_template("stability-overseas-template/child.html")
        elif self.testType == "smartshare":
            self.t = get_template("smartshare-template/child.html")
        elif self.testType == "speedui":
            self.t = get_template("speedui-template/child.html")
        elif "miplayer" in self.testType or self.testType == "HDMI-stability" or self.testType == "Display-stability" or self.testType == "LiveTV" or self.testType == "photonengine" or self.testType =="connectivity":    # 信号源稳定性测试/display稳定性测试/connectivity稳定性和小米播放器测试共用一套报告template
            self.t = get_template("miplayer-template/child.html")
        else:
            print("unknown testType:{}".format(self.testType))
            # self.feishu.send_text_message("未知测试类型:{}，无法匹配报告模板,请适配后重新解析\n".format(self.testType) +  # 应该是新增了测试项，解析模块没有适配
            #                               "任务地址:{}".format(self.build_url), self.feishu_user)
            self.feishu.send_message_interactive(receiver="heyingmei",
                                                 subject="自动化压测解析通知",
                                                 message_items={"解析任务地址": self.build_url,
                                                                "备注": "未知测试类型:{}，无法匹配报告模板,请适配后重新解析\n".format(self.testType)},
                                                 subject_background_color="blue")

        self.renderData['test_entries'] = self.resultSheet   # report`s result sheet
        if self.testType != "apps-switch":
            self.save_resultData()   # 云平台上报的case详情数据

        # todo 把大图画到report.html中

        self.writeToFile()   # report.html
        self.gen_jiralink()  # 更新jira描述
        # todo 打印report跳转link
        folderlist = self.logFolder.split("/")
        print("report link:{}".format(self.jenkinsLink + folderlist[-1] + "/report.html"))

    def save_resultData(self,):
        """保存case详情数据到json文件中用于上报"""
        result_dict = {}
        for case in self.resultSheet:
            # print("case:{}".format(case))
            casename = case["casefoldername"]
            result_dict[casename] = case

        with open(os.path.join(self.logFolder,"resultData.json"),"w") as j_file:
            json.dump(result_dict,j_file,ensure_ascii=False)

    def SummaryData(self):
        """测试结果总结"""
        self.summarySheet['build_user'] = self.build_user
        self.summarySheet['build_url'] = self.build_url
        self.summarySheet['ver'] = self.getprop("ro.build.version.release")
        self.summarySheet['date'] = self.getprop("ro.build.date")
        self.summarySheet['displayid'] = self.prop.get_version()
        self.summarySheet['model'] = self.prop.get_tv_model()

        self.summarySheet['product'] = self.getprop("ro.build.product")
        # summaryData['product'] = self.getprop("ro.product.name")    # 分支名
        if self.id.count(".") == 3:
            if self.id.endswith(":5555"):
                self.id = self.id.split(":")[0]
            self.summarySheet['serialno'] = ("{}({})".format(self.id, self.address))
        else:
            self.summarySheet['serialno'] = ("{}({})".format(self.getprop("ro.serialno"), self.address))

        self.summarySheet['id'] = self.getprop("ro.build.id")
        self.summarySheet['type'] = self.getprop("ro.build.type")
        self.summarySheet['manufacturer'] = self.getprop("ro.product.manufacturer")
        self.summarySheet['panel'] = self.getprop("ro.boot.mi.panel_size")
        self.summarySheet["case_exetime"] = self.exetime_sum   # 按秒算
        self.exetime_sum = self.exetime_sum / 3600   #转换成小时
        if self.mtbf_error == 0 :   # 如果测试过程中没有发生bug，那么errorcount=0，平均无故障时长等于测试时长
            mtbf = round(self.exetime_sum)   #四舍五入取整
        else:
            mtbf = round(self.exetime_sum / self.mtbf_error)  # 如果测试时长为0，或者正常测试，但是有出现error，那么计算平均无故障时长
        print("平均无故障时长 = 压测总时长 / 电视死机&重启次数 = {} / {} = {}".format(self.exetime_sum,self.mtbf_error,mtbf))
        self.summarySheet['mtbf'] = mtbf
        self.summarySheet['exetime_sum'] = round(self.exetime_sum)   # 执行时间也四舍五入取整
        self.summarySheet['errorcount'] = str(self.error_count)    # 报告中的问题发生次数（暂时按所有问题发生的次数统计，不只是mtbf的问题发生次数）

        parselist = open(os.path.join(self.logFolder, "parse.list"), "r")
        case_amount = len(parselist.readlines())  # 执行总case数

        self.summarySheet["pass_case"] = case_amount - self.summarySheet["error"]
        self.summarySheet["pass_smartshare"] = case_amount - self.summarySheet["fail"] - self.summarySheet["error"]


        # 手机投屏发现率，self.summarySheet["error"] 作为0，self.summarySheet["fail"] 带小数点，self.summarySheet["pass_case"]等于case amount

        print("case amount:",case_amount,self.summarySheet["pass_case"],self.summarySheet["pass_smartshare"])
        self.summarySheet["pass_rate"] = str(round(self.summarySheet["pass_smartshare"] / self.summarySheet["pass_case"],4) * 100)+"%"

        print("投屏成功率 = 投屏成功case数/case执行成功总数 = {}/{}={}".format(self.summarySheet["pass_smartshare"],self.summarySheet["pass_case"],self.summarySheet["pass_rate"]))

        self.parseBugList()    # buglist
        # print("summary sheet :{}".format(self.summarySheet))

    def parseBugList(self):
        """解析bug.list文件，得到报告表头测试结果总结数据"""
        self.jiras_info_list = []  # list里面存dict，一个dict是一条jira信息
        self.bugid_list = {}
        report_bugs_dict = {}  # 上报云平台的jira数据
        bug_template = "http://jira.n.xiaomi.com/browse/issue_key"
        if not os.path.isfile(self.logFolder + "/bug.list") and self.error_count != 0:
            # self.feishu.send_text_message("自动化测试提交jira issue出现错误。\n" +  # 说明有bug，但是提交jira失败了
            #                               "错误原因：Fail to commit bugs to jira...\n" +
            #                               "任务地址:{}".format(self.build_url), self.feishu_user)
            self.feishu.send_message_interactive(receiver="heyingmei",
                                                 subject="自动化压测解析通知",
                                                 message_items={"任务地址": self.build_url,
                                                                "备注": "自动化测试提交jira issue出现错误。"},
                                                 subject_background_color="blue")
        else:
            try:
                buglist = open(self.logFolder + "/bug.list", 'r')  # 如果有buglist文件
                buglistlines = buglist.readlines()
                if len(buglistlines) != self.error_count:  # 如果提交的bug数量与issuefile中统计的数量不相等，说明也有bug提交失败了
                    # self.feishu.send_text_message("自动化测试提交jira issue出现错误。\n" +  # 说明有bug，但是提交jira失败了
                    #                               "错误原因：buglist个数与issuefile统计个数不符\n" +
                    #                               "任务地址:{}".format(self.build_url), self.feishu_user)
                    self.feishu.send_message_interactive(receiver="heyingmei",
                                                         subject="自动化压测解析通知",
                                                         message_items={"任务地址": self.build_url,
                                                                        "备注": "自动化测试提交jira issue出现错误,buglist个数与issuefile统计个数不符"},
                                                         subject_background_color="blue")

                for line in buglistlines:  # 根据bug.list统计本次压测中每个bug发生的次数
                    # print("\ncheck line:", line)
                    bugid = re.findall(".*<a href=\"http://jira.n.xiaomi.com/browse/(.*) \">.*",line.strip().replace('\r', ','))[0]

                    if bugid not in self.bugid_list.keys():
                        self.bugid_list[bugid] = 1
                    else:
                        self.bugid_list[bugid] += 1       # 统计每个jira发生的次数

                for bugid in self.bugid_list.keys():   # upload data to bigfish
                    # summary sheet
                    # 读jira db的bug info
                    bug_info = self.jira_db.find_one({'bugid': bugid})
                    # print("bug info ",bug_info)  bug info  {'_id': ObjectId('64ad21d084a9df52bef38485'), 'appname': 'com.mitv.tvhome', 'type': 'anr', 'project': 'freeguy', 'version': '23.7.9.1554', 'stack': None, 'bugid': 'MITVAUTO-3064', 'count': 25, 'mac': None, 'test_type': 'monkey'}
                    if bug_info:
                        ISSUE_TYPE = bug_info['type']  # todo 更新jira 描述之前去重了
                        print("check ISSUE TYPE",ISSUE_TYPE)
                        if ISSUE_TYPE == 'fc':
                            self.summarySheet['fc'] += self.bugid_list[bugid]    # 加上本次jira发生的次数统计该类型issue的发生次数
                        if ISSUE_TYPE == 'tombstone':
                            self.summarySheet['tb'] += self.bugid_list[bugid]
                        if ISSUE_TYPE == 'anr':
                            self.summarySheet['anr'] += self.bugid_list[bugid]
                        if ISSUE_TYPE == 'reboot_kernel' or ISSUE_TYPE == 'reboot_systemserver' or ISSUE_TYPE == "reboot_hardware" or ISSUE_TYPE == "system_server_hung":
                            self.summarySheet['reboot'] += self.bugid_list[bugid]
                        if ISSUE_TYPE == "hdmi":
                            self.summarySheet["hdmiErr"] += self.bugid_list[bugid]
                        if "video" in ISSUE_TYPE or "audio" in ISSUE_TYPE:
                            self.summarySheet["playerErr"] += self.bugid_list[bugid]
                        if ISSUE_TYPE == "dead_system":
                            self.summarySheet["dead_system"] += self.bugid_list[bugid]
                        if ISSUE_TYPE == "wifi":
                            self.summarySheet["wifi"] += self.bugid_list[bugid]
                        if ISSUE_TYPE == "p2p" or ISSUE_TYPE == "rtsp_mtk" or ISSUE_TYPE == "rtsp_aml":
                            self.summarySheet["smartshare"] += self.bugid_list[bugid]
                        if ISSUE_TYPE == "speedui" or ISSUE_TYPE == "focusmode":
                            self.summarySheet["speedui"] += self.bugid_list[bugid]
                        # 更新issue_pkg
                        self.issue_pkg[bug_info['appname']] = ISSUE_TYPE

                        # 读jira
                        issue = self.jira.issue(bugid)
                        temp_jira_dict = {"bugid": bugid,
                                          "jira_url": bug_template.replace("issue_key", bugid),
                                          "summary": issue.fields.summary.split(",testcase=")[0],
                                          "priority": issue.fields.priority,
                                          "status": issue.fields.status,
                                          "assignee": issue.fields.assignee,
                                          "count": self.bugid_list[bugid],
                                          "appname":bug_info['appname'],

                                          }
                        # print(temp_jira_dict)
                        self.jiras_info_list.append(temp_jira_dict)
                        # 新增分类 互部的桌面应用
                        patchwall_apps = ["com.xiaomi.mitv.appstore","com.mitv.tvhome","com.xiaomi.tv.gallery","com.xiaomi.account","com.xiaomi.account.auth",
                                          "com.android.packageinstaller","com.xm.webcontent","com.mitv.shoplugin","com.duokan.videodaily","com.mitv.dvbplayer",
                                          "com.mitv.tvhome.atv"]
                        # # todo 重新根据App name读取component
                        component, __app_name = self.components.get_component(bug_info["appname"])  # 分配一个模块，一定会有返回值，如果匹配不到，会返回BSP-default模块
                        if component == "BSP-default":
                            if __app_name in self.installed_3apps:
                                component = "APP-第三方应用"
                            elif ISSUE_TYPE =="fc":  # 如果是FC 匹配不到一个合适的模块，则分配给BSP-Framework
                                component = "BSP-Framework"
                            else:
                                component = "BSP-稳定性"
                        # jira_issue_fields = issue.raw['fields']
                        # component = jira_issue_fields['components'][0]['name']

                        if ISSUE_TYPE in ['fc', 'nativecrash', 'tombstone']:
                            if "第三方应用" in component:
                                bug_info_type = "appstore_app_crash"
                            elif "BSP-GTVS" in component:
                                bug_info_type = "appstore_app_crash"
                            elif __app_name in patchwall_apps:
                                bug_info_type = "patchwall_app_crash"
                            else:
                                bug_info_type = "system_app_crash"

                        elif ISSUE_TYPE == "anr":
                            if "第三方应用" in component:
                                bug_info_type = "appstore_app_anr"
                            elif "BSP-GTVS" in component:
                                bug_info_type = "appstore_app_anr"
                            elif __app_name in patchwall_apps:
                                bug_info_type = "patchwall_app_anr"
                            else:
                                bug_info_type = "system_app_anr"

                        elif ISSUE_TYPE == "dead_system":
                            bug_info_type = "start_failed"

                        elif "audio" in ISSUE_TYPE:
                            bug_info_type = "audio(audio_flinger/audio_pa)"

                        elif "video_display" in ISSUE_TYPE or "video_decoder" in ISSUE_TYPE or "start_video" in ISSUE_TYPE:
                            bug_info_type = "video_display(video_decoder)"

                        elif "system_server" in ISSUE_TYPE:  # system_server_hung
                            bug_info_type = "reboot_systemserver"

                        elif "reboot_hardware" in ISSUE_TYPE:  #  hardware reboot
                            bug_info_type = "reboot_kernel"

                        elif "wifi" in ISSUE_TYPE:
                            if __app_name == "wifi_hard_reset":
                                bug_info_type = "wifi_hard_reset"
                            else:    # rtl_wifi & wifi_soft_reset
                                bug_info_type = "wifi_soft_reset"
                        # 其他的直接就用读到的bug_info_type就行
                        else:
                            bug_info_type = ISSUE_TYPE

                        report_bugs_dict[bugid] = {"appname": bug_info["appname"],
                                                   "bug_type": bug_info_type,
                                                   "count": self.bugid_list[bugid]}

            except Exception as e:
                print(traceback.print_exc())
                print(e)

        self.renderData["jira_infos"] = self.jiras_info_list
        with open(os.path.join(self.logFolder,"Report_bugs.json"),"w") as j_file:   # Report的summary
            json.dump(report_bugs_dict,j_file,ensure_ascii=False)

    def parseCaseFolder(self):
        """遍历每个case result folder，解析caselog，解析issuefile,统计总执行时长"""
        caseLogList = []
        if os.path.exists(os.path.join(self.logFolder, "parse.list")):
            parselist = open(os.path.join(self.logFolder, "parse.list"), "r")
            for line in parselist.readlines():
                line = line.strip()
                caseLogList.append(line)
        print("parse caseLogList:",caseLogList)

        # todo count case folder if not equal to foldercount,then send message to feishu
        foldercount = 0
        for folder in os.listdir(self.logFolder):
            if os.path.isdir(os.path.join(self.logFolder, folder)):   # case_folder
                foldercount += 1
        if len(caseLogList) != foldercount:
            # self.feishu.send_text_message("自动化测试解析casefolder提交jira issue异常结束，\n" +
            #                               "已解析casefolder数量={} 与总casefolder数量={} 不符\n".format(len(caseLogList),foldercount) +
            #                               "任务地址:{}".format(self.build_url), self.feishu_user)
            self.feishu.send_message_interactive(receiver="heyingmei",
                                                 subject="自动化压测解析通知",
                                                 message_items={"任务地址": self.build_url,
                                                                "备注": "自动化测试解析casefolder提交jira issue异常结束,已解析casefolder数量={} 与总casefolder数量={} 不符".format(len(caseLogList),foldercount)},
                                                 subject_background_color="blue")
        for folder_ in caseLogList:
            folder = os.path.join(self.logFolder,folder_)
            # print("\nprocessing in folder:",folder)
            pic_dir = folder
            url_list = folder.split("/")
            for i in url_list:
                if "_testResult_" in i:
                    start_index = url_list.index(i)
                    pic_dir = "/".join(url_list[start_index:])
                    break
            resultdata = {"casefoldername":folder_,
                          "pkgname":"",   # monkey的包名
                          "appname":"",  # monkey的包中文名
                          "casename":"",  # stability / miplayer的case名
                          "casechname":"", # stability / miplayer的case中文名
                          "caseurl":os.path.join(self.jenkinsLink,pic_dir),   # case的link
                          "ispass":"fail",
                          "case_result":"PASS",   # 投屏测试用例的执行结果 PASS(投屏pass)，ERROR(脚本执行异常)，FAIL(投屏异常)
                          "case_comment":"",     # 备注
                          "fcCount":0,
                          "TOPfcCount":0,
                          "anrCount":0,
                          "nativecrashCount":0,
                          "tombstoneCount":0,
                          "rebootCount":0,
                          "dead_systemCount":0,
                          "hdmiCount":0,
                          "playerErrCount":0,
                          "wifiCount":0,
                          "smartshareCount":0,
                          "speeduiCount":0,
                          "failreason":""
                          }     # 每个case的result data sheet
            self.parseCaseLog(folder, resultdata)  # 解析case.log
            self.parseIssueFile(folder, resultdata)  # 解析issue file
            if self.testType == "smartshare":
                resultdata["ispass"] = resultdata["case_result"]   # 大小写敏感，报告template里面已经都改了
            if resultdata["fcCount"] != 0:
                resultdata["failreason"] += "\n FC happened"
            if resultdata["anrCount"] != 0:
                resultdata["failreason"] += "\n ANR happened"
            if resultdata["tombstoneCount"] != 0:
                resultdata["failreason"] += "\n Tombstone happened"
            if resultdata["rebootCount"] != 0:
                resultdata["failreason"] += "\n Reboot happened"  # 重启
            if resultdata["dead_systemCount"] != 0:
                resultdata["failreason"] += "\n DeadSystemException happend" # 海外电视死机
            if resultdata["hdmiCount"] != 0:
                resultdata["failreason"] += "\n HDMI Error happened"  # 信号源异常
            if resultdata["wifiCount"] != 0:
                resultdata["failreason"] += "\n Wifi Reboot Error happened"  # wifi异常
            if resultdata["playerErrCount"] != 0:
                resultdata["failreason"] += "\n MiPlayer Error happened"  # 小米播放器的异常
            if resultdata["smartshareCount"] != 0:
                resultdata["failreason"] += "\n SmartShare Error happened"  # 投屏异常
            if resultdata["speeduiCount"] != 0:
                resultdata["failreason"] += "\n SpeedUI not work"
            if resultdata["fcCount"] + resultdata["anrCount"] + resultdata["tombstoneCount"] + resultdata["rebootCount"] + \
                    resultdata["dead_systemCount"] + resultdata["hdmiCount"] + resultdata["playerErrCount"] + \
                    resultdata["smartshareCount"] + resultdata["wifiCount"] + resultdata["speeduiCount"]== 0:
                del resultdata["failreason"]     # 如果这个case没有异常，则去掉failreason这个键值对
            if "monkey" in self.testType:
                resultdata["cpuurl"] = os.path.join(fds_Link , self.testType, pic_dir + "/cpu.html")    # 2024.6.26存到fds
                resultdata["memurl"] = os.path.join(fds_Link , self.testType, pic_dir + "/mem.html")
                resultdata["sermemurl"] = os.path.join(fds_Link, self.testType,pic_dir + "/ser_mem.html")
                resultdata["cpu_img_url"] = os.path.join(fds_Link, self.testType, pic_dir + "/cpu.png")
                resultdata["mem_img_url"] = os.path.join(fds_Link, self.testType,pic_dir + "/mem.png")
                resultdata["ser_mem_img_url"] = os.path.join(fds_Link, self.testType,pic_dir + "/ser_mem.png")
                self.html_pic.run(folder,self.android_version)   # 画图
            self.resultSheet.append(resultdata)  # 如果BUG LIST和备注再对应不上，则说明有issue提交失败了。

    def parseCaseLog(self,folder,resultdata):
        """解析case.log"""
        FLAG_PASS = "Sleeping for"
        FLAG_NOTRUN = "monkey aborted"
        ispass = "fail"     # fail / not run / pass
        case_result = "PASS"     # 投屏测试用例的执行结果 PASS(投屏pass)，ERROR(脚本执行异常)，FAIL(投屏异常)
        try:
            logfile = open(os.path.join(folder,"case.log"), "r")
            lines = logfile.readlines()
        # except IOError as e:
        #     lines = ""
        #     pass
        except Exception as e:
            lines = ""
            return

        casestep = ""
        stepindex = 0
        casename = "no case log"
        for line in lines:
            if line.find("INSTRUMENTATION_STATUS: class=") != -1:  # get package name
                index = len("INSTRUMENTATION_STATUS: class=")
                pkgname = line[index:].rstrip()
                resultdata["pkgname"] = pkgname
                resultdata["appname"] = get_app_ch_name(pkgname)    #中文名称
                casename = pkgname if pkgname.startswith('com.xiaomi') else pkgname.split('.')[-1]   # stability
                resultdata["casename"] = casename

            elif line.find("INSTRUMENTATION_STATUS: title=")!= -1:
                index = len("INSTRUMENTATION_STATUS: title=")
                casechname = line[index:].rstrip()
                resultdata["casechname"] = casechname

            elif line.find("Time: ") != -1:     # monkey
                index = line.index("Time: ")
                exetime = line[index+6:].rstrip()
                resultdata["exetime"] = exetime
                print("exetime:",exetime)
                if self.testType == "monkey" or self.testType == "apps-switch" or self.testType == "MIUITVmonkey" or self.testType == "Top22APPmonkey" or "monkey" in self.testType.lower():
                    self.exetime_sum += float(exetime)
                    print("exetime_sum:",self.exetime_sum)
            # Python版case.log的测试时间
            elif line.find("Ran 1 test in ") != -1:   # python case
                index = len("Ran 1 test in ")
                exetime = line[index:].rstrip()
                resultdata["exetime"] = exetime
            elif line.find("testTime==") != -1:  # record execute time  # 非monkey
                index = len("testTime==")
                exe_time = line[index:].rstrip()
                if self.testType != "monkey" or self.testType != "apps-switch" or self.testType != "MIUITVmonkey" or self.testType != "Top22APPmonkey":
                    self.exetime_sum += float(exe_time)

            # 解析caseStep Java版
            elif line.find("INSTRUMENTATION_STATUS: CaseStep=") != -1:  # get case step
                if stepindex < 10:
                    stepindex += 1
                    line = re.sub(r'INSTRUMENTATION_STATUS: CaseStep=\d?\d?\.?', str(stepindex) + '.', line, 1).rstrip()
                    casestep += line + "\n"
                # 超过10行的就不打了，太多了，没必要全都打印出来
                elif stepindex == 10:
                    stepindex += 1
                    casestep += "... \n"  # 表示没有打完

            elif line.find(FLAG_NOTRUN) != -1:  # record not run
                ispass = "notrun"
            elif line.find(FLAG_PASS) != -1:  # record pass
                ispass = "pass"

            elif line.find("remark") != -1:   # 解析异常结果
                line_dict = eval(line.split("remark:")[1].strip())
                print("line dict:",line_dict)
                print(type(line_dict))
                resultdata["case_result"] = line_dict["case_result"].upper()
                resultdata["case_comment"] = line_dict["comment"]
                if resultdata["case_result"] == "ERROR":
                    self.summarySheet["error"] += 1
                elif resultdata["case_result"] == "FAIL":
                    self.summarySheet["fail"] += 1

            elif line.find("smartshare_pass_rate") != -1:  # 解析手机投屏发现率
                print(line)
                pattern = r'\d+\.\d{2}'
                pass_rate = re.findall(pattern, line)[0]
                print("pass rate:",pass_rate)
                print(type(pass_rate))

                if pass_rate == "100.00":
                    resultdata["case_result"] = "PASS"
                else:
                    resultdata["case_result"] = "FAIL"
                resultdata["case_comment"] = "手机投屏发现率：{}%".format(pass_rate)
                self.summarySheet["fail"] += (1 - round(float(pass_rate) / 100,2))  # 保留两位小数


                # Python版failstack
            elif line.find("... FAIL") != -1 or line.find('... ERROR') != -1:   # case运行失败，但是不需要改变ispass的值，ispass初始值就是fail
                casecontent = self.get_traceback(folder)  # 截取错误栈，正好只打印最后一次error或fail的错误栈，否则就太多了
                resultdata["failstack"] = casecontent[:1024]

            # Java版failstack
            elif line.find("INSTRUMENTATION_STATUS: stack=") != -1:  # record fail reason
                index = len("INSTRUMENTATION_STATUS: stack=")
                failreason = line[index:].rstrip()
                resultdata["failreason"] = "Fail stack:\n" + failreason[0:70] + "\n" + failreason[71:140] + "\n" + failreason[141:]

            elif line.find("INSTRUMENTATION_STATUS: screencap=") != -1:  # get screenshot info
                index = len("INSTRUMENTATION_STATUS: screencap=")
                screencap = line[index:].rstrip()
                resultdata["screencap"] = screencap

            elif line.find("skipped=1") != -1:    # 海外盒子twilight，有skip的case，解析是3s左右，所以还是保留解析
                ispass = "skip"

            elif line.find("OK") != -1:  # record pass or fail
                ispass = "pass"
                
        resultdata["ispass"] = ispass
        if self.testType != "monkey" and self.testType != "apps-swtich" and self.testType != "MIUITVmonkey" and self.testType != "Top22APPmonkey":
            resultdata["casestep"] = casestep
            if casestep == "":   #1、有可能是case没跑起来,报告中也没有显示相关信息；2、国内稳定性测试没有casestep，所以打印整个case.log作为casecontent
                if "failstack" not in resultdata.keys() or resultdata['failstack'] == "":
                    if os.path.exists(folder + "/case.log"):
                        casecontent = open(folder + "/case.log", "r").read()
                        resultdata["casecontent"] = casecontent   # 直接读取整个case.log
            if resultdata["casechname"] == "":   # 没有获取到title，也就是用例中文描述，则使用casename
                casechname = casename.split('.')[-1]
                resultdata["casechname"] = casechname  # 至少让他有中文描述的link
            if casename == "":
                resultdata["casename"] = folder
                resultdata["casechname"] = "No case.log"

    def get_traceback(self,folder):
        logfile = linecache.getlines(folder + "/case.log")
        trace_content = ""
        for line in logfile:  # 截取错误栈
            trace_content += line
            if "Ran 1 test" in line:
                break
        return trace_content

    def parseIssueFile(self,folder,resultdata):
        """解析issue file"""
        filelistall = os.listdir(str(folder))  # 解析issue file
        for file in filelistall:   #
            if re.search(r"issue", file):
                try:
                    issuefile = str(folder + "/" + file)
                    logstackfile = issuefile.replace("issue", "logstack")
                    package = get_package(issuefile)
                    if package == None:
                        # print("fail to get app name! fail to commit issue")
                        return
                    __component, __app_name = self.components.get_component(package)
                    # print("check __component:", __component)

                    if __component == "BSP-default":
                        if __app_name in self.installed_3apps:
                            __component = "APP-第三方应用"
                        elif issuefile.endswith(".fc"):  # 如果是FC 匹配不到一个合适的模块，则分配给BSP-Framework
                            __component = "BSP-Framework"
                        else:
                            __component = "BSP-稳定性"

                    if __component in ["autotest", "ignore_packages"]:
                        print("===========It is an %s issue, skip===========" % __component)
                    elif is_uiautomator(issuefile) or is_uiautomator(logstackfile):
                        print("===========Uiautomator error , skip.=========")
                    else:  # 根据issue类型，写入到备注栏
                        self.error_count += 1
                        issue_type = file.split('.')[-1]  # issuefile的尾缀为issue类型
                        # print("issuetype:", issue_type)
                        if issue_type == 'fc':
                            resultdata["fcCount"] += 1
                            resultdata["ispass"] = "FC"  # issue
                            if is_TOP_pkg(issuefile):
                                resultdata["TOPfcCount"] += 1  # 前台应用
                        elif issue_type == 'anr':
                            resultdata["anrCount"] += 1
                            resultdata["ispass"] = "ANR"
                        elif issue_type == 'tombstone':
                            resultdata["tombstoneCount"] += 1
                            resultdata["ispass"] = "Tombstone"
                        elif "reboot" in issue_type:
                            resultdata["rebootCount"] += 1
                            resultdata["ispass"] = "Reboot"
                        elif "system_server_hung" in issue_type:   # 这个也是reboot的一种
                            resultdata["rebootCount"] += 1
                            resultdata["ispass"] = "Reboot"
                        elif issue_type == 'dead_system':
                            resultdata['dead_systemCount'] += 1  # 报告模板要改
                            resultdata["ispass"] = "DeadSystemException"
                        elif issue_type == "hdmi":
                            resultdata["hdmiCount"] += 1
                            resultdata["ispass"] = "HDMI"
                        elif issue_type == "wifi":        # 子系统重启
                            resultdata["wifiCount"] += 1
                            resultdata["ispass"] = "WIFI"
                        elif issue_type == "p2p" or "rtsp" in issue_type:        # miracast的异常
                            resultdata["smartshareCount"] += 1
                            resultdata["ispass"] = "SMARTSHARE"
                        elif issue_type == "speedui" or issue_type == "focusmode":
                            resultdata["speeduiCount"] += 1
                            resultdata["ispass"] = "SpeedUI"
                        elif "video" in issue_type or "audio" in issue_type:
                            resultdata["playerErrCount"] += 1
                            resultdata["ispass"] = "MiPlayer"
                        # 参与mtbf的error
                        # if __component != "APP-第三方应用" and __component != "BSP-信号源" and "BSP-小米播放器" not in __component and "BSP-GTVS" not in __component:
                        #     self.mtbf_error += 1
                        if "reboot" in issue_type or "dead_system" in issue_type or "system_server_hung" in issue_type:
                            self.mtbf_error += 1    # 死机、重启才参与mtbf计算
                except Exception as e:
                    print("error in {}".format(str(folder + "/" + file)))
                    # print(e)

    def get_jenkinsLink(self):
        jenkinsLink = "."
        if self.build_url and "http://jenkins.tv.xiaomi.srv/" in self.build_url:
            templist = self.build_url.split("/")
            jenkinsLink = ("/").join(templist[:5]) + "/ws/"
        elif os.path.isfile(os.path.join(self.logFolder,"jenkinsLink.txt")):
            jenkinsLink = open(os.path.join(self.logFolder,"jenkinsLink.txt")).read().rstrip()   # jenkin archive
        elif os.path.isfile("config/docker_jenkinsLink.txt"):
            jenkinsLink = open("config/docker_jenkinsLink.txt").read().rstrip()  # for docker
        elif os.path.isfile("config/jenkinsLink.txt"):
            jenkinsLink = open("config/jenkinsLink.txt").read().rstrip()  # 本地文件（docker不适用）
        print("jenkinsLink:", jenkinsLink)
        return jenkinsLink

    def writeToFile(self):
        html = self.t.render(Context(self.renderData))
        htmlPath = os.path.join(self.logFolder, "index1.html")
        with open(htmlPath, 'w') as fp:
            fp.write(html)
        self.renderData['javascript'] = True
        html = self.t.render(Context(self.renderData))
        htmlPath = os.path.join(self.logFolder, "report.html")
        with open(htmlPath, 'w') as fp:
            fp.write(html)

    # 在生成report.html文件生成后, 将jira url写入report.html
    def gen_jiralink(self):
        """
        更新jira上的“描述”模块，更新报告中BugList项
        """
        bug_template = '<a href="http://jira.n.xiaomi.com/browse/issue_key ">issue_key</a><span class="failCount">&nbsp&nbsp(count)&nbsp&nbsp</span>'
        all_lines = ""
        for bugid, bug_count in self.bugid_list.items():
            # print(bugid, bug_count)
            bug_tmp = bug_template
            bug_tmp = bug_tmp.replace("issue_key", bugid)
            bug_tmp = bug_tmp.replace('count', str(bug_count))  # 报告中显示的是本次压测中发生的次数
            all_lines += bug_tmp

            # todo 更新jira描述，一个bug更新一次即可
            bug_info = self.jira_db.find_one({'bugid': bugid})
            # print("check bug info:", bug_info)  # check count=1有没有计数项
            if bug_info:
                try:  # update jira 详情模块
                    des_old = self.jira.issue(bugid).fields.description.strip()  # .encode('utf8') python3读取string
                    # print("des old:", des_old)
                    last_desc = copy.deepcopy(des_old)
                    while ",截止目前共发生" in last_desc:  # 有些旧的问题还是叠加的，用循环可以把遗留问题解决掉。   # 更新jira描述
                        old_times = re.findall(",截止目前共发生\d*?次", last_desc)
                        last_desc = last_desc.replace(old_times[0], "")
                    des_new = last_desc + ",截止目前共发生%s次" % bug_info['count']  # 这里显示的jira db里统计的次数
                    # print("des new:", des_new)
                    self.jira.issue(bugid).update(description=des_new)

                    # todo jira超过5次不再往上贴log，但是开发还是会来找新的日志，在最后更新jira描述的时候，如果jira发生次数超过5次，则把本次压测的kingsoft-cloud的日志链接贴comment上
                    if bug_info['count'] > 5:
                        folderlist = self.logFolder.split("/")
                        self.jira.add_comment(bugid,"本次压测resultfoder异常日志目录url:{}\n"
                                                    "异常发生次数超过5次，jira不再comment中贴对应casefolder日志，可根据record_jira文件找到jira对应的casefolder\n"
                                                    "MITVAUTO库日志查询方法文档：https://xiaomi.f.mioffice.cn/docx/doxk4rBfHc4qGjwh5uqbl6dO4Xe\n"
                                                    "若没有访问权限小米融合云(FDS)日志，先登录后，再找@heyingmei开通权限\n"
                                                    "压测任务url：{}".format(os.path.join(fds_filestore ,self.testType,folderlist[-1]),self.build_url))

                except Exception as e:
                    print("更新jira描述出现错误")
                    # print(e)

        # print(all_lines)
        try:  # 更新报告中buglist项
            with open(self.logFolder + '/report.html', 'r') as r:
                lines = r.readlines()
            with open(self.logFolder + '/report.html', 'w') as w:
                for line in lines:
                    if line.find('bug list') != -1:
                        w.write(line.replace('bug list', all_lines))
                    elif line.find("Mitv小米播放器压测报告") != -1:
                        w.write(line.replace("小米播放器压测",self.testtype_chname))
                    elif line.find("MiTV自动化Monkey测试报告") != -1:
                        w.write(line.replace("Monkey测试",self.testtype_chname))
                    else:
                        w.write(line)
        except IOError as e:
            print(e)

        # todo report.html存放一份到fds上，并设置为网页打开
        metadata = FDSObjectMetadata()
        metadata.add_header(key="content-type", value="text/html")
        des_dir = os.path.join(self.logFolder,"report.html")
        url_list = des_dir.split("/")
        for i in url_list:
            if "_testResult_" in i:
                start_index = url_list.index(i)
                des_dir = "/".join(url_list[start_index:])     # 如果是绝对路径，要把前面的去掉
                break
        source_report = os.path.join(self.logFolder, "report.html")
        self.fds.upload_to_fds(source_report, des_dir,"mitv-autotest/"+self.testType, metadata=metadata)
        print("Report file in fds url:{}".format(os.path.join(fds_Link + self.testType,des_dir)))

        # todo record_jira 存一份到fds
        if os.path.isfile(os.path.join(self.logFolder,"record_jira")):
            source_record_jira = os.path.join(self.logFolder,"record_jira")
            des_record_jira = des_dir.replace("report.html","record_jira")
            self.fds.upload_to_fds(source_record_jira,des_record_jira,"mitv-autotest/"+self.testType)
            print("Record jira id & issuefile in fds url:{}".format(os.path.join(fds_Link+self.testType,des_record_jira)))
        # todo getprop.txt也存一份到fds
        if os.path.isfile(os.path.join(self.logFolder,"getprop.txt")):
            source_getprop = os.path.join(self.logFolder,"getprop.txt")
            des_getprop = des_dir.replace("report.html","getprop.txt")
            self.fds.upload_to_fds(source_getprop,des_getprop,"mitv-autotest/"+self.testType)
            print("put getprop.txt to fds url:{}".format(os.path.join(fds_Link+self.testType,des_getprop)))


if __name__ == '__main__':
    deviceID = argv[1]  # 电视设备id
    logFolder = argv[2]  # project result folder
    testType = argv[3]  # 测试类型 stability smoke monkey apps-switch
    address = argv[len(argv) - 1]  # 测试地点为最后一个参数
    build_url = " "
    build_user = "unknown"
    if len(argv) >= 6:
        build_url = argv[4]
        build_user = argv[5]
    report = Generate(deviceID, logFolder, testType, build_url, build_user, address)
    report.generating()

# python3 Reporter.py $device_id $project_result_folder $testType $BUILD_URL $BUILDER_USER $Address
# argv[1] : device_ID
# argv[2] : project result folder，如果在当前目录下执行，需要输入绝对路径
# argv[3] ：测试类型 stability stability-overseas miplayer monkey apps-switch 根据不同的测试类型调用不同的report template
# argv[4] : Jenkins console link
# argv[5] : builder user
# argv[6] : 测试地点，最后一个参数
# todo 注意，重新解析，resultfolder中需要有对应的jenkinsLink.txt文件，否则log地址会有误