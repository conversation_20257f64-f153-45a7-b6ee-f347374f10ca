#! /usr/bin/env python
# -*- coding:utf-8 -*-

# 任务中断后，一键触发完成解析上报、生成报告，发送email
import os,argparse,datetime
try:
    from UpdateIssue import LogParser
    from Reporter import Generate
    from sendEmails import SendEmail
    from upload_result_data import UploadTestInfos
    from common_method import *
    from make_parselist import *
except:
    from .UpdateIssue import LogParser
    from .Reporter import Generate
    from .sendEmails import SendEmail
    from .upload_result_data import UploadTestInfos
    from .common_method import *
    from .make_parselist import *
from feishu.feishu import FeiShu
from mfile.upload_to_fds import *
fds = Fds()
feishu = FeiShu()

def zip_resultfolder(result_folder,testType):
    """打包整个目录，上传至fds"""
    zip_file_path = result_folder+".zip"  # zip文件最终存储路径与result_folder在同级目录
    if not os.path.exists(zip_file_path):
        print("zip file:", time.ctime())
        shutil.make_archive(base_name=result_folder, format="zip", root_dir=result_folder)  # 这个文件生成在casefolder外面，把它挪进casefolder中
        print(time.ctime())
    try:
        source_dir, des_dir = zip_file_path, zip_file_path
        url_list = des_dir.split("/")
        for i in url_list:
            if "_testResult_" in i:
                des_dir = os.path.join(i.replace(".zip",""), url_list[-1])
                break
        print("source zip path:", zip_file_path)
        print("des path:", des_dir)
        log_url = fds.upload_to_fds(source_dir, des_dir, "mitv-autotest/" + testType)
        print("log url:", log_url)
        return log_url
    except Exception as e:
        print("fail to push log to fds")
        print(e)

def running(device_id,test_job_name,job_build_number,testtype,job_build_url,test_builder,address,report_date = 0,pack_logs = 0):
    # todo send feishu ,重新解析
    result_folder = ""
    # 列出当前workspace中所有的job folder，遍历对应带@的目录
    for job_folder in os.listdir("/home/<USER>/Jenkins/workspace/"):
        if test_job_name in job_folder and "@tmp" not in job_folder:
            result_folder_fatherpath = os.path.join("/home/<USER>/Jenkins/workspace/",job_folder)
            # result_folder_fatherpath = os.path.join("/home/<USER>/Jenkins/workspace/",args.test_job_name)
            print("Looking result folder in job:{}".format(job_folder))
            for folder in os.listdir(result_folder_fatherpath):
                temp_folder = os.path.join(result_folder_fatherpath,folder)
                if os.path.isdir(temp_folder) and "_testResult_{}_".format(job_build_number) in folder:
                    result_folder = temp_folder
                    break
            if result_folder != "":    # 说明已经找到对应的result_folder
                break

    if result_folder == "":
        print("没有找到应用压测结果目录")     #抛出异常，jenkins执行状态为fail
        raise Exception("没有找到应用压测结果目录，请检查电视是否异常，或者压测目录是否在当前服务器节点")
        # exit()
    print("Found result folder:{}".format(result_folder))
    testtype, testtype_chname,device_type,network_ = get_testchname(os.path.join(result_folder, "test_info"), testtype)

    # todo 打包全量日志，仅做打包日志操作，不做解析日志的操作
    if pack_logs == 1:
        print("打包全量日志，并上传fds。打包日志时间因日志文件大小有关，请耐心等待。本次任务只做日志打包工作，不做解析，如需解析报告，请另外构建一次任务")
        log_url = zip_resultfolder(result_folder,testtype)
        # todo 发送飞书通知
        feishu.send_message_interactive(receiver=test_builder,
                                        subject="自动化压测全量日志",
                                        message_items={"原始任务地址": job_build_url,
                                                       "全量日志下载链接，如无法正常跳转，请复制至浏览器新页面下载": log_url},
                                        subject_background_color="blue")

        exit()

    # 如果是竟品机，则不做上报的动作，只解析生成报告
    if "hisense" in testtype.lower() or "tcl" in testtype.lower() or "huawei" in testtype.lower():
        print("竟品机，不上报jira，直接生成测试报告")
        run_parselist(result_folder)

    # todo 判断已解析的folder数量是否等于所有case folder的数量，如果相等并且有report.html文件，则无需重新解析

    parse_folder_count = 0
    if os.path.exists(os.path.join(result_folder, "parse.list")):
        parselist = open(os.path.join(result_folder, "parse.list"), "r").readlines()
        parse_folder_count = len(parselist)

    folder_count = 0
    for folder in os.listdir(result_folder):
        if os.path.isdir(os.path.join(result_folder, folder)):  # case_folder
            folder_count += 1

    if os.path.exists(os.path.join(result_folder, "report.html")) and parse_folder_count == folder_count :  # 正常结束
        print("压测正常结束，无需重新解析。")

    else:
        test_schedule = os.path.join(result_folder, "test_schedule")  # 测试进度
        parser = LogParser(device_id, result_folder, testtype, job_build_url,test_builder)
        parser.run_parse()
        if os.path.exists(test_schedule):
            now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            add_log(test_schedule, "{} Finished UpdateIssue".format(now_time))

        gen_report = Generate(device_id, result_folder, testtype, job_build_url, test_builder,address)  # 调用了就直接执行了生成报告了
        gen_report.generating()
        if os.path.exists(test_schedule):
            now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            add_log(test_schedule, "{} Finished Report".format(now_time))

        sendEmail = SendEmail(device_id, result_folder, testtype, test_builder)
        sendEmail.mail_sender()

        upload_resulte_infos = UploadTestInfos(device_id,testtype,result_folder)    # 上传结果数据
        upload_resulte_infos.upload_res_info()

    if report_date == 1:     # 不做解析，但重新上传数据
        print("不重新解析报告，但重新上报结果数据")
        upload_resulte_infos = UploadTestInfos(device_id, testtype, result_folder)  # 上传结果数据
        upload_resulte_infos.upload_res_info()

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description="Parameters")
    parser.add_argument("--device_id",type=str,help="测试设备ip地址")
    parser.add_argument("--testtype",type=str,default=None,help="测试类型：monkey/apps-switch/stability/miplayer/None")    # None根据result folder中写入的文件去读取，也可以不填了
    parser.add_argument("--job_build_number",type=int,help="Jenkins压测任务的BUILD NUMBER",default=None)
    parser.add_argument("--test_job_name",type=str,help="Jenkins压测任务的JOB NAME",default="")
    parser.add_argument("--address",type=str,help="测试地点",default="unknown")
    parser.add_argument("--test_builder",type=str,help="测试执行人",default="unknown")
    parser.add_argument("--report_data",type=int,help="只上报结果数据至云平台，无需重新解析，0：不需重新上报，1：重新上报一次结果数据",default=False)   # bool值输入会判定为True
    parser.add_argument("--pack_logs",type=int,help="打包全量日志上传至fds，0：不打包，1：打包",default=False)   # bool值输入会判定为True

    args = parser.parse_args()

    job_build_url = "http://jenkins.tv.xiaomi.srv/job/{}/{}/console".format(args.test_job_name,args.job_build_number)   # 拼接执行日志
    parse_job_name = os.getenv("JOB_NAME")
    parse_build_number = os.getenv("BUILD_NUMBER")
    parse_job_url = "http://jenkins.tv.xiaomi.srv/view/MitvAutoParse/job/{}/{}/console".format(parse_job_name,parse_build_number)

    print("device_id:",args.device_id)
    print("testtype:",args.testtype)
    print("job_build_url:",job_build_url)
    print("test_builder:",args.test_builder)
    print("address:",args.address)

    feishu_user = "heyingmei"
    feishu = FeiShu()
    feishu.send_message_interactive(receiver=feishu_user,
                                    subject="自动化压测解析通知",
                                    message_items={"执行人":args.test_builder,
                                                 "原始任务地址":job_build_url,
                                                 "解析任务地址":parse_job_url,
                                                 "备注":"自动化测试异常中止，现执行自动解析上报模块"},
                                    subject_background_color = "blue")

    running(args.device_id, args.test_job_name, args.job_build_number, args.testtype, job_build_url, args.test_builder, args.address,args.report_data,args.pack_logs)

# python3.8 -u guard_reporter.py --device_id ${DEVICEID} --testtype ${TESTTYPE} --job_build_number $JOB_BUILD_NUMBER --test_job_name ${TEST_JOB_NAME} --address $Address --test_builder ${TEST_BUILDER}