#! /usr/bin/env python
# -*- coding:utf-8 -*-

from __future__ import division, print_function
import copy
import os.path
import shutil
from sys import argv
import urllib3
urllib3.disable_warnings()
try:
    from common_method import *     # 进入mitv_autotest_parse目录后执行
except Exception as e:
    from .common_method import *    # 在mitv_autotest_parse目录外执行
from mjira.tv_jira import Jira
from feishu.feishu import <PERSON><PERSON><PERSON><PERSON>
from mfile.upload_to_fds import *

# mongo_collection = DevAppDataCollection()



class LogParser():   # 遍历result文件夹，解析issuefile，上报issue，记录issue和已解析result文件夹
    def __init__(self,device_id,logFolder,testType,build_url=" ",build_user="unknown"):
        """
        解析resultfolder中所有的case，上报jira
        Args:
            device_id:
            logFolder: 本次压测的result folder
            testType:
            build_url:
            build_user:
        """
        self.logFolder = logFolder
        self.device_id = device_id
        self.prop = Prop(self.logFolder, self.device_id)
        self.getprop = self.prop.getprop
        self.testType = testType
        self.jenkinsLink = self.get_jenkinsLink()
        try:
            self.jira = Jira().login_jira()
            mitvauto = self.jira.project(jira_key)
        except Exception as e:
            print(traceback.print_exc())
            print("login jira 出错，等1min后重试一次")
            self.jira = Jira().login_jira()
            mitvauto = self.jira.project(jira_key)

            # 如果还是失败，就会直接fail退出了
        self.components = Component(mitvauto)
        self.build_url = build_url
        self.build_user = build_user

        self.feishu_user = []
        # if self.build_user not in ["unknown","ROBOT-MITV","robot-mitv","jenkins"]:
        #     self.feishu_user.append(self.build_user)
        self.feishu_user.append('heyingmei')  # 飞书预警
        self.feishu = FeiShu()
        print('check feishu:', self.feishu_user)  # 飞书通知
        self.fds = Fds()
        # todo 电视上已安装的app
        self.installed_3apps=""
        if os.path.isfile(os.path.join(self.logFolder,"record_3apps")):
            self.installed_3apps = open(os.path.join(self.logFolder,"record_3apps")).read().rstrip()

        self.record_jira = os.path.join(self.logFolder,"record_jira")

    def get_jenkinsLink(self):
        jenkinsLink = "."
        if os.path.isfile(os.path.join(self.logFolder,"jenkinsLink.txt")):
            jenkinsLink = open(os.path.join(self.logFolder,"jenkinsLink.txt")).read().rstrip()   # jenkin archive
        elif os.path.isfile("config/docker_jenkinsLink.txt"):
            jenkinsLink = open("config/docker_jenkinsLink.txt").read().rstrip()  # for docker # todp docker的jenkinslink也要换位置才行，每次都要保存到结果目录
        elif os.path.isfile("config/jenkinsLink.txt"):
            jenkinsLink = open("config/jenkinsLink.txt").read().rstrip()  # 本地文件（docker不适用）
        print("jenkinsLink:", jenkinsLink)
        return jenkinsLink

    def run_parse(self):
        self.mongo_collection = DevAppDataCollection()
        self.jira_db = self.mongo_collection._collection(collection_name="jira_db", db_name="autotest")
        print("+++++++++++++++pymongo+++++++++++++++++")
        self.get_database(self.testType)    # 读取数据库类型和压测中文名
        self.assm_mn = self.getprop("ro.boot.assm_mn")
        folderlist = []
        for folder in os.listdir(self.logFolder):
            if os.path.isdir(os.path.join(self.logFolder, folder)):   # case_folder
                folderlist.append(os.path.join(self.logFolder, folder))
        folderlist.sort(key=lambda x: os.stat(x).st_ctime)     # 按文件夹最后一次修改的时间排序
        print("caseLoglist amount:", len(folderlist))  # 解析的文件夹

        self.product_name = self.getprop("ro.product.name")  # 根据start.sh中生成issue file的字段
        self.version = self.prop.get_version()

        self.TV_Product_Model = self.prop.get_tv_product_model()  # Amlogic / MTK
        print("tv platform:{}".format(self.TV_Product_Model))
        parse_folders = []   #已经解析了的case folder
        if os.path.exists(os.path.join(self.logFolder, "parse.list")):          # parse.list只保存到case folder，前面有可能是相对路径可能是绝对路径
            parselist = open(os.path.join(self.logFolder, "parse.list"), "r")
            for line in parselist.readlines():
                line = line.strip()  # 去除末尾换行符
                parse_folders.append(line)
        # 读取parse.list文件，记成一个list，用于后续判断，不能像bug.list那样每次都放到开头,因为他只读取一次
        for folder in folderlist:   # 遍历当前testResult folder中所有的目录
            casefolder = folder.split("/")[-1] if folder.split("/")[-1] != "" else folder.split("/")[-2]
            print("case folder :",casefolder)
            if casefolder in parse_folders:
                continue
            filelistall = os.listdir(str(folder))   # folder =  case_folder
            for file in filelistall:
                if "issue" in file:
                    issuefile = os.path.join(folder,file)
                    logstackfile = issuefile.replace("issue","logstack")
                    if os.path.exists(logstackfile):
                        if (self.testType == "monkey" or self.testType == "apps-switch" or self.testType == "MIUITVmonkey" or self.testType == "Top22APPmonkey" ) and file.endswith("fc"):  # monkey 的 fc，过滤掉部分已知bugs
                            self.judge_special_logs(issuefile, logstackfile)
                    try:
                        self.update_db(folder,issuefile,logstackfile)
                    except Exception as e:   # 大概率是login jira的问题
                        print("update_db error:{}".format(folder))
                        print(traceback.print_exc())  # 定位出错语句

            # record parse.list
            parselist = open(os.path.join(self.logFolder, "parse.list"), "a+")
            parselist.writelines(casefolder + '\n')
            parselist.close()

        self.update_dbversion()

    def judge_special_logs(self,issuefile,logstack):
        """
        monkey issue
        对特定已知的bug的log信息进行筛选，符合条件的取消提交jira
        如果logstack中包含“spacial_logs”，在issue file中标记上"CommitJira:No"
        """
        if os.path.isfile(logstack):
            allline = []
            with open(logstack, "rb") as log:
                for index, line in enumerate(log):
                    line = line.decode('utf-8', 'ignore')   # 去掉乱码
                    allline.append(line)
            with open(logstack,"w") as p:  # 重写该文件
                p.write("".join(allline))
            stack = open(logstack).read()
            issuefile_tmp = open(issuefile, 'a+')
            special_logs_1 = "act=com.mstar.android.intent.action.TV_INPUT_BUTTON flg=0x10000000"
            special_logs_2 = "Could not open database"
            if stack.find(special_logs_1) != -1 or stack.find(special_logs_2) != -1:
                issuefile_tmp.write("CommitJira:No")
                issuefile_tmp.close()

    def update_dbversion(self):
        """
        update local pymongo database : product_versions
        """
        product_name = self.product_name  # ro.product.name
        new_version = self.version
        if self.product_versions.find_one({"_id": product_name}):
            db_vers = self.product_versions.find_one({"_id": product_name}).get('version')
            if db_vers:
                db_vers.append(new_version)
            else:
                db_vers = [new_version]
            sorted_vers = self.sort_version(db_vers)
            self.product_versions.update_one({"_id": product_name}, {"$set": {"version": sorted_vers}})
        else:
            self.product_versions.insert_one({"_id": product_name})
            db_vers = [new_version]
            sorted_vers = self.sort_version(db_vers)
            self.product_versions.update_one({"_id": product_name}, {"$set": {"version": sorted_vers}})

    def sort_version(self, version_list):
        try:
            sorted_versions = sorted(list(set(version_list)), key=lambda a: int(
                a.split('.')[-1]), reverse=True)
            return sorted_versions
        except Exception as e:
            return version_list

    def get_commit_status(self, issuefile):  #
        """
        monkey 在create issue之前，先判断该bug是否需要create，与前面的judge_special_logs对应
        """
        try:
            lines = open(issuefile).readlines()
        except:
            lines = open(issuefile, encoding="gbk").readlines()
        for line in lines:
            match = re.findall(".*CommitJira:(.*).*", line.strip().replace('\r', ','))
            for x in match:
                status_value = match[0]
                return status_value      # 如果是不用commitjira的，这里会有一个No
        return "Yes"

    def update_db(self, folder, issuefile, logstack):
        print("\nupdate_db folder:", folder)
        str_array = issuefile.split(".")
        ISSUE_TYPE = str_array[len(str_array) - 1]
        jira_datasheet = {
            'appname': None,
            'type': None,
            'project': None,
            'version': None,
            "app_version":None,
            'stack': None,
            'bugid': None,
            'count': 1,
            'mac': None,
            'test_type': self.testType
        }
        # app_name = self.get_package(issuefile)
        app_name = get_package(issuefile)
        if app_name == None:
            print("fail to get app name! fail to commit issue")
            return
        __component, __app_name = self.components.get_component(app_name)  # 分配一个模块，一定会有返回值，如果匹配不到，会返回BSP-default模块

        if __component == "BSP-default":
            if __app_name in self.installed_3apps:
                __component = "APP-第三方应用"
            elif issuefile.endswith(".fc"):  # 如果是FC 匹配不到一个合适的模块，则分配给BSP-Framework
                __component = "BSP-Framework"
            else:
                __component = "BSP-稳定性"

        # todo 在jira_db查找前就先过滤掉不上报的包
        # if __component in ["autotest", "GTVS", "ignore_packages"]:   # 过滤
        if __component in ["autotest", "ignore_packages"]:   # 过滤
            print("===========It is an %s issue, skip===========" % __component)
            return
        elif is_uiautomator(issuefile) or is_uiautomator(logstack):  # 过滤掉UiAutomation的bug
            print("Uiautomator error , skip.")
            return
        elif self.get_commit_status(issuefile) == "No":  # monkey中的已知bug，不再提交
            print("===========monkey Knowns issue, ignore it.===========")
            return

        if __app_name != "BSP-default":    # 如果不是找不到匹配的模块名
            app_name = __app_name    # 则用模块中写入的包名，除去冗余

        project = self.getprop("ro.product.name")
        version = self.version
        app_version = self.get_appversion(issuefile)
        stack = ''
        if os.path.exists(logstack):
            stack = open(logstack, 'rb').read().decode('utf8', 'ignore')
            if len(stack) > 2048:    # 字符太多提交不上去了
                stack = stack[:2048]

        # todo 2024.2.1 黑屏相关，区分【miplayer】、【ATV/DTMB/AV/HDMI】
        if ISSUE_TYPE == "video_screen":
            if "ATV" in folder:
                app_name+="_ATV"
            elif "DTMB" in folder:
                app_name+="_DTMB"
            elif "AV" in folder:
                app_name+="_AV"
            elif "HDMI" in folder:
                app_name+="HDMI"
            # 其他的就是miplayer相关的

        print({'appname': app_name,
            'type': ISSUE_TYPE,
            'project': project,
            'version': version,
            "app_version":app_version
             })

        if app_version and app_version != "None":    # 如果有app version，则用app version来check bug exist
            bug_exist = self.jira_db.find_one({
                'appname': app_name,
                'type': ISSUE_TYPE,
                'project': project,
                'app_version': app_version
            })
        else:   # 否则用rom版本check bug exist
            bug_exist = self.jira_db.find_one(
                {'appname': app_name,
                 'type': ISSUE_TYPE,
                 'project': project,
                 'version': version
                 })
        print("check bug exist:",bug_exist)

        # todo 有异常的case都上传日志到fds
        folderlist = folder.split("/")
        zip_name = folderlist[-1] + ".zip"
        zip_file_path = os.path.join(folder, zip_name)  # zip文件最终存储路径在casefolder内
        save2fdslog = os.path.join(folder, "save2fds.log")
        if not os.path.exists(zip_file_path):
            print("zip file:", time.ctime())
            shutil.make_archive(base_name=folder, format="zip", root_dir=folder)  # 这个文件生成在casefolder外面，把它挪进casefolder中
            print("mv file to casefolder")
            shutil.move(folder + ".zip", folder)
            print(time.ctime())
        if not os.path.isfile(save2fdslog):
            try:
                source_dir,des_dir = zip_file_path,zip_file_path
                url_list = des_dir.split("/")
                for i in url_list:
                    if "_testResult_" in i:
                        des_dir = os.path.join(i,url_list[-1])
                        break
                print("source zip path:", zip_file_path)
                print("des path:", des_dir)
                # log_url = self.fds.upload_to_fds(source_dir,object_name=self.testType,bucket_name="mitv-autotest")
                log_url = self.fds.upload_to_fds(source_dir, des_dir, "mitv-autotest/" + self.testType)
                add_log(save2fdslog,log_url)  # 上传成功了就存一个日志链接
            except Exception as e:
                print("fail to push log to fds")
                print(e)

        if bug_exist:         # todo 如果bug exist，查找该jira的状态，是否为close，如果是close，则新建一个jira
            find_issue = self.jira.issue(bug_exist['bugid'])
            issue_status = str(find_issue.fields.status)
            print("check issue status:{}".format(issue_status))
            if issue_status == "已关闭" or issue_status == "已解决":   # close的问题 或者resolve的问题
                # todo resolve的问题要先关闭了然后再新建
                processes = self.jira.transitions(bug_exist['bugid'])
                for process in processes:
                    process_name = process['name']
                    process_id = str(process['id'])
                    if process_name == "关闭问题":
                        print("该问题的状态为‘已解决’，现在改为‘已关闭’，然后再重新建一个issue")
                        self.jira.transition_issue(find_issue,process_id)

                old_bug = copy.copy(bug_exist)
                print("bug_exist",old_bug)
                issue_info = self.createIssue(folder, issuefile, logstack)
                if issue_info:      # createIssue成功了
                    if stack:
                        try:
                            time.sleep(3)
                            self.jira.add_comment(issue_info['issue_key'], stack)
                        except Exception:
                            pass
                    # todo FC issue 是前台应用 or 后台应用
                    if ISSUE_TYPE == "fc" and is_TOP_pkg(issuefile):
                        time.sleep(3)
                        self.jira.add_comment(issue_info['issue_key'], "前台应用 {} 发生FC。".format(app_name))

                    bug_exist["bugid"] = issue_info["issue_key"]    # bug_exist的信息改成新的issue，下次再出现查找到的是新的jira
                    bug_exist['count'] = 1
                    print("replace new issue to jira db:{}\n".format(jira_datasheet))
                    self.jira_db.replace_one(old_bug, bug_exist)
            else:    # bug还没有colse，在后面叠加日志
                old_bug = copy.copy(bug_exist)
                print("bug_exist",old_bug)
                issue_key = bug_exist['bugid']
                url = '%s/browse/%s ' % (addr, issue_key)
                buglist = open(self.logFolder + '/bug.list', 'a+')
                buglist.writelines("<a href=\"" + url + "\">" + issue_key + "</a><span class=\"failCount\">&nbsp&nbsp(count)&nbsp&nbsp</span>" + '\n')  # 三方app也要展示
                buglist.close()

                # todo record jira & issue file
                add_log(self.record_jira,"jira_id:{},issue file:{}\n".format(issue_key,issuefile))

                print("======update jira issue({}) count=======".format(issue_key))
                bug_exist['count'] += 1

                if (bug_exist['count'] == 20 and bug_exist['type'] == "anr") or (bug_exist['count'] == 10 and bug_exist['type'] != "anr"):
                    try:
                        time.sleep(3)
                        self.jira.issue(issue_key).update(priority={'name': 'Critical'})  # 更新字段
                        time.sleep(3)
                        self.jira.issue(issue_key).update(customfield_10817={'value': 'often'})
                        # issue = self.jira.issue(issue_key)     # 旧的写法，都work
                        # sleep(1)
                        # issue.update(priority={'name': 'Critical'})
                        # issue.update(customfield_10817={'value': 'often'})
                    except Exception as e:
                        print("Error happened while updating issue:%s." % issue_key)
                        print(e)
                        # self.feishu.send_text_message("自动化测试更新jira issue相关字段出现错误。\n" +
                        #                          "定位出错文件路径：{}\n".format(issuefile) +
                        #                          "错误原因：fail to update jira issue dict \n" +
                        #                          "任务地址:{}".format(self.build_url),self.feishu_user)
                        self.feishu.send_message_interactive(receiver="heyingmei",
                                                        subject="自动化压测解析通知",
                                                        message_items={"解析任务地址": self.build_url,
                                                                       "定位出错文件路径":issuefile,
                                                                       "备注": "自动化测试更新jira issue相关字段出现错误。"},
                                                        subject_background_color="blue")

                # todo focusmode 的bug更新优先级 @付京东 提供规则
                if bug_exist['type'] == "focusmode":
                    try:
                        new_priority = self.get_priority(issuefile)
                        print("new priority:",new_priority,type(new_priority))
                        old_priority = str(self.jira.issue(issue_key).get_field("priority"))   # 当前的priority
                        # major >> critical / blocker
                        # major / critical >> blocker
                        # minor 次要  major 重要 critical 严重 blocker 紧急
                        if old_priority == "重要" and new_priority != "Major":
                            print("更新issue{}的priority，from{} to {}".format(issue_key,old_priority,new_priority))
                            self.jira.issue(issue_key).update(priority={'name': new_priority})  # 更新字段
                        elif old_priority != "紧急" and new_priority == "Blocker":
                            print("更新issue{}的priority，from{} to {}".format(issue_key,old_priority,new_priority))
                            self.jira.issue(issue_key).update(priority={'name': new_priority})  # 更新字段
                    except Exception as e:
                        print(e)
                        print("自动化测试更新jira优先级出现错误")

                if bug_exist['count'] <= 5:
                    # 创建issue后直接使用add_comment()函数添加服务器log地址
                    # 在issue上添加log地址和日志
                    try:
                        time.sleep(3)  # 加个等待，避免频繁访问，接口hold不住
                        folderlist = folder.split("/")
                        relative_path = folderlist[-2] + "/" + folderlist[-1] + ".zip" # 最后两层目录拼接出来日志跳转链接
                        # todo fds的日志链接,直接粘贴ds的日志链接
                        self.jira.add_comment(issue_key,"Logs url(复制链接至新网页直接下载）: {}\n "
                                                        "压测任务url：{}\n"
                                                        "压测使用 {}网络\n"
                                                        "压测电视mn号：{}\n"
                                                        "若没有访问权限小米融合云(FDS)日志，先登录后，再找@heyingmei开通权限".format(os.path.join(fds_Link,self.testType, relative_path),self.build_url,self.network_,self.assm_mn))
                        time.sleep(3)
                        self.jira.add_comment(issue_key, stack)
                        # todo FC issue 是前台应用 or 后台应用
                        if ISSUE_TYPE == "fc" and is_TOP_pkg(issuefile):
                            time.sleep(3)
                            self.jira.add_comment(issue_key, "前台应用 {} 发生FC。".format(app_name))
                    except Exception:
                        pass
                self.jira_db.replace_one(old_bug,bug_exist)
        else:
            issue_info = self.createIssue(folder, issuefile,logstack)
            if issue_info:
                if stack:
                    try:
                        time.sleep(3)
                        self.jira.add_comment(issue_info['issue_key'], stack)
                    except Exception:
                        pass
                # todo FC issue 是前台应用 or 后台应用
                if ISSUE_TYPE == "fc" and is_TOP_pkg(issuefile):
                    time.sleep(3)
                    self.jira.add_comment(issue_info['issue_key'],"前台应用 {} 发生FC。".format(app_name))

                jira_datasheet['appname'] = app_name
                jira_datasheet['type'] = ISSUE_TYPE
                jira_datasheet['project'] = project
                jira_datasheet['version'] = version
                jira_datasheet['app_version'] = app_version
                jira_datasheet['bugid'] = issue_info['issue_key']
                print("insert new issue to jira db:{}\n".format(jira_datasheet))
                self.jira_db.insert_one(jira_datasheet, True)

    def get_summary(self, issuefile):  #
        try:
            lines = open(issuefile).readlines()
        except:
            lines = open(issuefile,encoding="gbk").readlines()
        for line in lines:
            if line.find('Summary:') != -1:
                if line.find('\r') != -1:
                    for target in range(line.count('\r')):
                        match = re.findall(".*Summary:(.*).*", line.strip().replace('\r', ','))
                        for x in match:
                            summary = match[0]
                        return str(summary)
                else:
                    match = re.findall(".*Summary:(.*).*", line.strip())
                    for x in match:
                        summary = match[0]
                    return str(summary)

    def get_description(self, issuefile):  #
        try:
            lines = open(issuefile).readlines()
        except:
            lines = open(issuefile, encoding="gbk").readlines()
        for line in lines:
            match = re.findall(".*Description:(.*).*", line.strip().replace('\r', ','))
            for x in match:
                description = match[0]
                return description

    def get_appversion(self, issuefile):  #
        try:
            lines = open(issuefile).readlines()
        except:
            lines = open(issuefile, encoding="gbk").readlines()
        for line in lines:
            if "packageVersion:" in line:
                try:
                    app_version = line.strip().split("packageVersion:")[1]
                    return app_version
                except Exception:
                    return "None"
        else:
            return "None"

    def get_priority(self,issuefile):
        try:
            lines = open(issuefile).readlines()
        except:
            lines = open(issuefile, encoding="gbk").readlines()
        for line in lines:
            if "priority:" in line:
                try:
                    app_version = line.strip().split("priority:")[1]
                    return app_version
                except Exception:
                    return "Major"
        else:
            return "Major"

    def get_chiptype(self):
        project = self.getprop("ro.product.name")
        chipset = int(self.getprop("ro.boot.mi.panel_size"))
        # 在此添加含有chipset的jira项目
        if 'dangal' in project and chipset in [32,40,49]:
            print("=================================950X")
            return "950X"
        elif project == 'dangal' and chipset == 43:        #dangalUHD 43寸是960X
            print("=================================950X")
            return "950X"
        elif 'dangal' in project and chipset in [43,50,55]:
            print("=================================960X")
            return "960X"

        elif "amelie" in project and chipset == 32:
            print("==============================amelie 6683")
            return [6683, 32]
        elif "nino" in project:
            print("==============================nino 6886")
            return [6886, chipset]
        elif "volver" in project and chipset == 32:
            print("==============================volver 6683")
            return [6683, 32]
        elif "machuca" in project:
            print("==============================machuca 6886")
            return [6886, chipset]
        else:
            print("can not get chip type.")

    def createIssue(self, folder, issuefile,logstack):
        print("check issuefile:",issuefile)
        casefolder = issuefile.split("/")[-2]
        print("check case folder:",casefolder)
        # project_name = self.getprop("ro.build.product")
        # project_name = self.getprop("ro.product.name")   # 与getIssueInfo保持一致
        project_name = self.product_name
        try:
            if isinstance(project_name,str) and "_" in project_name:
                jira_project_name = jira_project_dict[project_name.split("_")[0]]
            else:
                jira_project_name = jira_project_dict[project_name]   # jira上对应项目库的名字
        except Exception as e:
            print(traceback.print_exc())
            print("project name not in jira_project_dict, please update jira name.")
            jira_project_name = project_name   # 先用着读到的项目名，后面再改
            # self.feishu.send_text_message("项目：{} 找不到对应的jira库名，请更新。\n任务地址：{}".format(project_name,self.build_url),self.feishu_user)
            self.feishu.send_message_interactive(receiver="heyingmei",
                                                 subject="自动化压测解析通知",
                                                 message_items={"解析任务地址": self.build_url,
                                                                "备注": "项目：{} 找不到对应的jira库名，请更新。".format(project_name)},
                                                 subject_background_color="blue")

        try:
            package = get_package(issuefile)
            product = self.getprop("ro.build.product")     # 项目名称(不带分支)  # ro.product.name
            __component,__app_name = self.components.get_component(package)    # 分配一个模块，一定会有返回值，如果匹配不到，会返回BSP-default模块

            if __component == "BSP-default":
                if __app_name in self.installed_3apps:
                    __component = "APP-第三方应用"
                elif issuefile.endswith(".fc"):   # 如果是FC 匹配不到一个合适的模块，则分配给BSP-Framework
                    __component = "BSP-Framework"
                else:
                    __component = "BSP-稳定性"
            assignee = self.components.get_assignee(__component,product)   # 稳定性和framework需要根据项目名assign 负责人
            print("check return componet:",__component,__app_name)

            if __component == "BSP-小米播放器-MTK":
                if self.TV_Product_Model == "Amlogic":
                    __component = "BSP-小米播放器-AML"
                assignee = self.components.get_assignee(__component,product)
                if __component == "BSP-小米播放器-MTK" and issuefile.endswith(".video_loading"):    # MTK项目的播放下载失败的assign给薛晋伟 2025.3.17 彭阿桢
                    assignee = "xuejinwei"

                if issuefile.endswith(".video_screen"):    # 非小米播放器压测的黑屏相关的assign
                    if "ATV" in casefolder or "DTMB" in casefolder:   # 这个issuefile的路径包含casefolder
                        assignee = "songyimeng"

                    elif "AV" in casefolder or "HDMI" in casefolder:
                        if __component == "BSP-小米播放器-MTK":   # MTK 非miplayer压测，黑屏给朱翔
                            assignee = "zhuxiang"
                        else:                                   # aml 非miplayer，黑屏给朱翔
                            assignee = "zhangxiaoxv"


                print("miplayer error :{},{}".format(__component,assignee))

            if __component in ["autotest", "ignore_packages"]:
                print("===========It is an %s issue, skip===========" % __component)
                return None
            elif is_uiautomator(issuefile) or is_uiautomator(logstack):    # 过滤掉UiAutomation的bug
                print("Uiautomator error , skip.")
                return None
            elif self.get_commit_status(issuefile) == "No":   # monkey中的已知bug，不再提交
                print("===========monkey Knowns issue, ignore it.===========")
                return None
            else:
                # project_key = self.get_projectname()
                description = "任务发起人:%s\n" % self.build_user
                description += "package:" + package + "\n"
                description += "packageVersion:" + self.get_appversion(issuefile) + "\n"
                description += self.get_description(issuefile)
                priority = self.get_priority(issuefile)
                issue_dict = {
                    'project': {'key': jira_key},   # 项目名 必填项
                    'summary': '[自动化][{}][{}][{}][{}]{}'.format(self.device_type,self.testtype_chname,project_name,self.prop.get_tv_model(),self.get_summary(issuefile)),  # 概要 必填项
                    'issuetype': {'name': 'Bug'},    # 问题类型 必填项
                    'components': [{'name': __component}],    # 模块 必填项
                    'priority': {'name': priority},    # 优先级 必填项
                    'customfield_10817': {'value': 'only once'},  # 重现率
                    'customfield_18202': self.prop.get_version(),  # Rom版本  (version)
                    'customfield_34400': jira_project_name,  # 电视项目
                    'customfield_20200': [{'value': '自动化'}],
                    'versions': [{'name': 'None'}],   # 影响版本 必填项
                }

                if assignee:    # 如果负责人不是None，则指派一个负责人
                    issue_dict["assignee"] = {'name': assignee}

                # if __component == "MIBOX-异常重启":
                #     issue_dict["assignee"] = {"name":self.build_user}

                # jira优先级规则定义
                criticaltype = ["reboot_systemserver","reboot_kernel","system_server_hung","dead_system"]
                str_array = issuefile.split(".")
                ISSUE_TYPE = str_array[len(str_array) - 1]
                if ISSUE_TYPE in criticaltype:
                    issue_dict['priority']['name'] = 'Critical'
                if 'dangal' in project_name:   # 模糊匹配 dangal,dangalUHD,dangalFHD
                    chiptype = [{'value': self.get_chiptype()}]
                    description += "\n芯片类型：{}".format(chiptype)
                if 'machuca' in project_name or 'volver' in project_name or 'amelie' in project_name or 'nino' in project_name:   # 模糊匹配（包括分支）
                    chiptype = self.get_chiptype()
                    description += "\nChipset-MITV:Mstar {} inch".format(chiptype)

                issue_dict['description'] = description
                print("当前需要提交jira的信息有：",issue_dict)
                # 必填项属性均不为空再创建bug  必填项中其他三项都有初始值，不会为空，只有component项有可能为空
                new_issue = None
                if issue_dict[u'components'][0]['name']:
                    try:
                        time.sleep(3)
                        new_issue = self.jira.create_issue(fields=issue_dict)
                        url = '%s/browse/%s ' % (addr, new_issue.key)
                        print('生成Jira地址: ' + url + "\n\n")
                    except Exception as e:
                        print("提交jira出错，出错原因：{}".format(e))
                        # self.feishu.send_text_message("自动化测试提交jira issue出现错误，重新提交jira。\n" +
                        #                          "定位出错文件路径：{}\n".format(issuefile) +
                        #                          "错误原因：try to create issue again\n" +
                        #                          "任务地址:{}".format(self.build_url),self.feishu_user)
                        self.feishu.send_message_interactive(receiver="heyingmei",
                                                        subject="自动化压测解析通知",
                                                        message_items={"解析任务地址": self.build_url,
                                                                       "定位出错文件路径":issuefile,
                                                                       "备注": "自动化测试更新jira issue相关字段出现错误。"},
                                                        subject_background_color="blue")

                        # 如果提交jira失败，大概原因会出在，1、模块名无法对应；2、分配issue的负责人在MITVAUTO没有权限
                        # 则直接将模块名改为BSP-default 由冬梅手动指派
                        # issue_dict['components'][0]['name'] = "BSP-default"
                        # issue_dict["assignee"] = {'name': 'xudongmei1'}
                        if __component == "MIBOX-异常重启":  # 可能是assignee匹配不到，提单就失败了
                            if assignee:
                                issue_dict["assignee"] = {'name': assignee}  # 按照项目匹配模块负责人
                            else:
                                issue_dict.pop("assignee", None)  # 别的情况走默认吧
                        # 重新create issue一次
                        time.sleep(3)
                        new_issue = self.jira.create_issue(fields=issue_dict)   # 一般是报HTTP 500服务器无响应的问题，再次尝试提单
                        url = '%s/browse/%s ' % (addr, new_issue.key)
                        print('生成Jira地址: ' + url + "\n\n")
                    finally:
                        # 创建issue后直接使用add_comment()函数添加服务器log地址
                        # 在issue上添加log地址
                        time.sleep(3)
                        folderlist = folder.split("/")
                        relative_path = folderlist[-2] + "/" +folderlist[-1] + ".zip"
                        self.jira.add_comment(new_issue.key,"Logs url(复制链接至新网页直接下载）: {}\n "
                                                        "压测任务url：{}\n"
                                                        "压测使用 {}网络\n"
                                                        "压测电视mn号：{}\n"
                                                        "若没有访问权限小米融合云(FDS)日志，先登录后，再找@heyingmei开通权限".format(os.path.join(fds_Link,self.testType, relative_path),self.build_url,self.network_,self.assm_mn))
                        buglist = open(self.logFolder + '/bug.list', 'a+')
                        buglist.writelines("<a href=\"" + url + "\">" + new_issue.key + "</a><span class=\"failCount\">&nbsp&nbsp(count)&nbsp&nbsp</span>" + '\n')
                        buglist.close()

                        # todo record bugid & issue file
                        add_log(self.record_jira,"jira_id:{},issue file:{}\n".format(new_issue.key,issuefile))

                        return {'url': url, 'issue_key': new_issue.key}
                else:  # 应该不会出现找不到模块名称的情况，如果找不到模块会分配一个默认模块
                    print("===================================================="
                          "some issue_dict values are invaild, skip creat issue"
                          "====================================================")
                    # self.feishu.send_text_message("自动化测试提交jira issue出现错误。\n" +
                    #                          "定位出错文件路径：{}\n".format(issuefile) +
                    #                          "错误原因：some issue_dict values are invaild, skip creat issue\n"+
                    #                          "任务地址:{}".format(self.build_url),self.feishu_user)
                    self.feishu.send_message_interactive(receiver="heyingmei",
                                                         subject="自动化压测解析通知",
                                                         message_items={"解析任务地址": self.build_url,
                                                                        "定位出错文件路径": issuefile,
                                                                        "备注": "自动化测试更新jira issue相关字段出现错误。"},
                                                         subject_background_color="blue")
                    return None
        except Exception as e:   # 提交jira出错
            print("提交jira出错:{}".format(e))
            # self.feishu.send_text_message("自动化测试提交jira issue出现错误。\n" +
            #                          "定位出错文件路径：{}\n".format(issuefile) +
            #                          "错误原因：{}\n".format(e) +
            #                          "任务地址:{}".format(self.build_url),self.feishu_user)
            self.feishu.send_message_interactive(receiver="heyingmei",
                                                 subject="自动化压测解析通知",
                                                 message_items={"解析任务地址": self.build_url,
                                                                "定位出错文件路径": issuefile,
                                                                "备注": "自动化测试更新jira issue相关字段出现错误。"},
                                                 subject_background_color="blue")
            return None

    def get_database(self,testType):
        """
        :param testType:  传入的testType，可能会传入None
        :return:
        """
        self.device_type = "整机"
        if os.path.isfile(os.path.join(self.logFolder,"test_info")):   # 以文件中的为准
            self.testType,self.testtype_chname,self.device_type,self.network_ = get_testchname(os.path.join(self.logFolder,"test_info"),testType)
            print("解析上报jira，本次压测使用 {}网络，电视：{}，测试类型：{}，测试类型中文名：{}".format(self.network_,self.device_type,self.testType,self.testtype_chname))
        else:
            self.get_database_(testType)   # 旧版的读database，适用于没有写入test_info的压测
            return
        db_name = "autotest"
        # if self.testType == "monkey":
        #     self.product_versions = mongo_collection._collection(collection_name="monkey",db_name=db_name)
        if self.testType == "smoke":
            self.product_versions = self.mongo_collection._collection(collection_name="smoke", db_name=db_name)
        else:
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)     # UAT压测的database全都用stability的

    def get_database_(self,testType):
        """
        通过测试类型，决定数据库，2024.9.12 UAT所有压测项的数据库都用stability，避免重复提交
        :param testType: 压测的类型：monkey、external_apps、stability
        :return: self.product_versions == 数据库
                 self.testtype_chname  == 压测的中文名
        """
        db_name = "autotest"
        if testType == "monkey"  or self.testType == "MIUITVmonkey" or self.testType == "Top22APPmonkey":
            self.product_versions = self.mongo_collection._collection(collection_name="stability",db_name=db_name)
            # self.product_versions = db['monkey']
            self.testtype_chname = "{}测试".format(testType)
        elif testType == "apps-switch":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['monkey']
            self.testtype_chname = "全局monkey测试"
        elif testType == "smoke":
            self.product_versions = self.mongo_collection._collection(collection_name="stability",db_name=db_name)
            self.testtype_chname = "Smoke测试"
        elif testType == "miplayer":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "小米播放器测试"
        elif testType == "smartshare":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "国内投屏测试"
        elif testType == "Audio-miplayer":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "小米播放器-音频稳定性测试"
        elif testType == "Video-miplayer":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "小米播放器-视频稳定性测试"
        elif testType == "TOP16-miplayer":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "小米播放器-TOP16三方应用测试"
        elif testType == "HDMI-stability":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "信号源稳定性测试"
        elif testType == "Display-stability":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "Display源稳定性测试"
        elif testType == "LiveTV":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "LiveTV测试"
        elif testType == "photonengine":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "光子引擎测试"
        elif testType == "connectivity":
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']   # 小米播放器压测走稳定性的db
            self.testtype_chname = "connectivity稳定性测试"
        else:   # stability
            self.product_versions = self.mongo_collection._collection(collection_name="stability", db_name=db_name)
            # self.product_versions = db['stability']
            self.testtype_chname = "稳定性测试"
        print("check testtypechname", self.testtype_chname)


if __name__ == '__main__':
    deviceID = argv[1]  # 电视设备id
    result_folder = argv[2]  # project result folder
    testType = argv[3]   # 区分测试类型，monkey / stability / external_apps / miplayer / smoke

    build_user = "unknown"
    build_url = " "
    if len(argv) >= 6:
        build_url = argv[4]
        build_user = argv[5]

    parser = LogParser(deviceID,result_folder,testType,build_url,build_user)
    parser.run_parse()

# 启动
# python3 UpdateIssue.py $device_id $project_result_folder $TESTTYPE [$BUILD_URL] [$BUILDER_USER]
# argv[1] : device_ID
# argv[2] : project result folder
# argv[3] : 测试类型：monkey / apps-switch / stability / miplayer / smoke
# argv[4] : Jenkins console link,可缺省   # 适用于不连内网的测试
# argv[5] : builder user，可缺省  # 适用于不连内网的测试
