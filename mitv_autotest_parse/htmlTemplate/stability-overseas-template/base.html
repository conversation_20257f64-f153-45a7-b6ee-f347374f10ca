<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <title>自动化测试结果</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="description" content="测试报告"/>

    <style type="text/css">

        body {
            font-size: 62.5%;
        }

        html > body {
            font-size: 10px;
        }

        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td {
            padding: 0;
            margin: 0;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
            table-layout: fixed
        }

        fieldset, img {
            border: 0;
        }

        img {
            display: block;
        }

        address, caption, cite, code, dfn, th, var {
            font-weight: normal;
            font-style: normal;
        }

        ol, ul {
            list-style: none;
        }

        caption, th {
            text-align: left;
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: normal;
            font-size: 100%;
        }

        q:before, q:after {
            content: '';
        }

        abbr, acronym {
            border: 0;
        }

        a {
            text-decoration: underline;
        }

        /*-----报告样式*/

        body {
            color: #4f6b72;
            background: #eff0f0;
        }

        #container {
            width: 1200px;
            margin: 0px auto;
        }

        #header {
            /* background: #DDDDDD; */
            height: 40px;
            width: 1200px;
            margin: 30px 0px 20px 0px;
            border-radius: 15px 15px 15px 15px;
            /* box-shadow: 3px 3px 4px #818181; */

        }

        #header span {
            margin: 0px 0px 0px 10px;
            font-size: 20px;
            line-height: 60px;
            font-family: 'Microsoft Yahei', SimHei, sans-serif;
            color: #CC0000;
            font-weight: bold;

        }

        #header a {
            font-size: 15px;
            font-weight: bold;
            line-height: 95px;
            margin-left: 460px;
        }

        #header a:link {
            color: blue;
            text-decoration: underline;
        }

        #header a:visited {
            color: purple;
            text-decoration: underline;
        }

        #header a:hover {
            color: orange;
            text-decoration: underline;
        }

        #main {
            width: 1200px;
            margin: 10px 0px 10px 0px;
        }

        #main_up {
            height: 400px;
            width: 1100px;
        }

        #subtitle_up {
            width: 1000px;
            height: 30px;
            margin-left: 10px;
            margin-bottom: 20px;
            font-size: 18px;
            line-height: 30px;
            font-family: 'Microsoft Yahei', SimHei, sans-serif;
            padding-left: 10px;
            /* background: #CCFFCC;
            box-shadow: 1px 1px 4px #818181;
            border-radius: 0px 0px 9px 9px; */
            font-weight: bold;
            color: #CC0000;
        }

        #subtittle_down {
            width: 1200px;
            margin-left: 10px;
            margin-bottom: 20px;
            font-size: 18px;
            height: 30px;
            line-height: 30px;
            font-family: 'Microsoft Yahei', SimHei, sans-serif;
            padding-left: 10px;
            /* background: #CCFFCC;
            box-shadow: 1px 1px 4px #818181;
            border-radius: 0px 0px 9px 9px; */
            font-weight: bold;
            color: #CC0000;
        }

        #summary_table {
            border: 5px solid white;
            margin: 0px 0px 0px 20px;

            font-family: 'Microsoft Yahei', SimHei, sans-serif;

            box-shadow: 3px 3px 3px #818181;
        }

        #summary tr {
            border: 1px solid #a5ada2;

        }

        #summary td {
            border: 1px solid #a5ada2;
            padding: 10px 10px 10px 10px;

        }

        .summary_head_td {
            background-color: #a9c79b;
            font-weight: bold;
            color: white;
            font-size: 15px;
        }

        .summary_td {
            background-color: #faf9ec;
            font-weight: bold;
            font-size: 13px;
        }

        .planCount {
            color: blue;
        }

        .execCount {
            color: green;
        }

        .noRunCount {
            color: red;
        }

        .passCount {
            color: green;
        }

        .failCount {
            color: red;
        }

        .summary_td a:link {
            color: blue;
            text-decoration: underline;
        }

        .summary_td a:visited {
            color: purple;
            text-decoration: underline;
        }

        .summary_td a:hover {
            color: orange;
            text-decoration: underline;
        }

        #casedetail select {
            margin-left: 25px;
        }

        #subtittle_down span {
            margin-left: 20px;
            font-size: 15px;
            color: #CC0000;
        }

        #main_down {
            /* height: 600px; */
            width: 1200px;
            margin-top: 0px;
        }

        .bugsTable{
            border: 5px solid white;
            margin: 15px 0px 0px 20px;
            font-family: 'Microsoft Yahei',SimHei,sans-serif;
            box-shadow: 3px 3px 3px #818181;
            width: 873px;
        }

        .bugsTable tr{
            border: 1px solid white;
            margin: 0px 0px 0px 20px;
        }

        .bugsTable td{
            font-size: 13px;
            border: 1px solid white;
            padding: 8px 8px 8px 8px;
            text-align: left;
            /*white-space:nowrap;*/
            overflow:hidden;
        }

        .bugsTable th{
            font-size: 15px;
            text-align: center;
            border: 1px solid white;
            padding: 8px 8px 8px 8px;
            background: #008B8B;
            font-weight: bold;
            color: white;
        }

        .caseTable {
            border: 5px solid white;
            margin: 15px 0px 0px 20px;
            font-family: 'Microsoft Yahei', SimHei, sans-serif;
            box-shadow: 3px 3px 3px #818181;
            width: 1200px;
        }

        .caseTable tr {
            border: 1px solid white;
            margin: 0px 0px 0px 20px;
        }

        .caseTable td {
            font-size: 13px;
            border: 1px solid white;
            padding: 8px 8px 8px 8px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;

        }

        .caseTable th {
            font-size: 15px;
            text-align: center;
            border: 1px solid white;
            padding: 8px 8px 8px 8px;
            background: #008B8B;
            font-weight: bold;
            color: white;
        }

        #fail td {
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break: break-all;
        }

        #ANR td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
            }

        #FC td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
        }

        #Tombstone td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
        }

        #NativeCrash td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
        }

        #Reboot td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
        }

        #DeadSystemException td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
        }

        #notrun td{
            background: #8a5266;
            color: white;
            font-weight: bold;
            word-break:break-all;
        }

        #skip td{
            background: #8a5266;
            color: white;
            font-weight: bold;
        }

        #pass td {
            background: #648566;
            color: white;
            font-weight: bold;
        }

        #tailer {
            margin-top: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        #tailer span {
            margin-top: 10px;
            font-size: 12px;
            line-height: 40px;
            color: #4e4c4d;
            font-family: 'Microsoft Yahei', SimHei, sans-serif;
        }

    </style>


    <script type="text/javascript">


        function changeData(selectValue) {

            var caseCount = document.getElementById("showAll").getElementsByTagName('tr').length;
            var passCount = document.getElementsByName('pass').length;
            var failCount = document.getElementsByName('fail').length;
            var skipCount = document.getElementsByName('skip').length;
            var notrunCount = document.getElementsByName('notrun').length;
            var faildetailCount = document.getElementsByName('faildetail').length;
            var fcCount = document.getElementsByName('FC').length;
            var anrCount = document.getElementsByName('ANR').length;
            var tbCount = document.getElementsByName('Tombstone').length;
            var rebootCount = document.getElementsByName('Reboot').length;
            var DeadSystemExceptionCount = document.getElementsByName('DeadSystemException').length;

            if (selectValue == 1) {
                for (var i = 0; i < caseCount; i++) {
                    document.getElementById("showAll").getElementsByTagName('tr')[i].style.display = "table-row";
                }
                for (var i = 0; i < faildetailCount; i++) {
                    document.getElementsByName('faildetail')[i].style.display = "none";
                }
                for(var i=0;i < failCount;i++){
                    document.getElementsByName('fail')[i].style.display = "table-row";
                }
                for(var i=0;i < notrunCount;i++){
                    document.getElementsByName('notrun')[i].style.display = "table-row";
                }
                for(var i=0;i < passCount;i++){
                    document.getElementsByName('pass')[i].style.display = "table-row";
                }

                for(var i=0;i < skipCount;i++){
                    document.getElementsByName('skip')[i].style.display = "table-row";
                }

                for(var i=0;i < fcCount;i++){
                    document.getElementsByName('FC')[i].style.display = "table-row";
                }

                for(var i=0;i < anrCount;i++){
                    document.getElementsByName('ANR')[i].style.display = "table-row";
                }

                for(var i=0;i < tbCount;i++){
                    document.getElementsByName('Tombstone')[i].style.display = "table-row";
                }

                for(var i=0;i < rebootCount;i++){
                    document.getElementsByName('Reboot')[i].style.display = "table-row";
                }

                for(var i=0;i < DeadSystemExceptionCount;i++){
                    document.getElementsByName('DeadSystemException')[i].style.display = "table-row";
                }

            } else if (selectValue == 2) {

                for (var i = 0; i < notrunCount; i++) {
                    document.getElementsByName('notrun')[i].style.display = "table-row";
                }
                for (var i = 0; i < faildetailCount; i++) {
                    document.getElementsByName('faildetail')[i].style.display = "none";
                }
                for (var i = 0; i < passCount; i++) {
                    document.getElementsByName('pass')[i].style.display = "none";
                }
                for (var i = 0; i < failCount; i++) {
                    document.getElementsByName('fail')[i].style.display = "table-row";
                }

                for (var i = 0; i < skipCount; i++) {
                    document.getElementsByName('skip')[i].style.display = "table-row";
                }

                for(var i=0;i < fcCount;i++){
                    document.getElementsByName('FC')[i].style.display = "table-row";
                }
                for(var i=0;i < anrCount;i++){
                    document.getElementsByName('ANR')[i].style.display = "table-row";
                }

                for(var i=0;i < tbCount;i++){
                    document.getElementsByName('Tombstone')[i].style.display = "table-row";
                }

                for(var i=0;i < rebootCount;i++){
                    document.getElementsByName('Reboot')[i].style.display = "table-row";
                }

                for(var i=0;i < DeadSystemExceptionCount;i++){
                    document.getElementsByName('DeadSystemException')[i].style.display = "table-row";
                }


            } else if (selectValue == 3) {
                for (var i = 0; i < passCount; i++) {
                    document.getElementsByName('pass')[i].style.display = "table-row";
                }
                for (var i = 0; i < failCount; i++) {
                    document.getElementsByName('fail')[i].style.display = "none";
                }

                for (var i = 0; i < skipCount; i++) {
                    document.getElementsByName('skip')[i].style.display = "none";
                }

                for (var i = 0; i < notrunCount; i++) {
                    document.getElementsByName('notrun')[i].style.display = "none";
                }
                for (var i = 0; i < faildetailCount; i++) {
                    document.getElementsByName('faildetail')[i].style.display = "none";
                }
                for(var i=0;i < fcCount;i++){
                    document.getElementsByName('FC')[i].style.display = "none";
                }

                for(var i=0;i < anrCount;i++){
                    document.getElementsByName('ANR')[i].style.display = "none";
                }

                for(var i=0;i < tbCount;i++){
                    document.getElementsByName('Tombstone')[i].style.display = "none";
                }

                for(var i=0;i < rebootCount;i++){
                    document.getElementsByName('Reboot')[i].style.display = "none";
                }

                for(var i=0;i < DeadSystemExceptionCount;i++){
                    document.getElementsByName('DeadSystemException')[i].style.display = "none";
                }

            } else {
                alert("没有case运行");
            }

        }


    </script>


    {% if javascript %}

    <script type="text/javascript">

        function toggle(id) {

            var tb = document.getElementById(id);

            if (tb.style.display == 'none')

                tb.style.display = 'table-row';

            else

                tb.style.display = 'none';

        }

    </script>

    {% endif %}


</head>
<body>

<div id="container">

    <div id="header">

        <span id="headSpan">Mitv稳定性测试报告</span>

        <a class="clr" href="http://jenkins.tv.xiaomi.srv/" target="_blank"
        > Click To Jenkins </a>

    </div>


    <hr color=#CC0000 SIZE=3>
    <div id="main">
        <div id="main_up">
            <div id="subtitle_up">测试结果总结</div>

            <div id="summary">
                <table id="summary_table">{% block summary %}{% endblock %}</table>
            </div>
            <div></div>
        </div>

        <div id="main_down">
            </br>
            </br>

            <div id="bugsdetail">
                <tr>
                    <div id="subtitle_up" >JIRA详情</div>
                    <table id="showAll"  class="bugsTable" >
                        <th style="width: 15%;">Jira号</th><th style="width: 50%;">概要</th><th style="width: 7%;">优先级</th><th style="width: 8%;">状态</th><th style="width: 10%;">经办人</th><th style="width: 10%;">发生次数</th>
                        {% block jira_info %}{% endblock %}
                    </table>
                    <div></div>
                </tr>
            </div>
            <br>
            <br>

            <div id="casedetail">
                <div id="subtitle_up" >CASE详情</div>
                <select onchange="changeData(this.value)" autocomplete="off">
                    <option name="all" value="1">显示全部测试用例</option>
                    <option name="onlyFail" value="2">仅显示失败用例</option>
                    <option name="onlyPass" value="3">仅显示成功用例</option>
                </select>

                <table id="showAll" class="caseTable">

                    <tr>
                        <th style="width: 5%;text-align:center;">序号</th>
                        <th style="width: 40%;">用例方法</th>
                        <th style="width: 40%;">用例中文描述</th>
                        <th style="width: 10%;">测试结果</th>
                        <th style="width: 14%;">用例执行时间</th>
                        <th style="width: 12%;">备注</th>
                    </tr>
                    {% block entry %}{% endblock %}
                </table>


            </div>
        </div>
    </div>
    <div id="tailer">
        <hr color=#CC0000 SIZE=2>
        <span> &copy 2016 - 2024, MiTV BSP Team, Email: <EMAIL></span>
    </div>
</div>
</body>
</html>
