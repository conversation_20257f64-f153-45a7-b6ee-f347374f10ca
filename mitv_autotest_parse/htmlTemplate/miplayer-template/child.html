{% extends "miplayer-template/base.html" %}

{% block summary %}

<tr>
    <td class="summary_head_td">测试执行人</td>
    <td class="summary_td">{{summaryData.build_user}}</td>
    <td class="summary_head_td">控制台输出</td>
    <td class="summary_td"><a href="{{summaryData.build_url}}" target="_blank">执行详情</a></td>
</tr>

<tr>
    <td class="summary_head_td">设备型号</td>
    <td class="summary_td">{{summaryData.model}}</td>
    <td class="summary_head_td">产品名称</td>
    <td class="summary_td">{{summaryData.product}}</td>
</tr>

<tr>
    <td class="summary_head_td">版本代号</td>
    <td class="summary_td">{{summaryData.id}}</td>
    <td class="summary_head_td">软件版本号</td>
    <td class="summary_td">{{summaryData.displayid}}</td>
</tr>

<tr>
    <td class="summary_head_td">安卓版本</td>
    <td class="summary_td">{{summaryData.ver}}</td>
    <td class="summary_head_td">软件编译类型</td>
    <td class="summary_td">{{summaryData.type}}</td>
</tr>

<tr>
    <td class="summary_head_td">设备串号/地址</td>
    <td class="summary_td">{{summaryData.serialno}}</td>
    <td class="summary_head_td">软件编译时间</td>
    <td class="summary_td">{{summaryData.date}}</td>
</tr>

<tr>
    <td class="summary_head_td">测试时间(小时)</td>
    <td class="summary_td">{{summaryData.exetime_sum}}</td>
    <td class="summary_head_td">问题发生次数</td>
    <td class="summary_td">{{summaryData.errorcount}}</td>
</tr>

<tr>
    <td class="summary_head_td">电视尺寸</td>
    <td class="summary_td">{{summaryData.panel}}</td>
    <td class="summary_head_td">MTBF值</td>
    <td class="summary_td"><span class="failCount">{{summaryData.mtbf}}</span></td>
</tr>

<!--<tr>-->
<!--    <td class="summary_head_td">设备IP/SN</td>-->
<!--    <td class="summary_td" colspan="3">{{summaryData.deviceIP}}</td>-->
<!--</tr>-->

<tr>
    <td class="summary_head_td">执行结果</td>
    <td colspan="3" class="summary_td">
        <span class="failCount">FC：{{summaryData.fc}} &nbsp&nbsp&nbsp
            ANR：{{summaryData.anr}} &nbsp&nbsp&nbsp
            TOMBSTONES：{{summaryData.tb}} &nbsp&nbsp&nbsp
            Reboot：{{summaryData.reboot}} &nbsp&nbsp&nbsp
            HDMI：{{summaryData.hdmiErr}} &nbsp&nbsp&nbsp
            MiPlayer：{{summaryData.playerErr}} &nbsp&nbsp&nbsp
            Wifi：{{summaryData.wifi}} &nbsp&nbsp&nbsp
        </span>
   </td>
</tr>

{% endblock %}

{% block jira_info %}
{% for jira_info in  jira_infos %}

<tr>
    <td style = "text-align:center;font-weight: bold;">{% if javascript %}<a href="{{jira_info.jira_url}}" target="_blank">{% endif %}{{jira_info.bugid}}{% if javascript %}</a>{% endif %}</td>
    <td>{% if jira_info.summary %}{% endif %}{{jira_info.summary}}</td>
    <td style = "text-align:center;">{% if jira_info.priority %}{% endif %}{{jira_info.priority}}</td>
    <td style = "text-align:center;">{% if jira_info.status %}{% endif %}{{jira_info.status}}</td>
    <td style = "text-align:center;">{% if jira_info.assignee %}{% endif %}{{jira_info.assignee}}</td>
    <td style = "text-align:center;">{% if jira_info.count %}{% endif %}{{jira_info.count}}</td>
</tr>

{% endfor %}
{% endblock %}

{% block entry %}

{% for entry in test_entries %}

<tr id="{{entry.ispass}}" name="{{entry.ispass}}" {% if javascript %} onclick="toggle('row{{forloop.counter}}')"{% endif %}>
    <td style = "text-align:center;">{{forloop.counter}}</td>
    <td>{% if entry.casename %}#{% endif %}{{entry.casename}}</td>
    <td>{% if javascript %}<a href="{{entry.caseurl}}" target="_blank">{% endif %}{{entry.casechname}}{% if javascript %}</a>{% endif %}</td>
    <td style = "text-align:center;">{{entry.ispass|upper}}</td>
    <td style = "text-align:center;">{{entry.exetime}}</td>
    <td>
        <div name="fc" {% if entry.fcCount == 0 %}style="display:none"{% endif %}>FC：{{entry.fcCount}}</div>
        <div name="tb" {% if entry.tombstoneCount == 0 %}style="display:none"{% endif %}>TOMBSTONE：{{entry.tombstoneCount}}</div>
        <div name="anr" {% if entry.anrCount == 0 %}style="display:none"{% endif %}>ANR：{{entry.anrCount}}</div>
        <div name="reboot" {% if entry.rebootCount == 0 %}style="display:none"{% endif %}>Reboot：{{entry.rebootCount}}</div>
        <div name="hdmi" {% if entry.hdmiCount == 0 %}style="display:none"{% endif %}>HDMI：{{entry.hdmiCount}}</div>
        <div name="playerErr" {% if entry.playerErrCount == 0 %}style="display:none"{% endif %}>MiPlayer：{{entry.playerErrCount}}</div>
        <div name="wifi" {% if entry.wifiCount == 0 %}style="display:none"{% endif %}>Wifi：{{entry.wifiCount}}</div>

    </td>

</tr>




{% if javascript %}<tr id="row{{forloop.counter}}" class="faildetail" name="faildetail"
                       {% if entry.ispass = "pass" %}style="display:none"
                       {% elif entry.ispass = "skip" %}style="display:none"
                       {% endif %}
><td></td><td colspan="3">

    <pre>{{entry.casestep}}{{entry.casecontent}}{{entry.failstack}}{% if entry.failreason %}<span style="color:#FF0000">{{entry.failreason}}</span>{% endif %}</pre>

        </td><td colspan="1">{% if entry.screencap %}<a href="{{entry.caseurl}}/{{entry.screencap}}" target="_blank">查看截图</a>{% endif %}</td></tr>{% endif %}


{% endfor %}

{% endblock %}
