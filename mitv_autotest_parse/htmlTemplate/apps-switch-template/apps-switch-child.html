{% extends "apps-switch-template/apps-switch-base.html" %}

{% block summary %}

<tr>
    <td class="summary_head_td">测试执行人</td>
    <td class="summary_td">{{summaryData.build_user}}</td>
    <td class="summary_head_td">控制台输出</td>
    <td class="summary_td"><a href="{{summaryData.build_url}}" target="_blank">执行详情</a></td>
</tr>

<tr>
    <td class="summary_head_td">设备型号</td>
    <td class="summary_td">{{summaryData.model}}</td>
    <td class="summary_head_td">产品名称</td>
    <td class="summary_td">{{summaryData.product}}</td>
</tr>

<tr>
    <td class="summary_head_td">版本代号</td>
    <td class="summary_td">{{summaryData.id}}</td>
    <td class="summary_head_td">软件版本号</td>
    <td class="summary_td">{{summaryData.displayid}}</td>
</tr>

<tr>
    <td class="summary_head_td">安卓版本</td>
    <td class="summary_td">{{summaryData.ver}}</td>
    <td class="summary_head_td">软件编译类型</td>
    <td class="summary_td">{{summaryData.type}}</td>
</tr>

<tr>
    <td class="summary_head_td">设备串号/地址</td>
    <td class="summary_td">{{summaryData.serialno}}</td>
    <td class="summary_head_td">软件编译时间</td>
    <td class="summary_td">{{summaryData.date}}</td>
</tr>

<tr>
    <td class="summary_head_td">测试时间(小时)</td>
    <td class="summary_td">{{summaryData.exetime_sum}}</td>
    <td class="summary_head_td">问题发生次数</td>
    <td class="summary_td">{{summaryData.errorcount}}</td>
</tr>

<tr>
    <td class="summary_head_td">电视尺寸</td>
    <td class="summary_td">{{summaryData.panel}}</td>
    <td class="summary_head_td">MTBF值</td>
    <td class="summary_td"><span class="failCount">{{summaryData.mtbf}}</span></td>
</tr>

<!--<tr>-->
<!--    <td class="summary_head_td">设备IP/SN</td>-->
<!--    <td class="summary_td" colspan="3">{{summaryData.deivceIP}}</td>-->
<!--</tr>-->

<tr>
    <td class="summary_head_td">执行结果</td>
    <td colspan="3" class="summary_td">
        <span class="failCount">FC：{{summaryData.fc}} &nbsp&nbsp&nbsp&nbsp
            ANR：{{summaryData.anr}} &nbsp&nbsp&nbsp&nbsp
            TOMBSTONES：{{summaryData.tb}} &nbsp&nbsp&nbsp&nbsp
            Reboot：{{summaryData.reboot}} &nbsp&nbsp&nbsp&nbsp
            HDMI：{{summaryData.hdmiErr}} &nbsp&nbsp&nbsp
            Wifi：{{summaryData.wifi}} &nbsp&nbsp&nbsp
        </span>
   </td>
</tr>

<!--<tr>-->
<!--    <td class="summary_head_td">Bug List</td>-->
<!--    <td colspan="3" class="summary_td">-->
<!--        bug list-->
<!--   </td>-->
<!--</tr>>-->

{% endblock %}


{% block jira_info %}
{% for jira_info in  jira_infos %}

<tr style="background-color: #faf9ec;">
    <td style = "text-align:center;font-weight: bold;">{% if javascript %}<a href="{{jira_info.jira_url}}" target="_blank">{% endif %}{{jira_info.bugid}}{% if javascript %}</a>{% endif %}</td>
    <td>{% if jira_info.summary %}{% endif %}{{jira_info.summary}}</td>
    <td style = "text-align:center;">{% if jira_info.priority %}{% endif %}{{jira_info.priority}}</td>
    <td style = "text-align:center;">{% if jira_info.status %}{% endif %}{{jira_info.status}}</td>
    <td style = "text-align:center;">{% if jira_info.assignee %}{% endif %}{{jira_info.assignee}}</td>
    <td style = "text-align:center;">{% if jira_info.count %}{% endif %}{{jira_info.count}}</td>
</tr>

{% endfor %}
{% endblock %}


<!--{% block performance_trends %}-->

<!--{% for entry in performance_trends %}-->

<!--<tr id="{{entry.ispass}}" name="{{entry.ispass}}" {% if javascript %} onclick="toggle('row{{forloop.counter}}')"{% endif %} {% if entry.ispass = "pass" %} style="background-color:#648566" {% else %} style="background-color:#8a5266;color: #eff0f0" {% endif %}><td >{{forloop.counter}}</td>-->
<!--    <td >{{entry.name}}</td>-->
<!--    <td >{{entry.ChineseName}}</td>-->
<!--    <td >{{entry.ispass}}</td>-->
<!--</tr>-->

<!--{% if javascript %}<tr id="row{{forloop.counter}}" class="faildetail" name="faildetail" {% if entry.ispass = "pass" %}style="display:none"{% endif %} ><td></td><td colspan="5">-->

<!--<pre>-->
<!--    &lt;!&ndash; <span class="planCount">CPU趋势图</span></br> &ndash;&gt;-->
<!--    <a href="{{entry.cpu_html}}" title = "点击查看源文件">-->
<!--        <img src="{{entry.cpu_png}}" width=800px height=400px />-->
<!--    </a>-->
<!--    <hr color=#CC0000 SIZE=2>-->
<!--    &lt;!&ndash; <span class="planCount">Mem趋势图</span></br> &ndash;&gt;-->
<!--    <a href="{{entry.mem_html}}" title = "点击查看源文件">-->
<!--        <img src="{{entry.mem_png}}" width="800px" height="400px" />-->
<!--    </a>-->

<!--</pre>-->

<!--{% endif %}-->



<!--{% endfor %}-->

<!--{% endblock %}-->
