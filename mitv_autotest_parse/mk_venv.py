import os
import time
import requests

WORKSPACE =  os.path.abspath(os.path.dirname(__file__))
VENV_PACKAGE = 'https://mitv-autotest.cnbj1.mi-fds.com/mitv-autotest/resource/venv.zip'
SAVE_TO = 'venv.zip'


class Venv(object):
    def __init__(self):
        pass

    def run(self):
        venv_path = os.path.join(WORKSPACE, 'venv')
        if os.path.exists(venv_path):
            print('clear cache venv')
            os.system(f'rm -rf {SAVE_TO} {venv_path}')
            time.sleep(3)
        self.download_venv_package()

    def download_venv_package(self):
        zip_f = os.path.join(WORKSPACE, SAVE_TO)
        resp = requests.get(VENV_PACKAGE)
        if resp.status_code == 200:
            with open(zip_f, 'wb') as f:
                f.write(resp.content)
        if os.path.exists(zip_f):
            print(f'{zip_f} download success')
            self.unzip(zip_f, WORKSPACE)

    def unzip(self, zip_path, extract_path):
        result = os.popen(f'unzip {zip_path}').read()
        with open('unzip.log', 'w') as f:
            f.write(result)
        time.sleep(3)
        if os.path.exists(extract_path):
            print('unzip success')
            return True

if __name__ == '__main__':
    venv = Venv()
    venv.run()
