# -*- coding: utf-8 -*-
import time

import humanize
import threading
import os
import math
import traceback
from subprocess import PIPE,Popen,STDOUT
from dateutil import parser
from filechunkio import FileChunkIO
from ks3.connection import Connection
import logging
import logging.handlers
# from mongo import *

AK = 'AKLTEP6OHcjXRkGdwhlL1Em_3A'
SK = 'OMjUxLvOePKWWyby4NE4x0nqEehSNRrqp9vXx4kizq0w6MGaRdktMvP4lsJr8rwPJg=='
HOST = 'ks3-cn-beijing.ksyun.com'
BUCKET = 'bkt-tj-bsp-ota-kscn'
RETRY_TIMES = 5
THREAD_NUM = 10
EXPIRE_TIME = 60 * 60 * 24 * 365 * 20
# todo 上传文件到金山云
conn = Connection(AK, SK, host=HOST)
b = conn.get_bucket(BUCKET)

class KsEngine(object):
    # def __init__(self, mongo_client, logger):
    def __init__(self):
        # self.client = mongo_client
        # self.logger = logger
        self.b = b

    def upload_part_task(self, mp, local_path, ks3_key, offset, chunk_size, part_num, retry_times, multi_task_result):
        cur_task_ret = False
        try:
            for i in range(retry_times):
                try:
                    with FileChunkIO(local_path, 'rb', offset=offset, bytes=chunk_size) as fp:
                        mp.upload_part_from_file(fp, part_num=part_num)
                    cur_task_ret = True
                    print("%s part %d upload success" % (ks3_key, part_num))
                    break
                except BaseException as e:
                    print("%s part %d fail,uploadid=%s,error=%s" % (ks3_key, part_num, mp.id, traceback.format_exc()))
                    if i + 1 >= retry_times:
                        print("%s part %d upload fail" % (ks3_key, part_num))
                        raise e
        except BaseException as e:
            cur_task_ret = False
        finally:
            multi_task_result[part_num] = cur_task_ret

    def single_put(self, object_key, file_path):
        k = self.b.new_key(object_key)
        data = {
            "_id": object_key,
        }
        ret = k.set_contents_from_filename(file_path)
        if ret and ret.status == 200:
            ksc_file_url = self.parse_path(object_key)
            print("%s single put success" % object_key)
            return ksc_file_url
        else:
            # if not self.mongo_key_exists(self.client.otaversion_faild, data):
            #     self.client.otaversion_faild.insert(data)
            return False

    def multi_put(self, object_key, file_path):
        f_size = os.stat(file_path).st_size
        mp = self.b.initiate_multipart_upload(object_key)
        if not mp:
            print("%s init multiupload error" % object_key)
            return False
        print("%s begin multipart upload,uploadid=%s" % (object_key, mp.id))
        chunk_size = 104857600
        chunk_count = int(math.ceil(f_size / float(chunk_size)))
        multi_task_result = {}
        data = {
            "_id": object_key,
        }
        try:
            threads = []
            for i in range(chunk_count):
                offset = chunk_size * i
                bs = min(chunk_size, f_size - offset)
                part_num = i + 1

                t = threading.Thread(target=self.upload_part_task, args=(
                mp, file_path, object_key, offset, bs, part_num, RETRY_TIMES, multi_task_result))
                threads.append(t)
                t.start()

            for t in threads:
                t.join()

            if len(multi_task_result) != chunk_count:
                raise RuntimeError(
                    "%s part miss,expect=%d,actual=%d" % (object_key, chunk_count, len(multi_task_result)))
            for item in multi_task_result.keys():
                if not multi_task_result[item]:
                    raise RuntimeError("%s part upload has fail" % object_key)
            ret = mp.complete_upload()
            if ret and ret.status == 200:
                ksc_file_url = self.parse_path(object_key)
                print("%s multipart upload success" % object_key)
                return ksc_file_url
            return False

        except BaseException as e:
            print("%s multipart upload failed" % object_key)
            # self.client.otaversion_faild.insert(data)
            if mp:
                mp.cancel_upload()
            print(e)
            return False

    def parse_path(self, file_key):
        paths = file_key.split("/")[2:]
        update_data = {
            "length": len(paths)
        }
        for idx, p in enumerate(paths):
            temp = {"level_%s" % idx: p}
            update_data.update(temp)

        k = self.b.get_key(file_key)
        if k:
            url = k.generate_url(EXPIRE_TIME)
            print("url:",url)
            update_data.update({
                "url": url,
                "size": humanize.naturalsize(k.size, gnu=True),
                "modify_time": parser.parse(k.last_modified).strftime("%Y/%m/%d, %H:%M:%S")
            })
            return url
        # self.client.otaversion_success.update({"_id": file_key}, {"$set": update_data}, upsert=True)

    def mongo_key_exists(self, coll, data):
        doc = coll.find_one(data)
        return doc

ks_engine = KsEngine()
def upload_ksyun(object_key,file_path,retry_times=RETRY_TIMES):

    upload_res = False
    for i in range(retry_times):
        try:
            k = b.get_key(object_key)
            if k:
                print("%s is already exists" % object_key)
                ks_engine.parse_path(object_key)
                if k.size == os.stat(file_path).st_size:    # 如果金山云上和本地的文件大小相等，则不用再传一次了，否则会再传一次
                    # if os.path.exists(file_path):
                    #     os.remove(file_path)
                    #     print("remove %s successfully" % file_path)
                    return
                print("file: %s, local and ksyun size not match, upload again" % file_path)

            if cal_size(file_path, 104857600):
                upload_res = ks_engine.multi_put(object_key, file_path)
            else:
                upload_res = ks_engine.single_put(object_key, file_path)
            break
        except Exception as e:
            if i + 1 >= retry_times:
                print("upload file: %s failed" % file_path)
                print(str(e))
                raise e
    # try:
    #     if upload_res and os.path.exists(file_path):
    #         os.remove(file_path)
    #         print("remove %s successfully" % file_path)
    # except Exception as e:
    #     print("remove %s failed, error: %s" % (file_path, e.message))

def initLogger(log_file):
    log_level = logging.INFO
    logger_ = logging.getLogger()
    logger_.setLevel(log_level)

    formatter = logging.Formatter('[%(asctime)s][%(levelname)s]: %(message)s')
    log_FileHandler = logging.handlers.TimedRotatingFileHandler(filename=log_file,
                                                                when='D',
                                                                interval=1,
                                                                backupCount=3)

    log_FileHandler.setFormatter(formatter)
    log_FileHandler.setLevel(log_level)
    logger_.addHandler(log_FileHandler)

    console = logging.StreamHandler()
    console.setLevel(log_level)
    console.setFormatter(formatter)
    logger_.addHandler(console)
    return logger_


def cal_size(path, threshold):
    size = os.stat(path).st_size
    return size > threshold

if __name__ == "__main__":
    source_folder = "test_folder"
    tar_folder = "freeguy_23.11.7.2012_testResult_28_20231109_105352/test_globaldimming_20231109_105400/test_globaldimming_20231109_105400.tar.gz"
    tar_filename = "test_globaldimming_20231109_105400.tar.gz"
    # Popen("tar -zcvf {} {}".format(tar_folder, source_folder),shell=True, stdout=PIPE, stderr=STDOUT)   # 打包文件
    # time.sleep(1)    # 不要一打包完就上传，会出错，给点时间写入文件
    # logger = initLogger(tar_folder)
    ks_engine = KsEngine()
    object_key = 'mitv/Display_stability/{}/{}'.format("freeguy_23.11.7.2012_testResult_28_20231109_105352",tar_filename)
    if cal_size(tar_folder, 104857600):
        upload_res = ks_engine.multi_put(object_key, tar_folder)
    else:
        upload_res = ks_engine.single_put(object_key, tar_folder)
