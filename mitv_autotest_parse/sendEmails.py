#!/usr/bin/python
# coding=utf-8

import os
import time
import json
import requests
from sys import argv
import socket
try:
    from common_method import *
except:
    from .common_method import *

class SendEmail():
    def __init__(self,device_id,result_folder,testtype,build_user=""):
        self.device_id = device_id
        self.result_folder = result_folder
        self.testtype, self.testtype_chname,self.device_type,self.network_ = get_testchname(os.path.join(self.result_folder, "test_info"),testtype)
        self.prop = Prop(self.result_folder, self.device_id)
        self.build_version = self.prop.get_version()
        self.project_name = self.prop.getprop("ro.build.product")
        self.build_user = None
        if build_user:
            if not build_user.endswith("@xiaomi.com"):
                self.build_user = build_user + "@xiaomi.com"
            else:
                self.build_user = build_user

    def get_host_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            ip = s.getsockname()[0]
        finally:
            s.close()
        return ip

    def is_KJY_PC(self):
        """
        判断是否是科技园的服务器，发送报告，科技园的服务器都还掉了，只有海外西三旗的服务器
        """
        local_ip = self.get_host_ip()
        if local_ip in ["*************", "************", "*************", "*************"]:
            return "oversea"
        else:
            return None

    def mail_sender(self):
        to_list = ['<EMAIL>']
        cc_list = ['<EMAIL>']
        cc_list.append(self.build_user)
        # html格式的文件名称一定不能为index,否则会报错
        html_content = open(os.path.join(self.result_folder,"report.html"), 'rb').read()
        subject = "[" + self.project_name + "][" + self.build_version + "] MiTV {}".format(self.testtype_chname)

        # subject = "[" + self.project_name + "][" + self.build_version + "] MiTV自动化测试报告"
        # if self.testtype == "monkey":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV Monkey测试报告"
        # if self.testtype == "apps-switch":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV全局Monkey测试报告"
        # if self.testtype == "MIUITVmonkey":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV MIUITV Monkey测试报告"
        # if self.testtype == "Top22APPmonkey":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV Top22三方应用Monkey测试报告"
        #
        # if self.testtype == "stability":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV稳定性测试报告"
        #
        # if self.testtype == "miplayer":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV小米播放器测试报告"
        # if self.testtype == "Audio-miplayer":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV Audio稳定性测试报告"
        # if self.testtype == "Video-miplayer":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV Video稳定性测试报告"
        # if self.testtype == "TOP16-miplayer":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV TOP16三方应用测试报告"
        #
        # if self.testtype == "HDMI-stability":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV信号源稳定性测试报告"
        # if self.testtype == "Display-stability":
        #     subject = "[" + self.project_name + "][" + self.build_version + "] MiTV Display稳定性测试报告"

        to_list = ', '.join(to_list)
        cc_list = ', '.join(cc_list)

        body = {
            "token": "<EMAIL>",  # Robot
            "subject": subject,
            "tos": to_list,
            "content": html_content,
            "html": True,
            "ccs": cc_list
        }
        files = {'attachFiles': ('report.html', html_content)}
        for i in range(3):
            send = requests.post('https://t.mioffice.cn/mailapi/mail', data=body, files=files)
            send_result = send.json()
            if send_result["status"] == "success":
                print(json.dumps(send_result, ensure_ascii=False, indent=4))
                print("Send successfully : %s." % i)
                break
            else:
                time.sleep(10)
        else:
            print("Report mail send failed 3 times.")
            print(json.dumps(send_result, ensure_ascii=False, indent=4))


if __name__ == '__main__':
    device_id = argv[1]
    result_folder = argv[2]
    testtype = argv[3]
    build_user = ""
    if len(argv) > 4:
        build_user = argv[4]
    sendEmail = SendEmail(device_id,result_folder,testtype,build_user)
    sendEmail.mail_sender()
