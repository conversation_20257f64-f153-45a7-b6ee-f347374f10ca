#! /usr/bin/env python
# -*- coding:UTF-8 -*-
import json
import sys,os,time,re
from dot_data.upload_to_ptf import UploadToPlatformField,UploadTestData
from tvadb.tv_adb import TvAdb,TvSysData
from mdb.app_info import DevAppDataCollection
from mjira.tv_jira import Jira
try:
    from common_method import *    # 进入mitv_autotest_parse目录后执行
except Exception as e:
    from .common_method import *    # 在mitv_autotest_parse目录外执行

tool_version = "2024.8.15"
jenkins_sublink = "jenkins.tv.xiaomi.srv"

class UploadTestInfos():
    def __init__(self,device_id,testtype,result_folder):
        self.device_id = device_id
        self.result_folder = result_folder
        self.data_fields = UploadToPlatformField(self.device_id)
        self.running = True
        self.upload_now = True     # 关键节点立刻上报
        self.tv = TvAdb()
        self.tv_sys_data = TvSysData()
        # self.jira_db = DevAppDataCollection()._collection(collection_name="jira_db",db_name="autotest")
        self.jira = Jira().login_jira()
        self.testtype, self.testtype_chname,self.device_type,self.network_ = get_testchname(os.path.join(self.result_folder, "test_info"), testtype)
        self.testtype_tool = {
            1:"monkeytest",
            2:"stability_py",
            14:"miplayertest",  #小米播放器测试
            17:"miplayertest",  #信号源稳定性测试
            18: "Display-stability",  # display 稳定性测试
            19:"connectivity",   # connectivity 稳定性测试
            20:"stability-overseas",  # 海外稳定性测试
            21:"LiveTV",   # LiveTV 海外
            22:"smartshare",  # 国内投屏专项测试
            23: "photonengine",  # 光子引擎测试
        }
        self.upload_log = os.path.join(self.result_folder,"upload_data.log")

    def upload_res_info(self):
        self.result_test_info = {
            "available_ram": 0,     # 剩余RAM
            "available_rom": 0,     # 剩余ROM
            "plan_case_sum": 0,     # 计划执行的case总数
            "finish_case_sum": 0,   # 已执行完成case总数
            "case_exetime": 0,  # case执行时长
            "bugs_info": {},        # bugs的jira信息(dict)
            "summary_info": {},  # 测试结果总结
            "cases_info":{}         # 每个case的信息(dict)
        }

        if os.path.isfile(self.upload_log):
            self.test_id = None
            self.run_mode = None
            upload_log = open(self.upload_log)
            upload_log_line = upload_log.readlines()
            for line in upload_log_line:
                if line.startswith("upload res:") and "test_id" in line:
                    test_id_pattern = "'test_id': '[A-Za-z0-9]+'"
                    test_id_number = '[A-Za-z0-9]+'
                    print(line)
                    upload_resdict = re.findall(test_id_pattern, line)[0]
                    self.test_id = re.findall(test_id_number, upload_resdict)[2]
                    break
                if line.startswith("run mode:"):
                    if "prod" in line:
                        self.run_mode = "prod"
                    else:
                        self.run_mode = "dev"
                    self.upload_data_to_ptf = UploadTestData(self.run_mode)
            if not self.test_id or not self.run_mode:
                print("can not load test id or run mode,exit.")
                exit()
        else:
            print("upload_data.log does not exist,exit.")
            exit()

        plan_pkgs_sum = 0
        if os.path.isfile(os.path.join(self.result_folder, "plan_testcase")):  # install & upgrade pkg finished
            plan_pkgs = open(os.path.join(self.result_folder, "plan_testcase"))
            plan_pkgs_lines = plan_pkgs.readlines()
            for pkg in plan_pkgs_lines:
                if pkg.startswith("#") or pkg.startswith("="):
                    continue
                plan_pkgs_sum += 1
            plan_pkgs_sum -= 1
            LOOP_O, LOOP_I = plan_pkgs_lines[0].split()  # 第一行写的是大小loop
            self.result_test_info["plan_case_sum"] = plan_pkgs_sum * int(LOOP_I) * int(LOOP_O)

        finish_case_sum = 0
        if os.path.isfile(os.path.join(self.result_folder, "test_schedule")):  # 用 isfile能动态判断某个文件是否生成
            finish_pkgs = open(os.path.join(self.result_folder, "test_schedule"))
            finish_case_lines = finish_pkgs.readlines()
            for line in finish_case_lines:
                if "SETUP" not in line and "END" not in line and "Finished" not in line and "FINISH" not in line:
                    finish_case_sum += 1
            self.result_test_info["finish_case_sum"] = finish_case_sum

        # todo while break ,upload result datas
        des_dir = os.path.join(self.result_folder, "report.html")
        if os.path.isfile(des_dir):
            test_result_status = 1
            fds_Link = "http://cnbj1-fds.api.xiaomi.net/mitv-autotest/"
            # report_url = self.build_url.split(self.start_test_info["jenkins_job_name"])[0]
            # report_url += self.start_test_info["jenkins_job_name"] + "/ws/" + self.result_folder + "/report.html"
            url_list = des_dir.split("/")
            for i in url_list:
                if "_testResult_" in i:
                    start_index = url_list.index(i)
                    des_dir = "/".join(url_list[start_index:])  # 如果是绝对路径，要把前面的去掉
                    break
            report_url = os.path.join(fds_Link,self.testtype,des_dir)
        else:
            test_result_status = 2
            report_url = "None"

        if os.path.isfile(os.path.join(self.result_folder,"Report_bugs.json")):
            with open(os.path.join(self.result_folder,"Report_bugs.json")) as bugs_file:
                bugsData = json.load(bugs_file)
                self.result_test_info["bugs_info"] = bugsData

        if os.path.isfile(os.path.join(self.result_folder, "summaryReport.json")):
            with open(os.path.join(self.result_folder, "summaryReport.json"), "r") as s_file:
                summaryData = json.load(s_file)
                self.result_test_info["summary_info"] = summaryData  # 测试结果总结
                self.result_test_info["case_exetime"] = summaryData["case_exetime"]
        if os.path.isfile(os.path.join(self.result_folder, "resultData.json")):
            report_size =  os.path.getsize(os.path.join(self.result_folder, "resultData.json"))
            print("report size:{} M".format(report_size))
            if isinstance(report_size,int) and report_size < 5 * 1024 * 1024:
                with open(os.path.join(self.result_folder, "resultData.json"), "r") as j_file:
                    resultData = json.load(j_file)
                    self.result_test_info["cases_info"] = resultData
            else:
                print("report.html is larger than the max reporter size,report result data to bigfish platform without cases info")    #报告太大了，不加case info了
        try:
            if self.tv.reconnect(device_id=self.device_id,timeout=60):
                self.result_test_info["available_ram"] = self.tv_sys_data.available_ram(self.device_id)
                self.result_test_info["available_rom"] = self.tv_sys_data.available_rom(self.device_id)
        except Exception:
            print("fail to read tv available_ram & available_rom")

        try:
            self.result_datas = self.data_fields.finish_field(tool_status=1, test_result_status=test_result_status,
                                                              log_url=report_url,
                                                              data=self.result_test_info)  # log_url字段填写的是报告的fds_url

            upload_res = self.upload_data_to_ptf.finish_data(self.result_datas,self.test_id)
            print("upload_result_info RES:", upload_res)
            add_log(self.upload_log,"reupload result info" +"\n" + time.ctime() + "\n" + str(self.result_datas))
            if self.result_folder and os.path.isfile(os.path.join(self.result_folder, "test_schedule")):
                add_log(os.path.join(self.result_folder, "test_schedule"), "TEST FINISH")     # 停止main进程的upload_info_ptf.py
        except Exception as e:
            pass

if __name__ == '__main__':
    device_id = sys.argv[1]
    testtype = sys.argv[2]
    result_folder = sys.argv[3]
    if device_id.endswith(":5555"):
        device_id = device_id.split(":5555")[0]
    u = UploadTestInfos(device_id,testtype,result_folder)
    u.upload_res_info()