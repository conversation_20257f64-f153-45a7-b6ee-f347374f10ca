# _*_ coding: utf-8 _*_
import linecache
import os,datetime
import logging
import traceback


def create_logger(logname):
    formatter = logging.Formatter(
        "[%(levelname).1s %(asctime)s.%(msecs)03d %(module)s:%(lineno)d] %(message)s",
        datefmt="%y%m%d %H:%M:%S")
    NAME = os.path.basename(logname)
    logger = logging.getLogger(NAME)
    logger.setLevel(logging.DEBUG)

    log_file_handler = logging.FileHandler(logname)
    log_file_handler.setLevel(logging.DEBUG)
    log_file_handler.setFormatter(formatter)
    logger.addHandler(log_file_handler)

    ch = logging.StreamHandler()
    ch.setFormatter(formatter)
    ch.setLevel(logging.DEBUG)
    logger.addHandler(ch)
    return logger

def add_log(logname,writeline,output = False):
    if output == True:
        # print(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'),logname,writeline)
        print(logname,writeline)
    log_file = open(logname,"a+",encoding='utf-8')
    log_file.write(writeline)
    log_file.write("\n")
    log_file.close()

def count_keyword(file,keyword):
    """统计某个关键字在某个文件中出现的次数"""
    counter = 0
    linecache.clearcache()
    log = linecache.getlines(file)
    for index, line in enumerate(log):
        if keyword in line:
            counter += 1
    linecache.clearcache()
    return counter

def decode_logcat(logcat_file):
    """
    log解码成utf8格式
    解码后的logcat file路径记为logcat_file
    :param logcat_path:
    :return:logcat_file
    """
    print("decode logcat to utf-8")
    allline = []
    with open(logcat_file, "rb") as log:
        for index, line in enumerate(log):
            try:
                line = line.decode('utf-8', 'ignore')
                allline.append(line)
            except Exception as e:
                print("decode error:{}".format(line))
                print(traceback.print_exc())
    with open(logcat_file, "w") as p:
        p.write("".join(allline))
    return logcat_file


if __name__ == '__main__':
    pass