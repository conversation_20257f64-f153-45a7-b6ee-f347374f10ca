# -*- coding:utf-8 -*-

import sys
import shlex
import subprocess
import unittest
from uiautomator import device as d
import time
from script.testcases.adb_command import *


def timeout_command(command):
    """
    python2 版popen没有timeout
    注意：adb的命令不一定带关键字 shell
    如过需要打印返回值到log中，output传入参数为True
    """
    try:
        state=Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
    except Exception as e:
        return ""
    return out

def execCLI(running_time = 43200):

    monkey_cmd = "adb shell monkey --throttle 2000 --pct-nav 40 --pct-majornav 40 --pct-syskeys 15 --pct-appswitch 5 --ignore-crashes --ignore-timeouts --ignore-security-exceptions --kill-process-after-error -v -v -v 99999999"
    Popen(monkey_cmd, shell=True, stdout=PIPE, stderr=STDOUT)     # 不等待执行完毕就往下走
    start_time = time.time()
    print("start monkey time:",time.ctime())
    while time.time() - start_time < running_time:   # 倒计时
        time.sleep(30)
    # cmd = 'adb shell am force-stop {}'.format(package_name)    # stop monkey
    # timeout_command(cmd)
    cmd = 'adb shell pkill monkey'
    timeout_command(cmd)
    cmd = 'adb shell busybox pkill com.android.commands.monkey'
    timeout_command(cmd)

class MonkeyTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.wakeup()
        back2home_page()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d.wait.update(timeout=10000)
        # d.press.home()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        for _ in range(4):
            d.press("back")
        d.wakeup()
        for _ in range(2):
            d.press.back()
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        for _ in range(2):
            d.press.back()
        d.press.home()
        time.sleep(5)


    def test_monkey(self):
        execCLI()