# -*- coding:utf-8 -*-
import time
import subprocess
from subprocess import PIPE, STDOUT, Popen
from time import sleep

from uiautomator2 import Device
from lxml import etree
import os
from script.smartshare.Miplay import *
# from Miplay import *
import unittest
import configparser


class Miracastshare(unittest.TestCase):
    """miracast"""

    def setUp(self):
        phone_id, tv_id = self.read_config()
        self.phone_id = phone_id
        self.remove_uiautomator()      # 先停止手机上原有的uiautomator，否则容易报错
        self.d_phone = Device(phone_id)
        print(self.d_phone.info)
        self.tv_id = tv_id

    def tearDown(self):
        self.adb_reconnect(device_id=self.tv_id)

    def remove_uiautomator(self):  #
        """重新初始化手机端的uiautomator2"""
        Popen('adb connect %s' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        time.sleep(3)
        Popen('adb -s %s shell /data/local/tmp/atx-agent server --stop' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell rm /data/local/tmp/atx-agent' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator.test' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s root' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()

    def read_config(self):
        cf = configparser.ConfigParser()
        cf.read("script/smartshare/config.ini", encoding="utf-8")
        try:
            phone_id = cf.get('devices_info', 'phone_id')
            tv_id = cf.get('devices_info', 'tv_id')
            print("phone id:{}".format(phone_id))
            print("tv id:{}".format(tv_id))
            return phone_id, tv_id
        except Exception as e:
            print("script/smartshare/config.ini does not exists")
            print(e)

    # def __init__(self, phone_id, tv_id, d_phone):
    #     self.phone_id = phone_id
    #     self.tv_id = tv_id
    #     self.d_phone = d_phone
        # self.miplayshare = Miplayshare(phone_id, tv_id, d_phone)

    # def tv_smartshare(self):
    #     Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
    #           stderr=STDOUT)
    #     time.sleep(5)
    #     Popen("adb -s %s shell am start -n com.xiaomi.mitv.smartshare/.MainActivity" % self.tv_id, shell=True, stdout=PIPE,
    #           stderr=STDOUT)
    #     time.sleep(5)
    #
    #
    # def tv_click(self):
    #     for i in range(2):
    #         self.d_tv(resourceId="com.xiaomi.mitv.smartshare:id/btn1").click()
    #         time.sleep(2)
    """手机miracast"""
    def miracast_phone2tv(self):
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                phone_name = get_device_id(self.phone_id)
                if phone_name == "cupid":
                    Popen("adb -s %s shell cmd statusbar expand-settings" % self.phone_id, shell=True,
                          stdout=PIPE, stderr=STDOUT).wait()
                    time.sleep(5)
                    # todo 调用d_phone函数检查是否存在resourceId为"miui.systemui.plugin:id/tile_label"的元素页面
                    # resource_id_to_check = "miui.systemui.plugin:id/tile_label"
                    # exists = self.check_resource_id_exists(resource_id_to_check)
                    # print(f"Element with resourceId {resource_id_to_check} exists:", exists)

                    if self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").exists:
                        print("触发下拉菜单成功")
                        time.sleep(2)
                        print('点击投屏按钮')
                        self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").click()
                        print('选择投屏电视')
                        time.sleep(10)
                        if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="miracast").exists:
                            # time.sleep(2)
                            print("手机端发现电视设备成功")
                            self.d_phone(resourceId="com.milink.service:id/tv_device_name",
                                         textStartsWith="miracast").click()
                            time.sleep(5)
                            phone_activity = timeout_command(
                                "adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                            if "com.milink.service/com.milink.ui.activity.DisplayDialogActivity" in phone_activity:
                                print(phone_activity)
                                print("手机端click电视设备失败，重新触发")
                            else:
                                print("手机端投屏成功")
                            break
                        else:
                            print("投屏list未找到电视，重新触发")
                    else:
                        print("手机端miplay投屏触发失败，重新触发")
                elif phone_name == "lmi":
                    # todo 启动设置内activity触发miracast投屏
                    Popen(
                        'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                        shell=True,
                        stdout=PIPE, stderr=STDOUT).wait()
                    time.sleep(5)
                    if self.d_phone(resourceId="android:id/title", text="打开投屏").exists():
                        # time.sleep(2)
                        self.d_phone(resourceId="android:id/title", text="打开投屏").click()
                        print("打开投屏ok")
                        time.sleep(10)
                        if self.d_phone(resourceId="com.milink.service:id/tv_device_name",
                                        textStartsWith="miracast").exists:
                            # time.sleep(2)
                            self.d_phone(resourceId="com.milink.service:id/tv_device_name",
                                         textStartsWith="miracast").click()
                            time.sleep(10)
                            phone_activity = timeout_command(
                                "adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                            if "com.milink.service/com.milink.ui.activity.DisplayDialogActivity" in phone_activity:
                                print(phone_activity)
                                print("手机端click电视设备失败，重新触发")
                            else:
                                print("手机端投屏成功")
                                break
                        else:
                            print("again again")
                    else:
                        print("again")
                elif phone_name == "haotian":
                    Popen(
                        'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                        shell=True,
                        stdout=PIPE, stderr=STDOUT).wait()
                    time.sleep(5)
                    try:
                        assert self.d_phone(resourceId="android:id/title", text="投屏").exists(), "fail to find button"
                        self.d_phone(resourceId="android:id/title", text="投屏").click()
                        time.sleep(15)
                        assert self.d_phone(resourceId="com.milink.service:id/tv_device_name",
                                            textStartsWith="miracast").exists, "fail to find tvname"
                        self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="miracast").click()
                        time.sleep(15)
                        phone_activity = timeout_command(
                            "adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                        if "com.milink.service/com.milink.ui.activity.DisplayDialogActivity" in phone_activity:
                            print(phone_activity)
                            print("手机端click电视设备失败，重新触发")
                            if self.check_tv():
                                print("手机端投屏成功1")
                                break
                        else:
                            print("手机端投屏成功")
                            break
                    except Exception as e:
                        print(e)
                        print("again restart")
                else:
                    print("current devices unsupported")
            else:
                print('三次运行手机端脚本失败')
                # out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                # self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")


    def tv_2miracast(self):
        Popen("adb -s %s shell am force-stop com.xiaomi.mitv.smartshare" % self.tv_id, shell=True,
              stdout=PIPE,
              stderr=STDOUT)
        time.sleep(2)
        Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True,
              stdout=PIPE,
              stderr=STDOUT)
        time.sleep(2)
        Popen("adb -s %s shell am start -n com.xiaomi.mitv.smartshare/.MainActivity" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        time.sleep(2)
        # Popen("adb -s %s shell input keyevent KEYCODE_DPAD_DOWN" % self.tv_id, shell=True, stdout=PIPE,
        #       stderr=STDOUT)
        # time.sleep(2)
        Popen("adb -s %s shell input keyevent KEYCODE_DPAD_RIGHT" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        time.sleep(2)
        Popen("adb -s %s shell input keyevent KEYCODE_DPAD_RIGHT" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        time.sleep(2)
        # Popen("adb -s %s shell input keyevent KEYCODE_DPAD_CENTER" % self.tv_id, shell=True, stdout=PIPE,
        #       stderr=STDOUT)



    def miracast_phonevideo(self):
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                Popen("adb -s %s shell am force-stop com.tencent.qqlive" % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                print("打开腾讯视频")
                Popen("adb -s %s shell am start -S com.tencent.qqlive/.ona.activity.SplashHomeActivity" % self.phone_id,
                      shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(10)
                # self.d_phone(text="跳过").click()
                # time.sleep(10)
                # todo 处理弹窗
                # self.d_phone.disable_popups()
                if self.d_phone(text='我知道了').exists:
                    self.d_phone(text='我知道了').click()
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").wait.exists()
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").exists:
                    print('成功进入腾讯视频')
                    time.sleep(3)
                # logger.info("点击播放一个视频")
                print("播放电视剧页面下视频")
                # assert self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").exists, "fail to find"
                try:
                    assert self.d_phone(resourceId="com.tencent.qqlive:id/arg",
                                        text="电视剧").exists, "fail to find dianshiju"
                    self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").click()
                    # self.d_phone.xpath('//*[@content-desc="全部剧集"]/android.view.ViewGroup[2]').click()
                    # self.d_phone.xpath('//androidx.viewpager.widget.ViewPager/android.widget.RelativeLayout[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/androidx.recyclerview.widget.RecyclerView[1]/android.widget.FrameLayout[2]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
                    # self.d_phone(text="全部剧集").click()
                    self.d_phone(className='android.view.ViewGroup', descriptionStartsWith=',').click()
                    break
                except Exception as e:
                    print(e)
                    print("again restart")
            else:
                print('三次运行手机端脚本失败')
                out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")


    def adb_reconnect(self,device_id):
        fail_reason = None
        ip = device_id.replace(":5555", "")
        Popen("adb  disconnect %s:5555" % ip, shell=True, stdout=PIPE, stderr=PIPE).wait()
        for i in range(6):
            reconnect_stat = Popen("adb connect %s" % device_id, shell=True, stdout=PIPE,
                                   stderr=STDOUT).stdout.read().strip()
            if "connected to %s:5555" == str(reconnect_stat):
                fail_reason = None
                break
            time.sleep(2)
        if "failed to connect to" in str(reconnect_stat):
            fail_reason = reconnect_stat
        if fail_reason:
            print(fail_reason)
            return False

        Popen("adb connect %s" % device_id, shell=True, stdout=PIPE, stderr=STDOUT).stdout.read()
        reconnect_stat = Popen("adb -s %s shell ls -d" % device_id, shell=True, stdout=PIPE,
                               stderr=STDOUT).stdout.read().strip()
        if reconnect_stat == b".":
            print("设备%s连接成功..." % ip)
            os.system("adb -s " + device_id + " root")
            time.sleep(3)
            return True
        else:
            fail_reason = "device %s : %s" % (ip, reconnect_stat)
            print(fail_reason)
            return False

    def testtv_2phonetime(self,timeout=300):
        print("phone tv:{}".format(self.tv_id, self.phone_id))
        for i in range(6):
            print("-time-{}".format(i))
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            print("运行电视端脚本")
            self.shell_tvtomiracast(source_file="script/smartshare/miracast.sh", shell_name="miracast.sh")
            print("运行手机端脚本")
            self.miracast_phone2tv()
            # self.miracast_phonevideo()
            print('-------------------电视投屏中，计时5min------------------')
            start_time = time.time()
            while time.time() - start_time < timeout:
                pass
            print("-------------------投屏断开，再次投屏------------------")
            print("等待15s电视自动回连网络")
            time.sleep(15)
            # todo 电视断开连接后 ip会断连
            self.adb_reconnect(device_id=self.tv_id)


            # self.adb_reconnect(device_id=self.tv_id)



    def shell_tvtomiracast(self, source_file ='script/smartshare/miracast.sh', shell_name="miracast.sh"):
        target_file = '/data/local/tmp'
        subprocess.Popen("adb -s {} push {} {}".format(self.tv_id, source_file, target_file), shell=True, stdout=PIPE,
                                   stderr=STDOUT).stdout.read().strip()
        cmds = ["cd /data/local/tmp", "chmod 777 {}".format(shell_name), "nohup ./{} >/dev/null 2>&1 &".format(shell_name)]
        pipe = subprocess.Popen("adb -s %s shell" % (self.tv_id), stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                shell=True)
        try:
            pipe.communicate("\n".join(cmds).encode() + b"\n", timeout=5)
        except Exception as exp:
            print(str(exp))


    def check_tv(self):
        # time.sleep(10)  #miracast投屏接收时间长 10s
        state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                      stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        tv_name = get_device_id(self.tv_id)
        if b'com.xiaomi.mitv.smartshare/.wfd.WfdPlayerActivity' in out:
            print("电视端接收投屏成功")
            return True
        elif b'com.xiaomi.mitv.smartshare/.wifidisplay.WfdPlayerActivity' in out:
            print("电视端接收投屏成功")
            return True
        else:
            print('电视端接收投屏失败，查看当前 tv activity或投屏协议')  # 手机端投过去了，但电视端没有接收
            # print(out)
            # self.assertFalse(out, msg="{'case_result':'FAIL','comment':'电视端接收投屏失败'}")


    def testphone_2tv_miracast(self):
        Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        print("运行电视端脚本")
        self.shell_tvtomiracast(source_file="script/smartshare/miracast_shortime.sh", shell_name="miracast_shortime.sh")
        print("运行手机端脚本")
        self.miracast_phone2tv()
        # self.miracast_phonevideo()
        # print("等待15s电视自动回连网络")
        # # todo 电视断开连接后 ip会断连
        print("start reconnect")
        time.sleep(20)
        self.adb_reconnect(device_id=self.tv_id)


    def test_ethernet_phone_2tv_miracast(self):
        success_count = 0
        for i in range(20):
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            print("运行电视端脚本")
            self.tv_2miracast()
            time.sleep(5)
            print("运行手机端脚本")
            self.miracast_phone2tv()
            time.sleep(10)
            result = self.check_tv()
            if result:
                success_count += 1
        pass_rate = (success_count / 20) * 100
        print(f"测试次数: 20")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏成功率'}")

    def test_run_miracast_discovery(self):
        success_count = 0
        for i in range(100):
            print(f"第{i+1}次发现")
            self.tv_2miracast()
            time.sleep(3)
            result = self.start_phone_smartmiracast(button_name="投屏")
            if result.get("status") == "found":
                success_count += 1
            else:
                print(result.get("message") )
        pass_rate = (success_count / 100) * 100
        # pass_rate = sum(1 for res in results if res["status"] == "found") / len(results) * 100
        # 输出结果
        print(f"测试次数: 100")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏发现率'}")

    def start_phone_smartmiracast(self, button_name):
        for retry in range(3):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 2:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                Popen(
                    'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                    shell=True,
                    stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                if self.d_phone(resourceId="android:id/title", text=button_name).exists():
                    self.d_phone(resourceId="android:id/title", text=button_name).click()
                    time.sleep(15)
                    if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="miracast").exists:
                        print("find")
                        return {"status": "found", "devices": "miracast"}
                    else:
                        print("again")
                else:
                    print("again restart")
            else:
                return {"status": "not_found", "message": "未发现Miracast投屏设备"}

    def test_huaweiphone(self):
        # self.d_phone(resourceId="com.android.systemui:id/tile_label", text="无线投屏").click()
        self.d_phone(resourceId="com.huawei.desktop.explorer:id/wd_list_item_name").click()


# if __name__ == '__main__':
#     phone_id = "d3b5087e"
#     tv_id = "*************"
#     d_phone = Device(phone_id)
    # d_tv = Device(tv_id)
    # c = Miracastshare(phone_id, tv_id, d_phone)
    # c.testtv_2phonetime()
    # c.miracast_phonevideo()

    # c.miracast_phone2tv()
#
#     # c.start_shell()
#     c.miracast_phone2tv()
#     c.miracast_phonevideo()
#     time.sleep(20)
#     c.adb_reconnect(device_id)
