# -*- coding:utf-8 -*-
import time
from subprocess import PIPE, STDOUT, Popen
from uiautomator2 import Device
from lxml import etree
import unittest
import configparser
import os

monkey_cmd = "adb shell monkey --throttle 500 --pct-nav 40 --pct-majornav 40 --pct-syskeys 15 --pct-appswitch 5 --ignore-crashes --ignore-timeouts --ignore-security-exceptions --kill-process-after-error -v -v -v 99999999"
def timeout_command(command):
    """
    python2 版popen没有timeout
    注意：adb的命令不一定带关键字 shell
    如过需要打印返回值到log中，output传入参数为True
    """
    try:
        state=Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
    except Exception as e:
        return ""
    return out

def get_device_id(id):
    cmd = 'adb -s {} shell getprop ro.product.name'.format(id)
    device_id = timeout_command(cmd)
    return device_id

class Miplayshare(unittest.TestCase):

    # def __init__(self, phone_id, tv_id, d_phone):
    #     self.phone_id = phone_id
    #     self.tv_id = tv_id
    #     # self.tv_name = tv_name
    #     self.d_phone = d_phone

    def setUp(self):
        phone_id, tv_id = self.read_config()
        self.phone_id = phone_id
        self.remove_uiautomator()      # 先停止手机上原有的uiautomator，否则容易报错
        self.d_phone = Device(phone_id)
        print(self.d_phone.info)
        self.tv_id = tv_id

    def tearDown(self):
        Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
              stdout=PIPE, stderr=STDOUT).wait()


    def remove_uiautomator(self):  #
        """重新初始化手机端的uiautomator2"""
        Popen('adb connect %s' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        time.sleep(3)
        Popen('adb -s %s shell /data/local/tmp/atx-agent server --stop' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell rm /data/local/tmp/atx-agent' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator.test' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s root' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()

    def read_config(self):
        cf = configparser.ConfigParser()
        cf.read("script/smartshare/config.ini", encoding="utf-8")
        try:
            phone_id = cf.get('devices_info', 'phone_id')
            tv_id = cf.get('devices_info', 'tv_id')
            print("phone id:{}".format(phone_id))
            print("tv id:{}".format(tv_id))
            return phone_id, tv_id
        except Exception as e:
            print("script/smartshare/config.ini does not exists")
            print(e)

    def check_resource_id_exists(self,resource_id):
        # todo 使用UIAutomator的d_phone对象来查找具有指定resourceId的元素
        elements = self.d_phone(resourceId=resource_id)
        # todo 检查是否至少找到了一个匹配的元素
        return len(elements) > 0


    """手机miplay"""
    def miplay_phone2tv(self):
        # todo 桌面下拉菜单触发miplay投屏
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                phone_name = get_device_id(self.phone_id)
                if phone_name == "cupid":
                    Popen("adb -s %s shell cmd statusbar expand-settings" % self.phone_id, shell=True,
                          stdout=PIPE, stderr=STDOUT).wait()
                    time.sleep(5)
                    # todo 调用d_phone函数检查是否存在resourceId为"miui.systemui.plugin:id/tile_label"的元素页面
                    # resource_id_to_check = "miui.systemui.plugin:id/tile_label"
                    # exists = self.check_resource_id_exists(resource_id_to_check)
                    # print(f"Element with resourceId {resource_id_to_check} exists:", exists)

                    if self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").exists:
                        print("触发下拉菜单成功")
                        time.sleep(2)
                        print('点击投屏按钮')
                        self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").click()
                        print('选择投屏电视')
                        time.sleep(10)
                        if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="mip").exists:
                            time.sleep(2)
                            print("手机端发现电视设备成功")
                            self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="mip").click()
                            time.sleep(8)
                            phone_activity = timeout_command(
                                "adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                            if "com.milink.service/com.milink.ui.activity.DisplayDialogActivity" in phone_activity:
                                print(phone_activity)
                                print("手机端click电视设备失败，重新触发")
                            else:
                                print("手机端投屏成功")
                                break
                        else:
                            print("投屏list未找到电视，重新触发")
                    else:
                        print("手机端miplay投屏触发失败，重新触发")
                elif phone_name == "lmi":
                    # todo 启动设置内activity触发miplay投屏
                    Popen(
                        'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                        shell=True,
                        stdout=PIPE, stderr=STDOUT).wait()
                    time.sleep(5)
                    try:
                        assert self.d_phone(resourceId="android:id/title", text="打开投屏").exists(), "fail to find dakaitouping"
                        self.d_phone(resourceId="android:id/title", text="打开投屏").click()
                        time.sleep(10)
                        assert self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="mip").exists, "fail to find tvname"
                        self.d_phone(resourceId="com.milink.service:id/tv_device_name",
                                     textStartsWith="mip").click()
                        time.sleep(10)
                        phone_activity = timeout_command(
                            "adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                        if "com.milink.service/com.milink.ui.activity.DisplayDialogActivity" in phone_activity:
                            print(phone_activity)
                            print("手机端click电视设备失败，重新触发")
                        else:
                            print("手机端投屏成功")
                            break
                    except Exception as e:
                        print(e)
                        print("again restart")
                elif phone_name == "haotian":
                    Popen(
                        'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                        shell=True,
                        stdout=PIPE, stderr=STDOUT).wait()
                    time.sleep(10)
                    try:
                        assert self.d_phone(resourceId="android:id/title", text="投屏").exists(), "fail to find button"
                        self.d_phone(resourceId="android:id/title", text="投屏").click()
                        time.sleep(10)
                        assert self.d_phone(resourceId="com.milink.service:id/tv_device_name",textStartsWith="mip").exists, "fail to find tvname"
                        self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="mip").click()
                        time.sleep(10)
                        phone_activity = timeout_command(
                            "adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                        if "com.milink.service/com.milink.ui.activity.DisplayDialogActivity" in phone_activity:
                            print(phone_activity)
                            print("手机端click电视设备失败，重新触发")
                        else:
                            print("手机端投屏成功")
                            break
                    except Exception as e:
                        print(e)
                        print("again restart")
                else:
                    print("current devices unsupported")
            else:
                print('三次运行手机端脚本失败')
                out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")


        # print('选择投屏电视')
        # time.sleep(3)
        # self.d_phone(resourceId="com.milink.service:id/tv_device_name", text="bai(miplay)").click()
        # activity_name = "com.milink.ui.activity.DisplayDialogActivity"
        # if out.decode('utf-8').split("/")[0] in activity_name:

        # if b'com.milink.service/com.milink.ui.activity.DisplayDialogActivity' in out:
        #     print("手机投屏页面触发成功")
        #     print('选择投屏电视')
        #     time.sleep(3)
        #     self.d_phone(resourceId="com.milink.service:id/tv_device_name", text="bai(miplay)").click()
        # else:
        #     print('手机投屏页面触发失败，查看当前 tv activity\n')  # 手机端没有投过去
        #     print(out)
        #     print("重新开始手机投屏")
        #     for i in range(2):
        #         self.miplay_phone2tv()



    def adb_reconnect(self,device_id):
        fail_reason = None
        ip = device_id.replace(":5555", "")
        Popen("adb  disconnect %s:5555" % ip, shell=True, stdout=PIPE, stderr=PIPE).wait()
        for i in range(3):
            reconnect_stat = Popen("adb connect %s" % device_id, shell=True, stdout=PIPE,
                                   stderr=STDOUT).stdout.read().strip()
            if "connected to %s:5555" == str(reconnect_stat):
                fail_reason = None
                break
            time.sleep(2)
        if "failed to connect to" in str(reconnect_stat):
            fail_reason = reconnect_stat
        if fail_reason:
            print(fail_reason)
            return False

        Popen("adb connect %s" % device_id, shell=True, stdout=PIPE, stderr=STDOUT).stdout.read()
        reconnect_stat = Popen("adb -s %s shell ls -d" % device_id, shell=True, stdout=PIPE,
                               stderr=STDOUT).stdout.read().strip()
        if reconnect_stat == b".":
            print("设备%s连接成功..." % ip)
            os.system("adb -s " + device_id + " root")
            time.sleep(3)
            return True
        else:
            fail_reason = "device %s : %s" % (ip, reconnect_stat)
            print(fail_reason)
            return False


    def check_tv(self):
        time.sleep(10)  #miplay投屏接收时间长 10s
        state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                      stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        if b'com.xiaomi.hyper_connect.tv/.miplay.activity.MiPlayerActivity' in out:
            print("电视端接收投屏成功")
            return True
        elif b'com.xiaomi.mitv.smartshare/.wifidisplay.WfdPlayerActivity' in out:
            print("电视端接收投屏成功")
            return True
        else:
            print('电视端接收投屏失败，查看当前 tv activity或投屏协议')  # 手机端投过去了，但电视端没有接收
            # self.assertFalse(out, msg="{'case_result':'FAIL','comment':'电视端接收投屏失败'}")


    def phone_playvideo(self):
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                Popen("adb -s %s shell am force-stop com.tencent.qqlive" % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                print("打开腾讯视频")
                Popen("adb -s %s shell am start -S com.tencent.qqlive/.ona.activity.SplashHomeActivity" % self.phone_id,
                      shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(2)
                # self.d_phone(text="跳过").click()
                # time.sleep(10)
                # todo 处理弹窗
                self.d_phone.disable_popups()
                if self.d_phone(text='我知道了').exists:
                    self.d_phone(text='我知道了').click()
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").wait.exists()
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").exists:
                    print('成功进入腾讯视频')
                    time.sleep(3)
                # logger.info("点击播放一个视频")
                print("播放电视剧页面下视频")
                assert self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").exists, "fail to find"
                try:
                    assert self.d_phone(resourceId="com.tencent.qqlive:id/arg",
                                        text="电视剧").exists, "fail to find dianshiju"
                    self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").click()
                except Exception as e:
                    print(e)
                time.sleep(10)
                # self.d_phone.xpath('//*[@content-desc="全部剧集"]/android.view.ViewGroup[2]').click()
                # self.d_phone.xpath('//androidx.viewpager.widget.ViewPager/android.widget.RelativeLayout[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/androidx.recyclerview.widget.RecyclerView[1]/android.widget.FrameLayout[2]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
                # self.d_phone(text="全部剧集").click()
                self.d_phone(className='android.view.ViewGroup', descriptionStartsWith=',').click()
                icon = self.d_phone(resourceId="com.tencent.qqlive:id/arg").child(
                    className="android.widget.FrameLayout", index=3)
                time.sleep(3)
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").exists:
                    print("toupingcg")
                print("等待广告120s")
                time.sleep(120)
                print("手机端点击小电视图标进入投屏界面")
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏")
                time.sleep(10)
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").exists:
                    self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").click()  # 点击小电视图标
                else:
                    print("手机端触发投屏图标失败")
                time.sleep(10)
                # todo 选择投屏设备 需要手动填坐标
                # element = self.d_phone.xpath(
                #     '//*[@resource-id="android:id/content"]/android.widget.FrameLayout[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[4]/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.widget.ImageView[1]')
                # element.click()
                # Popen("adb -s %s shell input tap 207 1383" % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT)
                # time.sleep(5)
                # todo 判断手机投屏
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="音量").exists:
                    print("手机端投屏成功")
                    break
                else:
                    print("手机可能未进入投屏界面或反应慢")
                    print("重新开始手机投屏")
            else:
                print('三次运行手机端脚本失败')
                # out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                # self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")


    def reset_tv(self):
        """投屏断开后电视返回桌面"""
        for i in range(2):
            time.sleep(2)
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)  # 暂时不判断电视回没回了
        print("投屏断开后电视返回桌面")


    def testphone_2tv_miplay(self):
        success_count = 0
        for i in range(20):
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            self.miplay_phone2tv()
            result = self.check_tv()
            if result:
                success_count += 1
            self.reset_tv()
            # todo 电视断开连接后 ip偶现会断连
            self.adb_reconnect(device_id=self.tv_id)
        pass_rate = (success_count / 20) * 100
        print(f"测试次数: 20")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏成功率'}")


    def testtv_2phone_longtime(self, timeout=600):
        # print("debuging toupingla")
        # print("phone tv:{}".format(self.tv_id, self.phone_id))
        for i in range(6):
            print("-time-{}".format(i+1))
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            self.miplay_phone2tv()
            self.check_tv()
            self.phone_playvideo()
            print('-------------------电视投屏中，计时10min------------------')
            start_time = time.time()
            while time.time() - start_time < timeout:
                pass
            self.reset_tv()
            #todo 电视断开连接后 ip偶现会断连
            self.adb_reconnect(device_id=self.tv_id)
            print("-------------------投屏断开，再次投屏------------------")


    def test_run_miplay_discovery(self):
        success_count = 0
        for i in range(100):
            result = self.start_phone_smartmiplay(button_name="投屏")
            print(result)
            if result.get("status") == "found":
                success_count += 1
                print(success_count)
        pass_rate = (success_count / 100) * 100
        # pass_rate = sum(1 for res in results if res["status"] == "found") / len(results) * 100
        # 输出结果
        print(f"测试次数: 100")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏发现率'}")




    def start_phone_smartmiplay(self, button_name):
        for retry in range(3):
            print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 2:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                Popen(
                    'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                    shell=True,
                    stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                if self.d_phone(resourceId="android:id/title", text=button_name).exists():
                    self.d_phone(resourceId="android:id/title", text=button_name).click()
                    time.sleep(10)
                    if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="mip").exists:
                        print("find")
                        return {"status": "found", "devices": "miplay"}
                    else:
                        print("again")
                else:
                    print("again restart")
            else:
                return {"status": "not_found", "message": "未发现Miplay投屏设备"}




    def test_miplay_find_devices(self, target_devices):
        phone_name = get_device_id(self.phone_id)
        print(phone_name)
        time.sleep(3)
        if phone_name == "cupid":
            Popen("adb -s %s shell cmd statusbar expand-settings" % self.phone_id, shell=True,
                  stdout=PIPE, stderr=STDOUT).wait()
            time.sleep(5)
            if self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").exists:
                print("触发下拉菜单成功")
                time.sleep(2)
                print('点击投屏按钮')
                self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").click()
                print('选择投屏电视')
                time.sleep(10)
                if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="mip").exists:
                    time.sleep(5)
                    target_devices = "miplay"
                    return {"status": "found", "devices": target_devices}
                else:
                    print("投屏list未找到电视，重新触发")
            else:
                print("手机端miplay投屏触发失败，重新触发")
        elif phone_name == "lmi":
            # todo 启动设置内activity触发miplay投屏
            Popen(
                'adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                shell=True,
                stdout=PIPE, stderr=STDOUT).wait()
            time.sleep(5)
            try:
                assert self.d_phone(resourceId="android:id/title",
                                    text="打开投屏").exists(), "fail to find dakaitouping"
                self.d_phone(resourceId="android:id/title", text="打开投屏").click()
                time.sleep(10)
                assert self.d_phone(resourceId="com.milink.service:id/tv_device_name",
                                    textStartsWith="mip").exists, "fail to find tvname"
                time.sleep(5)
                return {"status": "found", "devices": target_devices}
            except Exception as e:
                print(e)
                print("again restart")
        else:
            return {"status": "not_found", "message": "未发现DLNA投屏设备"}


# if __name__ == '__main__':
#     phone_id = "af915268"
#     d_phone = Device(phone_id)
#     tv_id = '*************'
#     m = Miplayshare(phone_id, tv_id, d_phone)
#     m.miplay_phone2tv()
#     m.check_tv()
#     m.reset_tv()
#     m.adb_reconnect(device_id=tv_id)
#     # m.testtv_2phonetime()
#     for i in range(100):
#         m.testphone_2tv_miplay()
    # m.miplay_phone2tv()
    # m.check_tv()
    # m.phone_playvideo()
    # resource_id_to_check = "miui.systemui.plugin:id/tile_label"
    # exists = m.check_resource_id_exists(resource_id_to_check)
    # print(f"Element with resourceId {resource_id_to_check} exists:", exists)