# -*- coding:utf-8 -*-
import time
import subprocess
from subprocess import PIPE, STDOUT, Popen
from uiautomator2 import Device
from lxml import etree
import os
from script.smartshare.Miplay import *
# from testcases.adb_command import *
import unittest
import configparser


def timeout_command(command):
    """
    python2 版popen没有timeout
    注意：adb的命令不一定带关键字 shell
    如过需要打印返回值到log中，output传入参数为True
    """
    try:
        state=Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
    except Exception as e:
        return ""
    return out

class Mishare(unittest.TestCase):
    """miracast&DLNA"""
    #
    # def __init__(self, phone_id, tv_id, d_phone):
    #     self.phone_id = phone_id
    #     self.tv_id = tv_id
    #     # self.tv_name = tv_name
    #     self.d_phone = d_phone

    def get_device_id(self):
        cmd = 'adb -s {} shell getprop ro.product.name'.format(self.tv_id)
        device_id = timeout_command(cmd)
        return device_id
        # print(self.d_phone.info())
       # p = subprocess.Popen('adb -s %s shell getprop ro.product.name' % self.tv_id, shell=True, stdout=PIPE,
       #        stderr=STDOUT).wait()
       # out, err = p.stdout.read().decode().strip()
       # print(out)
       # if p.returncode ==

        # tv_model = timeout_command(cmd)
        # if tv_model == "KUNL-250B":
        #     print("tv_name is {}".format(tv_model))

    def setUp(self):
        phone_id, tv_id = self.read_config()
        self.phone_id = phone_id
        self.remove_uiautomator()  # 先停止手机上原有的uiautomator，否则容易报错
        self.d_phone = Device(phone_id)
        print(self.d_phone.info)
        self.tv_id = tv_id

    def tearDown(self):
        pass


    def remove_uiautomator(self):  #
        """重新初始化手机端的uiautomator2"""
        Popen('adb connect %s' % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT).wait()
        time.sleep(3)
        Popen('adb -s %s shell /data/local/tmp/atx-agent server --stop' % self.phone_id, shell=True, stdout=PIPE,
              stderr=STDOUT).wait()
        Popen('adb -s %s shell rm /data/local/tmp/atx-agent' % self.phone_id, shell=True, stdout=PIPE,
              stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator' % self.phone_id, shell=True, stdout=PIPE,
              stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator.test' % self.phone_id, shell=True, stdout=PIPE,
              stderr=STDOUT).wait()
        Popen('adb -s %s root' % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT).wait()

    def read_config(self):
        cf = configparser.ConfigParser()
        cf.read("script/smartshare/config.ini", encoding="utf-8")
        try:
            phone_id = cf.get('devices_info', 'phone_id')
            tv_id = cf.get('devices_info', 'tv_id')
            print("phone id:{}".format(phone_id))
            print("tv id:{}".format(tv_id))
            return phone_id, tv_id
        except Exception as e:
            print("script/smartshare/config.ini does not exists")
            print(e)

    def miraplay_phone2tv(self):
        #todo 三方镜像投屏
        # todo 桌面下拉菜单触发miplay投屏
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                Popen("adb -s %s shell cmd statusbar expand-settings" % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                # todo 启动设置内activity触发miplay投屏
                # Popen('adb -s %s shell am start -n com.milink.service/com.milink.ui.setting.SettingActivity' % self.phone_id,
                #       shell=True,
                #       stdout=PIPE, stderr=STDOUT).wait()
                # time.sleep(2)
                # self.d_phone(resourceId="android:id/title", text="打开投屏").click()

                # todo 调用d_phone函数检查是否存在resourceId为"miui.systemui.plugin:id/tile_label"的元素页面
                # resource_id_to_check = "miui.systemui.plugin:id/tile_label"
                # exists = self.check_resource_id_exists(resource_id_to_check)
                # print(f"Element with resourceId {resource_id_to_check} exists:", exists)
                if self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").exists:
                    print("触发下拉菜单成功")
                    time.sleep(2)
                    print('点击投屏按钮')
                    self.d_phone(resourceId="miui.systemui.plugin:id/tile_label", text="投屏").click()
                    print('选择投屏电视')
                    time.sleep(10)
                    # device_id = self.tv_id.get_device_id()
                    cmd = 'adb -s {} shell getprop ro.product.name'.format(self.tv_id)
                    device_id = timeout_command(cmd)
                    print(device_id)
                    if device_id == "KUNL-250B":
                        if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="hw").exists:
                            time.sleep(2)
                            self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="hw").click()
                            print("手机端投屏成功")
                            break
                    elif device_id == "tcl_mt5879_cn_db" or "t950s_be30a2":
                        if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="tcl").exists:
                            time.sleep(2)
                            self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="tcl").click()
                            print("手机端投屏成功")
                            break
                    elif device_id == "VIDAA_TV":
                        if self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="his").exists:
                            time.sleep(2)
                            self.d_phone(resourceId="com.milink.service:id/tv_device_name", textStartsWith="his").click()
                            print("手机端投屏成功")
                            break
                        else:
                            print("投屏list未找到电视，重新触发")
                else:
                    print("手机端miracast投屏触发失败，重新触发")
            else:
                print('三次运行手机端脚本失败')
                out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")
                # time.sleep(2)
                # state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                #               stderr=STDOUT)
                # state.wait()
                # out = state.stdout.read().strip()
                # print(out)

    def smartshare_phone2tv(self):
        #todo 三方DLNA投屏

        # print(self.d_phone.info)
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                Popen("adb -s %s shell am force-stop com.tencent.qqlive" % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                print("打开腾讯视频")
                Popen("adb -s %s shell am start -S com.tencent.qqlive/.ona.activity.SplashHomeActivity" % self.phone_id,
                      shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(2)
                # self.d_phone(text="跳过").click()
                # time.sleep(10)
                # todo 处理弹窗
                self.d_phone.disable_popups()
                if self.d_phone(text='我知道了').exists:
                    self.d_phone(text='我知道了').click()
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").wait.exists()
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").exists:
                    print('成功进入腾讯视频')
                    time.sleep(3)
                # logger.info("点击播放一个视频")
                print("播放电视剧页面下视频")
                assert self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").exists, "fail to find"
                try:
                    assert self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").exists, "fail to find dianshiju"
                    self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="电视剧").click()
                except Exception as e:
                    print(e)
                time.sleep(10)
                # self.d_phone.xpath('//*[@content-desc="全部剧集"]/android.view.ViewGroup[2]').click()
                # self.d_phone.xpath('//androidx.viewpager.widget.ViewPager/android.widget.RelativeLayout[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/androidx.recyclerview.widget.RecyclerView[1]/android.widget.FrameLayout[2]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
                # self.d_phone(text="全部剧集").click()
                self.d_phone(className='android.view.ViewGroup', descriptionStartsWith=',').click()
                icon=self.d_phone(resourceId="com.tencent.qqlive:id/arg").child(className="android.widget.FrameLayout",index=3)
                time.sleep(3)
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").exists:
                    print("toupingcg")
                print("等待广告120s")
                time.sleep(120)
                print("手机端点击小电视图标进入投屏界面")
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏")
                time.sleep(10)
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").exists:
                    self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").click()  # 点击小电视图标
                else:
                    print("手机端触发投屏图标失败")
                time.sleep(10)
                # todo 选择投屏设备 需要手动填坐标
                # element = self.d_phone.xpath(
                #     '//*[@resource-id="android:id/content"]/android.widget.FrameLayout[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[4]/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.widget.ImageView[1]')
                # element.click()
                # Popen("adb -s %s shell input tap 207 1383" % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT)
                # time.sleep(5)
                # todo 判断手机投屏
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="音量").exists:
                    print("手机端投屏成功")
                    break
                else:
                    print("手机可能未进入投屏界面或反应慢")
                    print("重新开始手机投屏")
            else:
                print('三次运行手机端脚本失败')
                out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")


    def check_tv(self,check_activity):
        time.sleep(10)  # miplay投屏接收时间长 10s
        state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                      stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        if check_activity.encode('utf-8') in out:
            print("电视端接收投屏成功")
            return True
        else:
            print('电视端接收投屏失败，查看当前 tv activity或投屏协议\n')  # 手机端投过去了，但电视端没有接收
            self.assertFalse(out, msg="{'case_result':'FAIL','comment':'电视端接收投屏失败'}")
            # print(out)
            # try:
            # except Exception as e:
            #     print(str(e))
            #     print("电视投屏失败")
            # print('电视端重新接受投屏')

    def check_tv_agreement(self, check_agreement):
        if check_agreement == "hw_miracast":
            hw_miracast_activity = "com.huawei.homevision.deviceinteract/.windowmanager.activity.DisplayActivity"
            self.check_tv(check_activity=hw_miracast_activity)
        elif check_agreement == "hw_dlna":
            hw_dlna_activity = "com.huawei.mediacenter/.videoplayer.InternalVideoPlayer"
            self.check_tv(check_activity=hw_dlna_activity)
        elif check_agreement == "tcl_miracast":
            tv_name = get_device_id(self.tv_id)
            print(tv_name)
            if tv_name == "tcl_mt5879_cn_db":
                tcl_miracast_activity = "com.tcl.miracast/.SinkActivity"
                self.check_tv(check_activity=tcl_miracast_activity)
            else:
                self.check_tv(check_activity="com.tcl.MultiScreenInteraction_TV/com.hpplay.sdk.sink.business.BusinessActivity")
        elif check_agreement == "tcl_dlna":
            tcl_dlna_activity = "tcl.MultiScreenInteraction_TV/com.tcl.allcast.presentation.activity.PresentationActivity"
            self.check_tv(check_activity=tcl_dlna_activity)
        elif check_agreement == "his_miracast":
            his_miracast_activity = "com.ms.ucast.ucastapp/.sink.player.CastPlayerActivity"
            self.check_tv(check_activity=his_miracast_activity)



    def testphone_2hwtv_Miracast(self):
        # print("this is {} times".format(i + 1))
        timeout_command("adb -s %s shell input keyevent HOME" % self.tv_id)
        self.miraplay_phone2tv()
        self.check_tv_agreement(check_agreement="hw_miracast")
        time.sleep(10)


    def testphone_2tcltv_Miracast(self):
        timeout_command("adb -s %s shell input keyevent HOME" % self.tv_id)
        self.miraplay_phone2tv()
        self.check_tv_agreement(check_agreement="tcl_miracast")
        time.sleep(10)

    def testphone_2histv_Miracast(self):
        timeout_command("adb -s %s shell input keyevent HOME" % self.tv_id)
        self.miraplay_phone2tv()
        self.check_tv_agreement(check_agreement="his_miracast")



#
# if __name__ == '__main__':
    # phone_id = 'd3b5087e'
    # d_phone = u2.connect('d3b5087e')
    # d_phone = Device(phone_id)
    # tv_id = '10.189.139.40'
#
#     hw = Mishare(phone_id, tv_id, d_phone)
    # hw.smartshare_phone2tv()
    # hw_dlna_activity = "com.huawei.mediacenter/.videoplayer.InternalVideoPlayer"
    # hw.check_tv(check_agreement="hw_dlna", check_activity=hw_dlna_activity)
    # for i in range(200):
    #     print("this is {} times".format(i+1))
    #     timeout_command("adb -s %s shell input keyevent HOME" % tv_id)
    #     hw.miraplay_phone2tv()
    #     hw_miracast_activity = "com.huawei.homevision.deviceinteract/.windowmanager.activity.DisplayActivity"
    #     hw.check_tv_agreement(check_agreement="hw_miracast")
    #     time.sleep(10)


    # tcl.miraplay_phone2tv()
    # tcl_miracast_activity = "com.tcl.miracast/.SinkActivity"
    # tcl.check_tv_agreement(check_agreement="tcl_miracast")
    # cmd = "adb -s 10.189.137.155 shell getprop ro.product.name"
    # a = timeout_command(cmd)
    # print("aaaa:",a)
    # his = Mishare(phone_id, tv_id, d_phone)
    # for i in range(100):
    #     his.testphone_2histv_Miracast()
    # his.smartshare_phone2tv()
