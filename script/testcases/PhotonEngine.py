#!/usr/bin/python
# -*- coding:utf-8 -*-

import time
import unittest
import random
from uiautomator import device as d
from script.testcases.adb_command import *

keys = ['up', 'down', 'left', 'right']

PLAY_time = 300
PLAY_TIME = 60

class AppStartTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        try:
            d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
            d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
            d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
            d.watcher("EXIT_VST_THIRD_APP").when(
                textContains="退出").click(text="退出")
            d.watcher("EXIT_MOLI_THIRD_APP").when(
                textContains="退出").click(text="退出")
            d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
                textContains="确定退出电视猫视频？").click(text="确定")
            d.watcher("EXIT_XUNLEI_THIRD_APP").when(
                textContains="是否退出迅雷看看？").click(text="确定")
            d.watcher("INSTALL_NEW_VERSION").when(
                textContains="您要安装此应用的新版本吗？").click(text="取消")
            d.watcher("EXIT_SOHU_APP").when(
                textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
            d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
            d.watcher("PASS_VST_UPDATE1").when(
                textContains="下次更新").click(text="下次更新")
            d.watcher("PASS_TOGIC_UPDATE").when(
                packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
            d.watcher("PASS_VST_UPDATE2").when(
                textContains='根据国家现行政策规定').press('enter')
            d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
            d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
            d.watcher("PASS_STOP_PLAY").when(
                textContains='确认退出').click(text="确认退出")

            d.watcher("PASS_APP_Popup").when(text="按【返回】可以关闭弹窗哦").press('back')
            d.watcher("PASS_APPKEEP_Popup1").when(text="按返回键退出播放").press('back')
            d.watcher("PASS_APPKEEP_Popup2").when(
                textContains="访问您设备上的照片、媒体内容和文件吗？").press('enter')
            d.watcher("PASS_APPAIQIYI_Popup").when(
                textContains="按【返回键】").press('back')
            d.watcher("PASS_APPBilibili_Popup").when(
                text="用户协议及隐私政策 ").press('enter')
            d.watcher("PASS_APPQQmusic_Popup").when(
                textContains="我知道了").press('enter')
            d.watcher("PASS_APPPPsport_Popup").when(
                textContains="同意").press('enter')
            d.watcher("PASS_APPPPsport_Popup2").when(
                textContains="我知道了").press('enter')
            d.watcher("PASS_APPPPsport_Popup2").when(
                textContains="允许CIBN聚体育拨打电话和管理通话吗？").press('enter')
            d.watcher("PASS_APPCloudvideo_popup").when(
                textContains="再看看").press('enter')


            d.watcher(("Install app-uiautomator-test.apk")).when(textContains="app-uiautomator-test.apk").press.up.center.down.center()  # not work

            d.wakeup()
            time.sleep(10)
            back2home_page()

        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        # time.sleep(1)
        # d.press.home()
        except IOError as exp:
            print(str(exp))
            self.restart_uiautomator()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")

        # d.press.home()
        back2home_page()
        frozenxiaoai()


    def restart_uiautomator(self):
        for i in range(3):
            d.server.stop()
            time.sleep(5)
            d.server.start()
            time.sleep(5)

    def start_yunshiting(self):
        print("INSTRUMENTATION_STATUS: title=启动云视听极光应用")
        app_name = "com.ktcp.video/.activity.MainActivity"
        start_app(app_name)
        package = get_front_package()
        if "com.ktcp.video/.activity.PrivacyAgreementActivity" in package:
            adb_center()
            time.sleep(2)
        d(text='精选').wait.exists(timeout=6000)
        if d(text='精选').exists:
            print('start app success')
        time.sleep(20)

    def start_aiqiyi(self):
        print("INSTRUMENTATION_STATUS: title=启动银河奇异果应用")
        app_name = "com.gitvdemo.video/com.gala.video.app.epg.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意并继续").exists:  # 同意用户协议
            adb_center()
            time.sleep(2)
        # 存储权限
        package = get_front_package()
        if "com.android.permissioncontroller/.permission.ui.GrantPermissionsActivity" not in package:
            adb_center()
            time.sleep(2)
        print("退出广告页推送")
        adb_back()
        time.sleep(20)

    def start_kumiao(self):
        print("INSTRUMENTATION_STATUS: title=启动酷喵应用")
        app_name = "com.cibn.tv/com.youku.tv.home.activity.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意").exists:
            d(textContains="同意").click()
            # adb_center()
            time.sleep(2)
        if d(textContains="是否将优酷XL设置为开机自启动？").wait.exists(timeout=6000):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
        if d(textContains="立即更新").wait.exists(timeout=6000):
            d.press.back()
        # time.sleep(4)
        time.sleep(20)

    def start_xiaodianshi(self):
        print("INSTRUMENTATION_STATUS: title=启动云视听小电视应用")
        back2home_page()
        app_name = "com.xiaodianshi.tv.yst/.ui.main.MainActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # 处理弹窗
        if "com.xiaodianshi.tv.yst/com.xiaodianshi.tv.yst.ui.messagedialog.MessageDialogActivity" in package:
            adb_back()
        # 同意用户协议
        if "com.xiaodianshi.tv.yst/.ui.introduction.IntroductionActivity" in package:
            adb_center()
        d.press.down()
        d.wait.update(timeout=2000)
        time.sleep(20)

    def start_mango(self):
        print("INSTRUMENTATION_STATUS: title=启动芒果TV应用")
        app_name = "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意").exists:
            adb_center()
            time.sleep(2)
        adb_center()  # 跳过开机广告1
        played = 0
        adb_back()  # 跳过广告2
        time.sleep(20)

    def start_wasu(self):
        print("INSTRUMENTATION_STATUS: title=启动华数鲜时光应用")
        app_name = "com.ixigua.android.tv.wasu/com.ixigua.android.business.tvbase.base.app.schema.AdsAppActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").exists:
            adb_center()
            time.sleep(2)
        time.sleep(20)

    def start_kuai(self):
        print("INSTRUMENTATION_STATUS: title=启动云视听快TV应用")
        app_name = "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意并继续").exists:
            adb_center()
        time.sleep(20)

    def start_qqmusic(self):
        print("INSTRUMENTATION_STATUS: title=启动qq音乐应用")
        app_name = "com.tencent.qqmusictv/.examples.NewMainActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        for i in range(4):
            adb_center()
        time.sleep(20)

    def start_cctv(self):
        print("INSTRUMENTATION_STATUS: title=启动CCTV央视频应用")
        app_name = "com.newtv.cboxtv/com.newtv.host.LauncherActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意并继续").exists:
            adb_center()
        if d(textContains="允许").exists:
            adb_center()
        time.sleep(20)

    def start_netease(self):
        print("INSTRUMENTATION_STATUS: title=启动网易云音乐应用")
        app_name = "com.netease.cloudmusic.tv/com.netease.cloudmusic.app.LoadingActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意协议并进入").exists:
            adb_center()
        if d(textContains="允许").exists:
            adb_center()
        time.sleep(20)

    def start_dianshimao(self):
        print("INSTRUMENTATION_STATUS: title=启动云视听电视猫应用")
        app_name = "com.moretv.android/.StartActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        time.sleep(20)

    def start_child(self):
        print("INSTRUMENTATION_STATUS: title=启动儿童思维训练应用")
        app_name = 'com.lutongnet.nldmx/.MainActivity'
        start_app(app_name)
        [adb_center() for i in range(2)]
        package = get_front_package()
        print(package)
        time.sleep(20)


    def power_screen(self,times):
        longpress_power()
        adb_left()
        adb_center()
        time.sleep(times)
        press_power()
        check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
        if check_screen == 'true':
            return True
        else:
            time.sleep(5)
            press_power()



    def test_appstart(self):
        """
        遍历启动top8应用四轮，进息屏和屏保各15min
        """
        for i in range(3):
            print("INSTRUMENTATION_STATUS: CaseRound={}".format(i))
            self.start_yunshiting()
            back2home_page()
            self.start_aiqiyi()
            back2home_page()
            self.start_kumiao()
            back2home_page()
            self.start_xiaodianshi()
            back2home_page()
            self.start_mango()
            back2home_page()
            self.start_wasu()
            back2home_page()
            self.start_kuai()
            back2home_page()
            self.start_qqmusic()
            back2home_page()
            print("INSTRUMENTATION_STATUS: CaseRound {} finished".format(i))
        print("INSTRUMENTATION_STATUS: CaseStep=电视进入屏保十五分钟")
        time.sleep(900)
        print("INSTRUMENTATION_STATUS: CaseStep=电视进入息屏十五分钟")
        a = timeout_command('adb shell settings get system mitv.short.power.action')
        print(a)
        if a == '0':
            breathing_screen(900)
        else:
            self.power_screen(900)
        print("INSTRUMENTATION_STATUS: CaseStep=光子引擎触发结束")
