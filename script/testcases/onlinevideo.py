#!/usr/bin/python
# -*- coding:utf-8 -*-

import unittest
import random
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time
from script.testcases.adb_command import *

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
PLAY_TIME = config_.getint('play_options','play_time')
PLAY_TIME_LONG = config_.getint('play_options','play_time_long')
THRESHOLD = config_.getint('play_options','threshold')
MOVIES = config_.getint('play_options','movie_nums')
TARGET_DIR1 = config_.get('play_options','target_dir1')
TARGET_DIR2 = config_.get('play_options','target_dir2')
TARGET_DIR3 = config_.get('play_options','target_dir3')
TARGET_DIR4 = config_.get('play_options','target_dir4')

# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR1 = str(config['play_options']['target_dir1'])
# TARGET_DIR2 = str(config['play_options']['target_dir2'])
# TARGET_DIR3 = str(config['play_options']['target_dir3'])
# TARGET_DIR4 = str(config['play_options']['target_dir4'])
# THRESHOLD = int(config['play_options']['threshold'])
PLAY_time = 120


class OnlineVideoTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
        d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
        d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
        d.watcher("EXIT_VST_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
            textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(
            textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(
            packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("PASS_STOP_PLAY").when(
            textContains='确认退出').click(text="确认退出")

        # d.wakeup()
        d.wakeup()
        back2home_page()
        # d.wait.update(timeout=10000)
        # d.press.home()
        # d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
        # d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        for _ in range(4):
            d.press.back()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")
        for _ in range(2):
            d.press.back()
        d.press.home()

    def testPlayOnlineVideo(self):

        for i in range(10):
            if not back2home_page():
                continue
            [d.press.right() for i in range(3)]
            for _ in range(3):
                d.press.right()
                d.wait.update(timeout=2000)
            for _ in range(4):
                d.press.down()
                d.wait.update(timeout=2000)
            d.press.right()

            '''
            for _ in range(3):
                d.press.down()
                time.sleep(2)
            d.press.right()

            assert d(resourceId="com.mitv.tvhome:id/di_img", className="android.widget.ImageView", instance=7).wait.exists(timeout=6000), 'not at 电影tab页'
            d(resourceId="com.mitv.tvhome:id/di_img", className="android.widget.ImageView", instance=7).click()
            '''
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000, package_name='com.mitv.mivideoplayer')
            d(textContains='收藏').wait.exists()
            d.press.left()
            d.wait.update(timeout=2000)
            for _ in range(random.randint(0, 5)):
                d.press.down()
                d.wait.update(timeout=1000)
            for _ in range(random.randint(0, 5)):
                d.press.up()
                d.wait.update(timeout=1000)
            d.press.right()
            d.wait.update(timeout=2000)
            for _ in range(random.randint(0, 5)):
                d.press.down()
                d.wait.update(timeout=1000)
            for _ in range(random.randint(0, 5)):
                d.press.up()
                d.wait.update(timeout=1000)
            d.press.enter()
            d.wait.update(timeout=3000)
            d.press.right()
            d.wait.update(timeout=3000)
            d.press.enter()
            # d.press.enter()
            '''
            assert d(packageName='com.mitv.mivideoplayer').wait.exists(
                timeout=20000), 'video not start!'
            '''
            # assert d(resourceId='com.xiaomi.mitv.player:id/main_frame').wait.exists(timeout=30000), 'video not start!'
            time.sleep(PLAY_TIME_LONG)
            # time.sleep(30)   # for test
        for _ in range(2):
            d.press.back()
            d.wait.update(timeout=1000)

    def testPlayOnlineVideoLong(self):
        for i in range(MOVIES):
            if not back2home_page():
                continue
            d(text='电影').click()
            time.sleep(2)
            for _ in range(3):
                d.press.down()
            d.press.right()
            d.press.enter()
            for _ in range(2):
                d.press.left()
            for _ in range(random.randint(0, 5)):
                d.press.down()
            d.press.right()
            for _ in range(random.randint(0, 5)):
                d.press.down()
            d.press.enter()
            d.press.right()

            time.sleep(3)
            d.press.enter()
            # d.press.enter()
            assert d(packageName='com.mitv.mivideoplayer').wait.exists(
                timeout=20000), 'video not start!'
            # assert d(resourceId='com.xiaomi.mitv.player:id/main_frame').wait.exists(timeout=30000), 'video not start!'
            time.sleep(PLAY_TIME_LONG)
        for _ in range(2):
            d.press.back()

    def testPlayVideoBetweenApp_short(self):
        self.testPlayVideoFromVSTClient()
        self.testPlayVideoFromTogicClient()

    def testPlayLocalVideo(self):
        for _ in range(20):
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(2)
            d.press.enter()
            if d(textContains="不支持该格式视频").exists:
                d.press.back()
                time.sleep(2)
                d.press.down()
            else:
                assert d(className="android.widget.ListView").child(
                    resourceId="com.xiaomi.mitv.mediaexplorer:id/iv_image").wait.gone(
                    timeout=20000), 'start to play video failed!'
                time.sleep(10)
                d.press.back()
                time.sleep(2)

        for _ in range(6):
            d.press.back()
            time.sleep(1)
        d.press.home()

    def testplayvideo(self):
        app_name = "com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity"
        start_app(app_name)
        package = get_front_package()
        if app_name not in package:  # 判断是否启动了这个app
            return False
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity'), 'can not video player'
        time.sleep(3)
        self.testPlayLocalVideo()

    def testplaylocalmusic(self):
        app_name = "com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity"
        start_app(app_name)
        package = get_front_package()
        if app_name not in package:  # 判断是否启动了这个app
            return False
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity'), 'can not video player'
        time.sleep(3)
        for _ in range(2):
            d.press.right()
        self.testPlayLocalVideo()


    def testPlayOnlineVideo1(self):
        """
        随机播放桌面主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        adb_home()
        time.sleep(5)
        adb_center()
        time.sleep(5)
        adb_down()
        time.sleep(2)
        adb_down()
        time.sleep(2)
        adb_center()
        # setting
        adb_long_pressmenu()
        adb_up()
        adb_right()
        adb_center()
        # scan setting
        [adb_quickdown() for i in range(7)]
        [adb_quickup() for i in range(3)]
