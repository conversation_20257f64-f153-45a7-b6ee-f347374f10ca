#!/usr/bin/python
# -*- coding:utf-8 -*-
import time
import unittest
import random
from uiautomator import device as d
from testconfig import config
import subprocess
import re
from random import choice
import time
from script.testcases.adb_command import *


import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
PLAY_TIME_LONG = config_.getint('play_options','play_time_long')
THRESHOLD = config_.getint('play_options','threshold')

# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# THRESHOLD = int(config['play_options']['threshold'])
#
print("==============",PLAY_TIME_LONG,THRESHOLD)

keys = ['up', 'down', 'left', 'right']
PLAY_time = 300

class SystemAppTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="稍后更新").click(text="稍后更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.wakeup()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        d.wait.update(timeout=10000)
        back2home_page()   # home
        # d.press.home()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
            data_used = int(used.group())
            if data_used > THRESHOLD:
                d.server.adb.cmd(
                    'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
                time.sleep(30)
            else:
                pass
        else:
            assert False, ' data space can not match'''
        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)     # wait for clean disk
        # d.press.enter()
        # d.wait.update(timeout=1000)
        for _ in range(4):
            d.press.back()
            d.wait.update(timeout=1000)
        # d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        for i in range(4):
            d.press.back()
        d.press.home()


    def testSwitchSong(self):
        # assert d.server.adb.raw_cmd('shell am start -n com.mi.umifrontend/.MainActivity'), 'launch music failed'
        back2home_page()
        app_name = 'com.mi.umifrontend/.MainActivity'
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        time.sleep(60)
        for _ in range(10):
            d.press.right()
            time.sleep(15)

    def testLaunchAndExitCloudGallery(self):
        """
        launch  app store and exit
        """
        # assert d.server.adb.raw_cmd('shell am start -n com.mitv.gallery/.activity.HomePageActivity'), 'launch cloud gallery failed!'
        # time.sleep(3)
        # d.wait.update(timeout=3000, package_name='com.mitv.gallery')
        back2home_page()
        app_name = 'com.mitv.gallery/.activity.HomePageActivity'
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        d(resourceId='com.mitv.gallery:id/all_tab').wait.exists()
        d.watcher("CLICK_UPDATE").when(textContains='升级').click(text='升级')
        for i in range(3):
            d.press.enter()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.right()
        d.press.back()
        d.wait.update(timeout=2000)
        d.press.home()
        d.wait.update(timeout=2000)

    def testLaunchHandbook(self):
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.handbook/.MainActivity'), 'open handbook failed'
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.handbook/.MainActivity'), 'open handbook failed'
        # d.wait.update(timeout=3000)
        # d.wait.update(timeout=3000, package_name='com.xiaomi.mitv.handbook')
        back2home_page()
        app_name = 'com.xiaomi.mitv.handbook/.MainActivity'
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        d(resourceId='com.xiaomi.mitv.handbook:id/title').wait.exists()
        for i in range(2):
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.enter()
            for i in range(3):
                d.press.right()
                # time.sleep(2)
                d.wait.update(timeout=2000)
            d.press.back()
            for i in range(3):
                d.press.right()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        d.press.back()

    def testlaunchNotification(self):
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.systemui/.notification.notificationcenter.NotificationCenterActivityAlias'), 'open notification failed'
        # d.wait.update(timeout=2000)
        app_name = "com.xiaomi.mitv.systemui/.notification.notificationcenter.NotificationCenterActivityAlias"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package   # 判断是否启动了该App

        # d.wait.update(timeout=2000, package_name='com.xiaomi.mitv.systemui')
        d(resourceId='com.xiaomi.mitv.systemui:id/title').wait.exists()
        d.press.up()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        d.press.enter()
        d.press.back()

    def testLaunchAndExitFM(self):
        # d.server.adb.raw_cmd('shell am start -n com.xiaomi.mimusic/.PlayerActivity')
        # time.sleep(3)
        # assert d(resourceId='com.xiaomi.mimusic:id/play_btn').child(className='android.widget.ImageView').wait.exists(
        #     timeout=20000), 'Open FM failed!'
        # time.sleep(8)
        back2home_page()
        app_name = 'com.xiaomi.mimusic/.PlayerActivity'
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App

    def testLaunchAndExitSetting(self):
        """
        launch  app store and exit
       """
        # d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.settings/.entry.MainActivity')
        app_name = "com.xiaomi.mitv.settings/.entry.MainActivity"
        start_app(app_name)
        package = get_front_package()
        if "settings_window" not in package:
            return False
        time.sleep(3)

    def testLaunchAndExitMediaExplorer(self):
        # d.press.home()
        # d.wait.update(timeout=20000,package_name='com.mitv.tvhome')
        # d(text='精选').wait.exists()
        if not back2home_page():
            return False
        app_name = "com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package   # 判断是否启动了该App
        print(package)
        # d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity')
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/video', text="视频").wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        time.sleep(5)

    def testSwitchAudio(self):
        assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.settings/.entry.MainActivity')
        d(text='声音').click()
        time.sleep(1)
        d.press.enter()
        for i in range(2):
            d.press.down()
        time.sleep(1)
        d.press.right()
        d.press.home()

    def testLaunchMusic(self):
        for i in range(3):
            # assert d.server.adb.raw_cmd('shell am start -n com.mi.umifrontend/.MainActivity'), 'fail to launch miguo music'
            if not back2home_page():
                return False
            app_name = "com.mi.umifrontend/.MainActivity"
            start_app(app_name)
            package = get_front_package()
            assert app_name.split("/")[0] in package  # 判断是否启动了该App
            for i in range(3):
                time.sleep(60)
                d.press.right()
            d.press.back()
            self.testSwitchAudio()

    def testSearch(self):
        for i in range(10):
            back2home_page()
            for i in range(2):
                d.press.up()
                d.wait.update(timeout=1000)
            if d(text="米家").exists:
                d(text=u'米家').click()
                d.wait.update(timeout=2000)
            d.press.left()
            d.wait.update(timeout=2000)
            if d(text=u"搜索").exists:
                d(text=u"搜索").click()
                d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000)
            if d(text="清空").exists:
                d(text="清空").click()
            # assert d(text='M').wait.exists(timeout=6000), 'not at search,cannot find M'
            d.press.enter()
            self.__Move_L_R(5, 'right', 2)
            self.__Move_L_R(20, 'down', 2)
            for i in range(5):
                d.press.enter()
                d.wait.update(timeout=2000)
                d.press.back()
                d.wait.update(timeout=2000)
                d.press.right()

    def testSimsearch(self):
        back2home_page()
        # d.press.home()
        # d.wait.update(timeout=5000, package_name='com.mitv.tvhome')
        # assert d(resourceId='android:id/content').wait.exists(), 'not at home'
        self.__Move_L_R(3, 'up', 2)
        for i in range(5):
            self.__Move_L_R(5, 'right', 2)
            self.__Move_L_R(5, 'left', 2)

    def testlaunchTVmanager(self):
        back2home_page()
        app_name = "com.xiaomi.mitv.tvmanager/.MainTvManagerActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package   # 判断是否启动了该App
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.tvmanager/.MainTvManagerActivity'), 'cannot launch TVmanager'
        # time.sleep(5)
        d.watcher("CLICK_UPDATE").when(textContains='升级').click(text='升级')
        # for i in range(2):
        # assert d(text = '垃圾清理').wait.exists(timeout=6000), '垃圾清理not exists'
        # d(text = '垃圾清理').click()
        # time.sleep(20)
        # d(text = '完成').click()
        # assert d(text = '一键加速').wait.exists(timeout=6000), '一键加速not exists'
        # d(text = '一键加速').click()
        # time.sleep(30)
        # d.click(610,913)
        # time.sleep(30)
        # assert d(text = '加速完成').wait.exists(timeout=6000), '加速完成not exists'
        # d(text = '加速完成').click()
        # d.press.back()

        assert d(text='内存加速').wait.exists(timeout=6000), '内存加速not exists'
        d(text='内存加速').click()
        time.sleep(30)
        d.press.back()

        assert d(text='垃圾清理').wait.exists(timeout=6000), '垃圾清理not exists'
        d(text='垃圾清理').click()
        time.sleep(30)
        d(text='垃圾清理').click()
        time.sleep(30)
        d.press.back()

        assert d(text='软件卸载').wait.exists(timeout=6000), '软件卸载not exists'
        d(text='软件卸载').click()
        time.sleep(10)
        d.press.back()

        assert d(text='深度清理').wait.exists(timeout=6000), '深度清理not exists'
        d(text='深度清理').click()
        time.sleep(3)
        d(text='大文件清理').click()
        time.sleep(5)
        d.press.enter()
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        time.sleep(10)
        d.press.back()
        d(text='应用数据清理').click()
        time.sleep(5)
        for i in range(15):
            d.press.enter()
        d.press.back()

        d(text='强力清理').click()
        time.sleep(2)
        d.watcher("PASS_NOTIFICATION").when(textContains="确定").click(text="确定")
        time.sleep(15)
        d.press.back()

        assert d(text='网络检测').wait.exists(timeout=6000), '网络检测not exists'
        d(text='网络检测').click()
        time.sleep(60)
        d.press.back()
        assert d(text='网络测速').wait.exists(timeout=6000), '网络测速not exists'
        d(text='网络测速').click()
        time.sleep(10)
        d.press.back()
        for i in range(3):
            d.press.back()

    def testlaunchWeather(self):
        back2home_page()
        time.sleep(3)
        app_name = "com.xiaomi.tweather/.TWeatherActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package   # 判断是否启动了该App
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.tweather/.TWeatherActivity'), 'cannot launch weather'
        # d.server.adb.raw_cmd('shell am start -n com.xiaomi.tweather/.TWeatherActivity')
        # d.wait.update(timeout=5000,package_name="com.xiaomi.tweather")
        for __index in range(10):
            d.press.right()
            d.wait.update(timeout=3000)
            if d(resourceId='com.xiaomi.tweather:id/add').exists:
                break

        assert d(text='城市管理').wait.exists(timeout=6000), '城市管理not exists'  # 添加一个城市
        if __index == 0:
            d(resourceId='com.xiaomi.tweather:id/add').click()
            d(text='北京').click()
            d.wait.update(timeout=3000)

        for __cities_index in range(8):
            d.press.right()
            d.wait.update(timeout=3000)
            if d(resourceId="com.xiaomi.tweather:id/delete").exists:
                break

        assert d(text='城市管理').wait.exists(timeout=6000), '城市管理not exists'  # 删除添加的城市

        for _ in range(8):
            d.press.left()
            d.wait.update(timeout=1000)  # back to the first city,the first city cannot delete

        for _ in range(8):   # delete the new add cities
            d.press.right()
            d.wait.update(timeout=1000)
            if d(resourceId="com.xiaomi.tweather:id/delete").exists:
                d(resourceId="com.xiaomi.tweather:id/delete").click()
                d.wait.update(timeout=2000)
            else:
                break

        # d(resourceId="com.xiaomi.tweather:id/delete").click()
        # time.sleep(2)

        for i in range(8):
            d.press.left()
            d.wait.update(timeout=2000)
            if d(text="语音播报").exists:
                d.press.back()
                break
        d.wait.update(timeout=2000)
        d.press.down()
        d.wait.update(timeout=2000)
        d.press.down()
        d.wait.update(timeout=2000)
        d.press.enter()
        d.wait.update(timeout=2000)
        d.press.back()

    def testlaunchCalendar(self):
        back2home_page()
        app_name = "com.xiaomi.mitv.calendar/.CalendarActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package   # 判断是否启动了该App
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.calendar/.CalendarActivity'), 'cannot launch calendar'
        d.wait.update(timeout=3000,package_name="com.xiaomi.mitv.calendar")
        d(text='节日').click()
        d.wait.update(timeout=3000)
        assert d(text='国庆节').wait.exists(timeout=6000), '国庆节not exists'  # 进入节日中的国庆节再返回
        d(text='国庆节').click()
        d.wait.update(timeout=3000)
        d.press.back()

    def testlaunchTVshop(self):
        back2home_page()
        app_name = "com.xiaomi.mitv.shop/.ShopHomeActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package   # 判断是否启动了该App
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.shop/.ShopHomeActivity'), 'fail to launch TVshop'
        # d.wait.update(timeout=3000)
        d.wait.update(timeout=3000, package_name='com.xiaomi.mitv.shop')
        d(resourceId='com.xiaomi.mitv.shop:id/tv_app_name').wait.exists()
        for i in range(20):
            d.press.down()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            for i in range(5):
                d.press.enter()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.right()

    def testlaunchGallery(self):
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.tv.gallery/.GalleryActivity'), 'fail to launch gallery'
        # time.sleep(3)
        # d.wait.update(timeout=3000, package_name='com.xiaomi.tv.gallery')
        back2home_page()
        app_name = "com.xiaomi.tv.gallery/.GalleryActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        d(resourceId='com.xiaomi.tv.gallery:id/main_nav').wait.exists()
        d.press.enter()
        # time.sleep(3)
        d.wait.update(timeout=3000)
        d.press.enter()
        for i in range(10):
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.right()
        d.press.back()
        d.wait.update(timeout=2000)
        d.press.home()

    def testlaunchSmartshare(self):
        # assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.smartshare/.MainActivity'), 'fail to launch smartshare'
        back2home_page()
        app_name = "com.xiaomi.mitv.smartshare/.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        time.sleep(2)
        for i in range(5):
            for i in range(2):
                self.__Move_L_R(2, 'right', 2)
            for i in range(2):
                self.__Move_L_R(2, 'left', 2)
        d.press.home()



        '''
        assert d(text="Miracast").exists, 'Miracast icon not found!'
        d(text="Miracast").click.wait()
        time.sleep(3)
        d.press.back()
        time.sleep(3)
        assert d(text="Airplay").exists, 'Airplay icon not found!'
        d(text="Airplay").click.wait()
        time.sleep(3)
        d.press.back()
        time.sleep(3)
        assert d(text="投屏神器").exists, '投屏神器 icon not found!'
        d(text="投屏神器").click.wait()
        time.sleep(3)
        d.press.back()
        time.sleep(3)
        assert d(text="微信投屏").exists, '微信投屏 icon not found!'
        d(text="微信投屏").click.wait()
        time.sleep(3)
        d.press.back()
        time.sleep(3)
        assert d(text="乐播投屏").exists, '乐播投屏 icon not found!'
        d(text="乐播投屏").click.wait()
        time.sleep(3)
        d.press.back()
        time.sleep(3)
        assert d(text="影音投屏").exists, '影音投屏 icon not found!'
        d(text="影音投屏").click.wait()
        time.sleep(3)
        d.press.back()
        time.sleep(3)
        '''


    def launchhome(self):
        self.__Move_L_R(10, 'down', 2)
        for i in range(5):
            d.press.enter()
            time.sleep(1)
            d.press.back()
            time.sleep(1)
            d.press.down()
        # d.press.home()
        # d.wait.update(timeout=2000,package_name='com.mitv.tvhome')
        # d.press.back()
        # time.sleep(5)
        back2home_page()

    def __Move_L_R(self, step, direction, sleepTime):
        for i in range(step):
            d.press(direction)
            d.wait.update(timeout=sleepTime*1000)   # ms

    def testlaunchHome(self):
        # d.press.home()
        # d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
        # d(text='精选').wait.exists()
        # d.press.back()
        # d.wait.update(timeout=1000)
        back2home_page()
        if d(text="精选").exists:
            # assert d(text="精选").exists, 'not at home'
            d(text='精选').click()
        self.launchhome()
        # 电视剧
        self.__Move_L_R(2, 'right', 2)
        self.launchhome()
        # 电影
        self.__Move_L_R(3, 'right', 2)
        self.launchhome()
        # 综艺
        self.__Move_L_R(4, 'right', 2)
        self.launchhome()
        # 儿童
        self.__Move_L_R(3, 'left', 2)
        self.launchhome()
        # vip
        self.__Move_L_R(1, 'right', 2)
        self.launchhome()
        # 纪录片
        self.__Move_L_R(8, 'right', 2)
        self.launchhome()
        # 分类
        self.__Move_L_R(20, 'right', 2)
        self.launchhome()
        # 应用
        self.__Move_L_R(20, 'right', 2)
        d.press.left()

        self.launchhome()

    def testPlayDuokanVideo(self):
        for i in range(5):
            d.press.back()
            time.sleep(2)
            d.press.back()
            time.sleep(2)
            d.press.back()
            time.sleep(2)
            back2home_page()
            # d.press.home()
            # d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
            # # assert d(packageName='com.mitv.tvhome').wait.exists(timeout=20000), 'not at home'
            # d(text='精选').wait.exists()
            #d.press.back()
            #d.wait.update(timeout=1000)
            # assert d(text='精选').exists, 'loading home timeout'
            #self.__Move_L_R(3, 'down', 2)
            #d.press.enter()
            app_name = "com.duokan.videodaily/.videoclip.VideoClipMainActivity"
            start_app(app_name)
            package = get_front_package()
            assert app_name.split("/")[0] in package  # 判断是否启动了该App
            # d.server.adb.raw_cmd('shell am start -n com.duokan.videodaily/.videoclip.VideoClipMainActivity')
            # d.wait.update(timeout=5000, package_name='com.duokan.videodaily')
            #assert d(packageName='com.duokan.videodaily').exists(timeout=20000), 'launch duokanvideo fail'
            d(resourceId="com.duokan.videodaily:id/common_video_item_fullscreen_ll").wait.exists()
            # assert d(packageName='com.duokan.videodaily').exists, 'loading duokan timeout!'
            if d(packageName='com.duokan.videodaily').exists:
                d.press.enter()
                time.sleep(PLAY_TIME_LONG)
            #self.__Move_L_R(2, 'back', 2)

    def testPlayPatchVideo(self):
        for i in range(30):
            d.press.back()
            time.sleep(2)
            d.press.back()
            time.sleep(2)
            d.press.back()
            time.sleep(2)
            back2home_page()
            d.press.back()
            d.wait.update(timeout=1000)
            # assert d(text='精选').exists, 'loading home timeout'
            d.press.down()
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=5000, package_name="com.mitv.mivideoplayer")  # 进入这个包
            package = get_front_package()
            if "com.mitv.mivideoplayer" not in package:   # check 当前是否在这个包
                continue
            # assert d(packageName='com.mitv.mivideoplayer',textContains='收藏').wait.exists(timeout=20000), 'not at mivideoplayer'
            # assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'not at mivideoplayer'
            # # 进入了packageName的页面之后，还没有加载结束，但是画面已经是符合packageName了，往左一步是到不了左边这个控件的
            # d(textContains='收藏').wait.exists()   # check 了几个电视剧（有选集）、电影（没有选集），需要会员，不需要会员的，只有“收藏”控件是统一的,状态可能是“收藏”或者是“已收藏”
            # assert d(textContains='收藏').exists,'loading mivideoplayer timeout'
            d.press.left()  # 左一步是为了让他播放第一集，否则进来默认是“开通会员”控件,如果这个视频不需要会员，则没有开通会员控件
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000)
            package = get_front_package()
            if "com.mitv.mivideoplayer" not in package:  # check 当前是否在这个包
                continue
            # assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'video not start!'
            time.sleep(PLAY_TIME_LONG)
            # time.sleep(30)

    def testlaunchHomeswitchTab(self):
        # 桌面tab页来回切换
        for i in range(10):
            back2home_page()
            for _ in range(3):
                d.press.left()
                d.wait.update(timeout=2000)
                d.press.right()
                d.wait.update(timeout=2000)
            for _ in range(20):
                d.press.left()
                d.wait.update(timeout=2000)

    def testSimpleHome(self):
        # 极简模式下浏览桌面“
        back2home_page()
        # d.press.home()
        # d.wait.update(timeout=5000, package_name='com.mitv.tvhome')
        # assert d(resourceId='android:id/content').wait.exists(), 'not at home'
        # d(text='精选').click()
        self.__Move_L_R(30, 'down', 2)
        self.__Move_L_R(30, 'up', 2)
        d.press('home')

    def test_SmartHome1(self):
        back2home_page()
        # d.press.home()
        #d.sleep(5)
        # assert d.server.adb.raw_cmd('shell am start com.xiaomi.smarthome.tv/com.xiaomi.smarthome.tv.ui.MainActivity')
       # d.sleep(APP_SLEEP_TIME)
       #  d.wait.update(timeout=1000, package_name='com.xiaomi.smarthome')
        app_name = "com.xiaomi.smarthome.tv/com.xiaomi.smarthome.tv.ui.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        assert d(text='默认房间').wait.exists(timeout=500), 'User not logged in'
        self.__Move_L_R(1, 'back', 2)
        for i in range(20):
            self.__Move_L_R(3, 'right', 1)
            self.__Move_L_R(3, 'left', 1)
        d.press('home')

    def test_SmartHome2(self):
        # assert d.server.adb.raw_cmd('shell am start com.xiaomi.smarthome.tv/com.xiaomi.smarthome.tv.ui.MainActivity')
        #d.sleep(APP_SLEEP_TIME)
        # d.wait.update(timeout=1000, package_name='com.xiaomi.smarthome')
        app_name = "com.xiaomi.smarthome.tv/com.xiaomi.smarthome.tv.ui.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        assert d(text='默认房间').wait.exists(timeout=5000), 'User not logged in'
        for i in range(30):
            self.__Move_L_R(1, 'enter', 2)
            self.__Move_L_R(1, 'back', 2)
        d.press('home')

    def touch_up(self):
        back2home_page()
        # assert d(text='精选').wait.exists(timeout=6000)
        for i in range(3):
            d.press.up()
            d.wait.update(timeout=1000)

    def testApplicationswitch(self):
        for r in range(5):
            # 桌面向上选择搜索栏
            self.touch_up()
            d.wait.update(timeout=2000)
            d(text="搜索").wait.exists()
            d.press.left()
            d.wait.update(timeout=2000)
            if d(text=u"搜索").wait.exists(timeout=600):
                d(text=u"搜索").click()
                d.wait.update(timeout=2000)
                d.press.enter()  # 进入搜索界面
                d.wait.update(timeout=2000)
                d(text="清空").wait.exists(timeout=6000)
                for _ in range(5):
                    d.press.right()
                    d.wait.update(timeout=2000)
                for _ in range(2):
                    d.press.down()
                    d.wait.update(timeout=2000)
                d.press.enter()
                d.wait.update(timeout=2000)
                for _ in range(5):
                    d.press.left()
            # 桌面向上选择米家
            self.touch_up()
            d(text="搜索").wait.exists()
            if d(text='米家').wait.exists(timeout=600):
                # assert d(text='米家').wait.exists(timeout=6000), '米家icon not find '
                d(text='米家').click()
                d.wait.update(timeout=2000)
                d.press.enter()
                for _ in range(5):
                    d.wait.update(timeout=2000)
                    d.press.enter()
                    d.wait.update(timeout=2000)
                    d.press.back()
                    d.wait.update(timeout=2000)
                    d.press.right()
            # 桌面向上选择信号源
            self.touch_up()
            d(text="搜索").wait.exists()
            if d(text='信号源').exists:  # transformer mibox没有信号源的icon
                # assert d(text='信号源').wait.exists(timeout=6000), '信号源icon not find '
                d(text='信号源').click()
                d.wait.update(timeout=2000)
                d.press.enter()
                d.wait.update(timeout=2000)
                d.press.enter()
                d.wait.update(timeout=10000)
            # 桌面向上选择模式
            self.touch_up()
            d(text="搜索").wait.exists()
            if d(text='模式').wait.exists(timeout=6000):
                # assert d(text='模式').wait.exists(timeout=6000), '模式icon not find '
                d(text='模式').click()
                d.wait.update(timeout=2000)
                d.press.enter()
                for _ in range(3):
                    d.press.right()
                    d.wait.update(timeout=2000)
                for _ in range(3):
                    d.press.left()
                    d.wait.update(timeout=2000)
            # 桌面向上选择设置
            self.touch_up()
            d(text="搜索").wait.exists()
            if d(text='设置').wait.exists(timeout=6000):
                # assert d(text='设置').wait.exists(timeout=6000), '设置icon not find '
                d(text='设置').click()
                d.wait.update(timeout=2000)
                d.press.enter()
                d.wait.update(timeout=2000)
                for _ in range(8):
                    d.press.enter()
                    d.wait.update(timeout=2000)
                    for c in range(5):
                        d.press.down()
                        d.wait.update(timeout=2000)
                    d.press.back()
                    d.wait.update(timeout=2000)
                    d.press.down()
            # 桌面向上选择投屏
            self.touch_up()
            d(text="搜索").wait.exists()
            # assert d(text='投屏').wait.exists(timeout=6000), '投屏icon not find '
            if d(text='投屏').wait.exists(timeout=600):  # 有的项目没有投屏功能，例如transformer
                d(text='投屏').click()
                d.wait.update(timeout=2000)
                d.press.enter()
                d.wait.update(timeout=2000)
                for _ in range(4):
                    d.press.right()
                    d.wait.update(timeout=2000)
                for _ in range(4):
                    d.press.left()
                    d.wait.update(timeout=2000)
            # 桌面向上选择用户反馈
            self.touch_up()
            d(text="搜索").wait.exists()
            # assert d(text='用户反馈').wait.exists(timeout=6000), '用户反馈icon not find '
            if d(text='用户反馈').wait.exists(timeout=6000):  # jobs项目没有'用户反馈'
                d(text='用户反馈').click()
                d.wait.update(timeout=2000)
                d.press.enter()
                d.wait.update(timeout=2000)
                for _ in range(3):
                    d.press.enter()
                    d.wait.update(timeout=2000)
                    for _ in range(6):
                        d.press.down()
                    d.wait.update(timeout=2000)
                    d.press.back()
                    d.wait.update(timeout=2000)
                    d.press.right()
                    d.wait.update(timeout=2000)

    def testfamilycamera(self):
        d.press.home()
        time.sleep(5)

        assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.familycamera/.FCamaActivity'), 'fail to launch camera'
        # 拍照
        time.sleep(5)
        d.press.menu()
        for _ in range(4):
            time.sleep(2)
            d.press.enter()
        d.press.back()
        time.sleep(2)
        d.press.menu()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.down()
        for _ in range(3):
            time.sleep(2)
            d.press.enter()
        time.sleep(2)
        d.press.back()
        time.sleep(2)

        # 录像
        d.press.left()
        time.sleep(2)
        d.press.menu()
        for _ in range(3):
            time.sleep(2)
            d.press.enter()
        time.sleep(20)
        d.press.enter()
        time.sleep(2)
        d.press.menu()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.down()
        for _ in range(2):
            time.sleep(2)
            d.press.enter()
        time.sleep(20)
        d.press.enter()

    # 企业版回到设置界面
    def __Egotosetting(self):
        d.press.down()
        time.sleep(2)
        self.__Move_L_R(5, 'left', 1)
        d.press.right()
        time.sleep(2)
        d.press.down()
        time.sleep(2)
    # 设置浏览

    def __Esettingbrowse(self):
        for i in range(5):
            time.sleep(2)
            d.press.enter()
            time.sleep(2)
            self.__Move_L_R(10, 'down', 2)
            d.press.back()
            time.sleep(2)
            d.press.down()
    # 企业版设置

    def testE_setting(self):
        self.__Egotosetting()
        d.press.enter()
        time.sleep(1)
        # assert d(text='网络设置').wait.exists(timeout=6000), 'not in setting '
        self.__Move_L_R(5, 'down', 2)
        self.__Move_L_R(6, 'up', 2)
        self.__Esettingbrowse()
    # 企业版浏览文件

    def testE_browsefiles(self):
        self.__Egotosetting()
        d.press.right()
        time.sleep(1)
        d.press.enter()
        self.__Move_L_R(5, 'right', 2)
        self.__Move_L_R(5, 'left', 2)
    # 企业版桌面

    def testE_tvhome(self):
        # 氛围
        self.__Move_L_R(5, 'right', 2)
        self.__Move_L_R(6, 'left', 2)
        d.press.up()
        time.sleep(2)
        self.__Move_L_R(5, 'right', 2)
        self.__Move_L_R(5, 'left', 2)
        # 企业展示
        d.press.down()
        time.sleep(1)
        self.__Move_L_R(2, 'right', 2)
        time.sleep(1)
        d.press.up()
        self.__Move_L_R(6, 'right', 2)
        self.__Move_L_R(7, 'left', 2)
        # 视频会议
        d.press.down()
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        d.press.up()
        time.sleep(1)
        self.__Move_L_R(6, 'right', 2)
        self.__Move_L_R(6, 'left', 2)
    # 电视管家

    def testE_TVmanager(self):
        d.press.down()
        time.sleep(1)
        self.__Move_L_R(4, 'right', 2)
        d.press.enter()
        time.sleep(2)
        d.watcher("CLICK_UPDATE").when(textContains='升级').click(text='升级')
        d.press.enter()
        time.sleep(20)
        assert d(text='内存加速').wait.exists(timeout=6000), '内存加速not exist'
        d(text='内存加速').click()
        time.sleep(30)
        d.press.back()
        assert d(text='垃圾清理').wait.exists(timeout=6000), '垃圾清理not exists'
        d(text='垃圾清理').click()
        time.sleep(30)
        d(text='垃圾清理').click()
        time.sleep(30)
        d.press.back()
        assert d(text='网络检测').wait.exists(timeout=6000), '网络检测not exists'
        d(text='网络检测').click()
        time.sleep(60)
        d.press.back()
        assert d(text='网络测速').wait.exists(timeout=6000), '网络测速not exists'
        d(text='网络测速').click()
        time.sleep(10)
        d.press.back()
        assert d(text='设备信息').wait.exists(timeout=6000), '网络测速not exists'
        d(text='设备信息').click()
        for i in range(3):
            d.press.back()

    def testopenSidebar(self):
        for _ in range(2):
            count = 0
            # Assistanticon = ['信号源', '网络', '截屏', '投屏', '设置']
            for i in range(3):
                d.server.adb.cmd(
                    'adb shell input keyevent --longpress KEYCODE_MENU')
                time.sleep(1)
                if count == 0:
                    self.__Move_L_R(1, 'enter', 1)
                elif count == 1:
                    self.__Move_L_R(1, 'right', 1)
                    self.__Move_L_R(1, 'enter', 1)
                else:
                    self.__Move_L_R(1, 'down', 1)
                    self.__Move_L_R(1, 'enter', 1)
                time.sleep(1)
                self.__Move_L_R(5, 'down', 1)
                count += 1

    def __returnAtmosphere(self):
        self.__Move_L_R(5, 'left', 2)
    # 企业版长时间播氛围壁纸

    def testEAtmosphere(self):
        self.__returnAtmosphere()
        d.press.enter()
        self.__Move_L_R(5, 'left', 2)
        self.__Move_L_R(5, 'right', 2)
        time.sleep(PLAY_TIME_LONG)
    # 企业版信号源

    def testELaunchSource(self):
        self.__returnAtmosphere()
        self.__Move_L_R(1, 'right', 2)
        d.press.enter()
        time.sleep(1)
        self.__Move_L_R(2, 'right', 2)
        self.__Move_L_R(2, 'left', 2)
# fenwei

    def testEExhibition(self):
        self.__returnAtmosphere()
        self.__Move_L_R(2, 'right', 2)
        d.press.enter()
        time.sleep(1)
        self.__Random()
        self.__Move_L_R(1, 'down', 2)
        d.press.enter()
        time.sleep(1)
        self.__Move_L_R(5, 'right', 1)
        self.__Move_L_R(5, 'left', 1)
        time.sleep(PLAY_TIME_LONG)
        d.press.menu()
        time.sleep(2)
        # 轮播
        assert d(text='轮播').wait.exists(timeout=6000), 'no click menu'
        d.press.enter()
        time.sleep(1)
        for i in range(3):
            d.press.enter()
            time.sleep(1)
            d.press.right()
            time.sleep(1)
        d.press.back()
        time.sleep(1)
        d.press.enter()
        time.sleep(20)
        d.press.back()
        # 删除
        self.__Move_L_R(1, 'menu', 2)
        self.__Move_L_R(2, 'down', 2)
        self.__Move_L_R(1, 'enter', 2)
    # 长按menu键

    def __Random(self):
        for _ in range(20):
            key = choice(keys)
            getattr(d.press, key)()
            time.sleep(2)
            self.__Move_L_R(1, 'right', 2)
            d.press.enter()
            time.sleep(5)
            d.press.back()


    def test_videoheadline(self):
        app_name = 'com.duokan.videodaily/com.duokan.videodaily.videoclip.VideoClipMainActivity'
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package
        adb_back()
        [adb_quickright() for i in range(6)]
        [adb_quickleft() for i in range(4)]
        adb_back()
        [adb_quickdown() for i in range(10)]
        adb_home()
        time.sleep(2)
        call_xiaoai()
        time.sleep(2)
        adb_long_pressmenu()
        time.sleep(2)

    def test_intelligentassis(self):
        back2home_page()
        adb_long_pressmenu()
        time.sleep(2)
        adb_up()
        adb_right()
        adb_center()
        [adb_quickdown() for i in range(8)]

    def test_mediaexplorer(self):
        try:
            app_name = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'
            start_app(app_name)
            package = get_front_package()
            assert app_name.split("/")[0] in package  # 判断是否启动了该App
        except AssertionError as exp:
            print(str(exp))
        time.sleep(5)
        adb_left()
        adb_down()
        adb_center()
        time.sleep(25)
        assert d(text=u'1A播放压测码流').wait.exists(
            timeout=10000), 'can not find U disk'
        d(text=u'1A播放压测码流').click()
        time.sleep(3)
        adb_center()
        # play local video
        for i in range(10):
            [adb_center() for i in range(2)]
            adb_volume_up()
            adb_volume_down()
            time.sleep(5)
            adb_long_pressmenu()
            time.sleep(3)
            adb_back()
            time.sleep(30)
            [adb_back() for i in range(3)]
            adb_down()



    def test_hksettings(self):
        app_name = "com.xiaomi.mitv.settings/.entry.MainActivity"
        start_app(app_name)
        package = get_front_package()
        if "settings_window" not in package:
            return False
        time.sleep(3)
        for i in range(7):
            adb_down()
        for i in range(7):
            adb_up()