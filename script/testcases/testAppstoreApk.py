#!/usr/bin/python
# -*- coding:utf-8 -*-

import unittest
import random
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time
from script.testcases.adb_command import *
# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR1 = str(config['play_options']['target_dir1'])
# TARGET_DIR2 = str(config['play_options']['target_dir2'])
# TARGET_DIR3 = str(config['play_options']['target_dir3'])
# TARGET_DIR4 = str(config['play_options']['target_dir4'])
# THRESHOLD = int(config['play_options']['threshold'])


class AppstroeApkTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
        d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
        d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
        d.watcher("EXIT_VST_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
            textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(
            textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(
            packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("PASS_STOP_PLAY").when(
            textContains='确认退出').click(text="确认退出")
        d.watcher("EXIT_DIANLIMAO__Popup").when(
            textContains='去看看').click(text="取消")
        d.watcher("EXIT_Bilibili__Popup").when(
            textContains='在屏幕顶端按上键').press('enter')

        d.wakeup()
        back2home_page()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d.wait.update(timeout=10000)
        # d.press.home()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd(
        		    'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        d.press.enter()
        for _ in range(4):
            d.press.back()
        time.sleep(2)

    def pressRight(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.right()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def pressLeft(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.left()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def pressUp(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.up()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def pressDown(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.down()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def pressEnter(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.enter()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def pressBack(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.back()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")
        for _ in range(2):
            d.press.back()
        d.press.home()

    def testLutongnetNldmx(self):
        back2home_page()
        app_name = "com.lutongnet.nldmx/.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # d.server.adb.raw_cmd('shell am start -n com.lutongnet.nldmx/.MainActivity')
        # time.sleep(60)
        d.wait.update(timeout=60000, package_name='com.lutongnet.brainadventure.plugin')
        self.pressDown(10)
        self.pressUp(10)
        for i in range(5):
            d.press.down()
            for j in range(4):
                d.press.enter()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                if i % 2 == 0:
                    d.press.right()
                else:
                    d.press.left()

    def testGongfubbWksz(self):
        back2home_page()
        app_name = "air.com.gongfubb.wksz.tv/.AppEntry"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # d.server.adb.raw_cmd('shell am start -n air.com.gongfubb.wksz.tv/.AppEntry')
        # time.sleep(30)
        d.wait.update(timeout=30000, package_name='air.com.gongfubb.wksz.tv')
        self.pressEnter(5)
        self.pressDown(5)
        self.pressUp(5)
        # time.sleep(1)
        d.wait.update(timeout=1000)
        d.press.enter()
        time.sleep(60)

    def testPlayVideoFromYouku(self):
        back2home_page()
        app_name = "com.cibn.tv/com.youku.tv.home.activity.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.cibn.tv/com.youku.tv.home.activity.HomeActivity'), 'can not launch Youku'
        '''
        assert d.server.adb.raw_cmd('shell am start -n com.cibn.tv/com.yunos.tv.yingshi.home.HomeActivity'), 'can not launch Youku'
        '''

        # time.sleep(2)
        d.wait.update(timeout=2000, package_name='com.cibn.tv')
        if d(textContains="同意").exists:
            adb_center()
            time.sleep(2)
        if d(textContains="是否将优酷XL设置为开机自启动？").wait.exists(timeout=6000):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
        if d(textContains="立即更新").wait.exists(timeout=6000):
            d.press.back()
        if d(textContains="淘宝/支付宝扫码登录",resourceId="com.cibn.tv:id/aec").exists:
            adb_back()
        # time.sleep(4)
        d.wait.update(timeout=4000)
        # assert d(text='电视剧').wait.exists(timeout=10000), "优酷-电视剧菜单 not found on screen"

        played = 0
        # assert d(text = '电影').exists(), '非电影界面'

        while played < 20:
            d.press.enter()
            if d(textContains="全屏").wait.exists(timeout=2000):
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.left()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.enter()
            else:
                d.press.back()
                # time.sleep(1)
                d.wait.update(timeout=1000)
                d.press.down()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                played += 1
                continue
            time.sleep(PLAY_TIME)

            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Youku-播放视频 未开始！'

            if d(textContains="非常抱歉，该节目暂时无法在TV端播放").wait.exists(timeout=6000):
                d.press.right()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.enter()
            played += 1
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.back()
            # time.sleep(3)
            d.wait.update(timeout=3000)
            d.press.back()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.down()

        for _ in range(5):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.home()

    def testBaoShengKtv(self):
        back2home_page()
        app_name = "com.baosheng.ktv/com.pc.parentcalendar.activity.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        time.sleep(5)
        # d.server.adb.raw_cmd("am start -S com.baosheng.ktv/com.pc.parentcalendar.activity.MainActivity")
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.baosheng.ktv/com.pc.parentcalendar.activity.MainActivity'), 'cannot launch baoshengktv'
        # time.sleep(10)
        d.wait.update(timeout=10000, package_name='com.baosheng.ktv')
        d.press.enter()
        # time.sleep(1)
        d.wait.update(timeout=1000)
        d.press.enter()
        self.pressRight(10)
        self.pressLeft(10)
        for i in range(5):
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.right()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        for i in range(3):
            d.press.back()
        d.press.home()

    def testChangBaSd(self):
        back2home_page()
        app_name = "com.changba.sd/com.changba.tv.module.main.ui.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # time.sleep(5)
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.changba.sd/com.changba.tv.module.main.ui.MainActivity'), 'cannot launch changba'
        # time.sleep(10)
        d.wait.update(timeout=10000, package_name='com.changba.sd')
        d.press.enter()
        # time.sleep(1)
        d.wait.update(timeout=1000)
        d.press.enter()
        self.pressRight(5)
        self.pressLeft(5)
        for i in range(5):
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.down()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        self.pressBack(3)
        d.press.home()

    def testFittime(self):
        time.sleep(5)
        back2home_page()
        app_name = "com.fittime.tv.mi/com.fittime.tv.module.main.MainActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.fittime.tv.mi/com.fittime.tv.module.main.MainActivity'), 'cannot launch fittime'
        # time.sleep(10)
        d.wait.update(timeout=10000, package_name='com.fittime.tv.mi')
        self.pressBack(1)
        self.pressRight(8)
        self.pressLeft(8)
        for i in range(5):
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.right()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        self.pressBack(3)
        d.press.home()

    def testLutongnetOtt(self):
        back2home_page()
        app_name = "com.lutongnet.ott.health/com.lutongnet.lib.app.health.HealthActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.lutongnet.ott.health/com.lutongnet.lib.app.health.HealthActivity'), 'cannot launch fittime'
        # # time.sleep(10)
        # d.wait.update(timeout=10000, package_name='com.lutongnet.ott.health')
        # self.pressBack(1)
        # time.sleep(3)
        # d.wait.update(timeout=3000)
        # if d(textContains="同意",resourceId="com.lutongnet.ott.health:id/btn_agree").exists:
        #     d(textContains="同意", resourceId="com.lutongnet.ott.health:id/btn_agree").click()
        # 同意协议弹窗
        if "com.lutongnet.ott.health/.privacypolicies.PrivacyPoliciesActivity" in package:
            adb_center()
            time.sleep(2)

        # 访问电视文件权限
        if "com.android.packageinstaller/.permission.ui.GrantPermissionsActivity" in package:
            adb_center()
            time.sleep(2)

        self.pressRight(15)
        self.pressLeft(10)
        for i in range(5):
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.right()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        self.pressBack(3)
        d.press.home()

    def testPlayVideoFromAiQiYi(self):
        back2home_page()
        app_name = "com.gitvdemo.video/com.gala.video.app.epg.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.gitvdemo.video/com.gala.video.app.epg.HomeActivity'), 'can not launch AiQiYi'
        # time.sleep(5)
        # d.wait.update(timeout=5000, package_name='com.gitvdemo.video')
        # d(text='推荐').wait.exists()
        if d(textContains="同意并继续").exists:  # 同意用户协议
            adb_center()
            time.sleep(2)
        # 存储权限
        package = get_front_package()
        if "com.android.permissioncontroller/.permission.ui.GrantPermissionsActivity" not in package:
            adb_center()
            time.sleep(2)
        print("退出广告页推送")
        adb_back()
        if d(textContains="立即更新").wait.exists(timeout=15000):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
        if d(textContains="解码器补丁").wait.exists(timeout=15000):
            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.down()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.enter()
        d.press.down()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        played = 0
        #         d.press.enter()
        while played < 20:
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            if d(text="全屏").wait.exists(timeout=5000):
                d.press.left()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.enter()
            else:
                played += 1
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                continue

            # assert d(className='android.view.View').wait.exists(timeout=20000), 'VST-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            for _ in range(2):
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
            played += 1
            # time.sleep(2)
            d.wait.update(timeout=2000)

        for _ in range(3):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        if d(textContains="退出").wait.exists(timeout=6000):
            d(textContains='退出').click()
            d.wait.update(timeout=5000)

    def testBilibili(self):
        back2home_page()
        app_name = "com.xiaodianshi.tv.yst/.ui.main.MainActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        """not work"""
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.xiaodianshi.tv.yst/.ui.main.MainActivity'), 'can not launch bilibili'
        # time.sleep(12)
        # d(text='精选').wait.exists(timeout=6000)
        # d.wait.update(timeout=12000, package_name='com.xiaodianshi.tv.yst')
        # d(text='推荐').wait.exists(timeout=6000)
        # 处理弹窗
        if "com.xiaodianshi.tv.yst/com.xiaodianshi.tv.yst.ui.messagedialog.MessageDialogActivity" in package:
            adb_back()
        # 同意用户协议
        if "com.xiaodianshi.tv.yst/.ui.introduction.IntroductionActivity" in package:
            adb_center()
        d.press.down()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        played = 0
        while played < 20:

            if d(textContains="全屏").wait.exists(timeout=2000):
                d.press.enter()
            else:
                d.press.down()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                played += 1
                continue

            # d.press.enter()
            # assert d(className='android.view.View').wait.exists(timeout=20000), '播放视频 未开始！'
            time.sleep(PLAY_TIME)
            # if not d(text="开始播放").wait.exists(timeout=10000):

            d.press.back()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            played += 1

            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)

        for _ in range(5):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.enter()

    '''def testPlayVideoFromMyVst(self):
        assert d.server.adb.raw_cmd('shell am start -n net.myvst.v2/.home.HomeActivity'), 'can not launch MyVst'
        time.sleep(12)
        for _ in range(2):
            d.press.left()
            time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(3)
        d.press.right()
        time.sleep(1)
        d.press.down()
        time.sleep(3)

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(8)
            d.press.enter()
            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Moli-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            # if not d(text="开始播放").wait.exists(timeout=10000):
            for _ in range(2):
                d.press.back()
            played += 1
            time.sleep(1)
            d.press.back()
            time.sleep(1)
            d.press.down()
            time.sleep(3)

        for _ in range(5):
            d.press.back()
            time.sleep(1)
        d.press.enter()'''

    def testPptvTvsports(self):
        back2home_page()
        app_name = "com.pptv.tvsports/.activity.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.pptv.tvsports/.activity.HomeActivity'), 'can not launch pptv tvsports'
        # time.sleep(12)
        d.wait.update(timeout=12000, package_name='com.pptv.tvsports')
        for _ in range(3):
            d.press.down()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        time.sleep(3)

        played = 0
        while played < MOVIES:
            d.press.enter()
            # time.sleep(8)
            d.wait.update(timeout=8000)
            d.press.enter()
            # assert d(className='android.view.View').wait.exists(timeout=20000), '播放视频 未开始！'
            time.sleep(PLAY_TIME)
            # if not d(text="开始播放").wait.exists(timeout=10000):
            for _ in range(2):
                d.press.back()
            played += 1
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.down()
            # time.sleep(3)
            d.wait.update(timeout=3000)

        for _ in range(5):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.enter()

    def testPlayVideoFromDianshimao(self):
        back2home_page()
        app_name = "com.moretv.android/.StartActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.moretv.android/.StartActivity'), 'can not launch Dian shi mao'
        # # time.sleep(10)
        # d.wait.update(timeout=10000, package_name='com.moretv.android')
        '''
        time.sleep(1)
        for _ in range(2):
            d.press.right()
            time.sleep(1)
        time.sleep(3)
        '''
        for _ in range(2):
            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)
        played = 0
        while played < MOVIES:
            d.press.enter()
            # time.sleep(3)
            d.wait.update(timeout=3000)
            if d(textContains="全屏").wait.exists(timeout=2000):
                d.press.enter()
            else:
                d.press.back()
                # time.sleep(1)
                d.wait.update(timeout=1000)
                d.press.down()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                played += 1
                continue
            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Dianshimao-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            for _ in range(2):
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
            d.press.right()
            played += 1
            # time.sleep(2)
            d.wait.update(timeout=2000)
        for _ in range(3):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.enter()

    '''def testPlayVideoFromSouhu(self):
        assert d.server.adb.raw_cmd('shell am start -n com.sohuott.tv.vod/.activity.BootActivity'), 'can not launch Souhu'
        time.sleep(10)
        d.press.right()
        time.sleep(2)
        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(3)
            d.press.enter()
            time.sleep(3)
            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Souhu-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            d.press.back()
            if d(textContains="退出播放").wait.exists(timeout=6000):
                d(textContains='退出播放').click()
            time.sleep(5)
            played += 1
            time.sleep(2)
            d.press.back()
            time.sleep(1)
            d.press.right()
            time.sleep(2)
            d.press.down()
            for i in range(played):
                d.press.right()
            time.sleep(3)

        for _ in range(3):
            d.press.back()
            time.sleep(1)
        if d(textContains="退出不看了").wait.exists(timeout=6000):
            d(textContains='退出不看了').click()'''

    def testPlayVideoFromYunshiting(self):
        back2home_page()
        app_name = "com.ktcp.video/.activity.MainActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if "com.ktcp.video/.activity.PrivacyAgreementActivity" in package:
            adb_center()
            time.sleep(2)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.ktcp.video/.activity.MainActivity'), 'can not launch Yunshiting'
        # # time.sleep(10)
        # d.wait.update(timeout=10000, package_name='com.ktcp.video')
        # d(text='精选').wait.exists(timeout=6000)
        # d.press.back()
        # time.sleep(2)
        # d.wait.update(timeout=2000)
        for _ in range(3):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
        d.press.down()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        played = 0
        while played < 10:
            d.press.enter()

            # time.sleep(3)
            d.wait.update(timeout=3000)
            d.press.left()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
            played += 1

            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Yunshiting-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            d.press.back()
            if d(textContains="退出播放").wait.exists(timeout=6000):
                d(textContains='退出播放').click()
            d.press.back()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)
        for _ in range(3):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        if d(textContains="退出了").wait.exists(timeout=6000):
            d(textContains='退出了').click()
            d.wait.update(timeout=5000)

    def testPlayVideoFromMangguo(self):
        back2home_page()
        app_name = "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意").exists:
            adb_center()
            time.sleep(2)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.hunantv.license/com.mgtv.tv.channel.activity.ChannelHomeActivity'), 'can not launch MangguoTV'
        # # time.sleep(10)
        # d.wait.update(timeout=10000, package_name='com.hunantv.license')
        # d(text='精选').wait.exists(timeout=6000)
        d.press.right()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        played = 0
        while played < 10:
            d.press.enter()
            # time.sleep(3)
            d.wait.update(timeout=3000)
            d.press.enter()
            # time.sleep(3)
            d.wait.update(timeout=3000)
            time.sleep(PLAY_TIME)
            d.press.back()
            if d(className='android.widget.RelativeLayout').wait.exists(timeout=6000):
                d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            played += 1
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
        for _ in range(3):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.enter()

    def test_HuashuTV(self):
        try:
        #华数TV
            adb_home()
            app_name = "com.ixigua.android.tv.wasu/com.ixigua.android.business.tvbase.base.app.schema.AdsAppActivity"
            start_app(app_name)
            package = get_front_package()
            # assert app_name.split("/")[0] in package  # 判断是否启动了该App
            print(package)
            if d(resourceId='com.ixigua.android.tv.wasu:id/ad', text='按OK键 同意并继续').wait.exists(
            timeout=2000):
                d(resourceId='com.ixigua.android.tv.wasu:id/ad', text='按OK键 同意并继续').wait.click(timeout=3)

            else:
                print('Skip startup advertisement')

            if d(textContains="残忍退出").exists:
                adb_right()
                adb_center()
                time.sleep(2)
            else:
                print('play video')
            time.sleep(60)
            [adb_quickright() for i in range(10)]
            call_xiaoai()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            adb_back()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
        except Exception as exp:
            print(str(exp))
            self.restart_uiautomator()

    def test_KuaiTv(self):
        #云视听快TV
        for i in range(10):
            adb_home()
            app_name = "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity"
            start_app(app_name)
            package = get_front_package()
            # assert app_name.split("/")[0] in package  # 判断是否启动了该App
            print(package)
            if d(textContains="同意并继续").exists:
                adb_center()
            time.sleep(5)
            adb_center()
            adb_back()
            adb_back()
            time.sleep(60)
            [adb_quickright() for i in range(10)]
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
            # time.sleep(100)

    def test_TVhome(self):
        #电视家3.0
        back2home_page()
        app_name = "com.tvrun.run/com.dianshijia.newlive.entry.SplashActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if "com.android.packageinstaller/com.android.packageinstaller.permission.ui.GrantPermissionsActivity" in package:
            adb_center()
        for i in range(5):
            adb_down()
            time.sleep(30)
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
                call_xiaoai()
                # 菜单切换
                adb_center()
                adb_quickleft()
                adb_down()
                adb_right()
                [adb_quickdown() for i in range(3)]
                adb_center()
                time.sleep(10)
                call_xiaoai()
                # breathing_screen()
                time.sleep(5)
                start_app(app_name)

    def test_qqmusic(self):
        for i in range(10):
            back2home_page()
            app_name = "com.tencent.qqmusictv/.examples.NewMainActivity"
            start_app(app_name)
            package = get_front_package()
            # assert app_name.split("/")[0] in package  # 判断是否启动了该App
            print(package)
            for i in range(4):
                adb_center()
            time.sleep(30)
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)