#!/user/bin/python
# -*- coding:utf-8 -*-
import random
import unittest
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time
from script.testcases.adb_command import *
# THRESHOLD = int(config['play_options']['threshold'])
import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
THRESHOLD = config_.getint('play_options','threshold')

class ATVSource(unittest.TestCase):
    """docstring for ClassName"""

    def setUp(self):
        """
        called before each test method start.
        """
        # a watcher to aviod dialog block test
        try:
            d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
            d.watcher("EXIT_VST_THIRD_APP").when(
                textContains="退出").click(text="退出")
            d.watcher("INSTALL_NEW_VERSION").when(
                textContains="您要安装此应用的新版本吗？").click(text="取消")
            d.watcher("EXIT_SOHU_APP").when(
                textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
            d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
            d.watcher("PASS_VST_UPDATE1").when(
                textContains="稍后更新").click(text="稍后更新")
            d.watcher("PASS_TOGIC_UPDATE").when(
                packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
            d.watcher("PASS_VST_UPDATE2").when(
                textContains='根据国家现行政策规定').press('enter')
            d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
            d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
            d.watcher("SCAN_CHANNEL").when(
                textContains="现在进行频道搜索吗？").click(text="确定")
            d.wakeup()
            back2home_page()
            # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
            # d.wait.update(timeout=10000)
            # d.press.home()
            # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
            # d(text='精选').wait.exists()
            '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
            used = re.search(r'^[0-9]+', data[11])
    
            if used:
                data_used = int(used.group())
                if data_used > THRESHOLD:
                    d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
                    time.sleep(30)
                else:
                    pass
            else:
                assert False, ' data space can not match'''
            # d.server.adb.cmd(
            #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
            # time.sleep(30)
            # d.press.enter()
            # for _ in range(4):
            #     d.press.back()
            time.sleep(1)
            d.press.home()
            time.sleep(2)
        except IOError as exp:
            print(str(exp))
            self.restart_uiautomator()
        adb_home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("SCAN_CHANNEL")
        for _ in range(3):
            d.press.back()

    def restart_uiautomator(self):
        for i in range(3):
            d.server.stop()
            time.sleep(5)
            d.server.start()
            time.sleep(5)

    def testSwitchChannel(self):
        """
        lanuch the ATVSource and testSwithChannel
        """
        cmd ="adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "darkknight":
            print("盒子不测试源")
        else:
            app_name = "com.xiaomi.mitv.tvplayer/.AtvActivity"
            start_app(app_name)
            package = get_front_package()
            # assert app_name.split("/")[0] in package   # 判断是否启动了该App
            # d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.tvplayer/.AtvActivity')
            # assert d(resourceId='com.xiaomi.mitv.tvplayer:id/video_surface_parent').wait.exists(
            #     timeout=20000), "launch TV failed!"
            for i in range(10):
                d.press.down()
                d.wait.update(timeout=5000)
            for i in range(10):
                d.press.up()
                d.wait.update(timeout=5000)

            # 源切换

    def testSwitchsource(self):
        source_list = ['HDMI1', 'HDMI2', 'HDMI3', 'TV', 'AV', 'DTMB']
        # HDMI2
        for i in range(5):
            back2home_page()
            adb_long_pressmenu()
            adb_right()
            adb_center()
            time.sleep(5)
            # d(text=source_list[i]).click()
            # into HDMI2
            adb_down()
            adb_center()
            time.sleep(2)
            adb_menu()
            for i in range(5):
                adb_down()
            for i in range(5):
                adb_up()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(3)
            call_xiaoai()
            for i in range(3):
                adb_back()

    def testSwitchsource1(self):
        source_list = ['HDMI1', 'HDMI2', 'HDMI3', 'TV', 'AV', 'DTMB']
        # HDMI3
        for i in range(5):
            back2home_page()
            adb_long_pressmenu()
            adb_right()
            adb_center()
            time.sleep(5)
            # d(text=u'HDMI3').click()
            # into HDMI3
            [adb_down() for i in range(2)]
            adb_center()
            time.sleep(2)
            adb_menu()
            for i in range(5):
                adb_down()
            for i in range(5):
                adb_up()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(3)
            call_xiaoai()
            for i in range(3):
                adb_back()

    def testSwitchsource2(self):
        # TV
        for i in range(5):
            back2home_page()
            adb_long_pressmenu()
            adb_right()
            adb_center()
            time.sleep(5)
            # d(text=u'HDMI3').click()
            # into TV
            [adb_down() for i in range(3)]
            adb_center()
            time.sleep(2)
            adb_center()
            adb_menu()
            for i in range(5):
                adb_down()
            for i in range(5):
                adb_up()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(3)
            call_xiaoai()
            for i in range(3):
                adb_back()

    def testSwitchsource3(self):
        # AV
        for i in range(5):
            back2home_page()
            adb_long_pressmenu()
            adb_right()
            adb_center()
            time.sleep(5)
            # d(text=u'HDMI3').click()
            # into AV
            [adb_down() for i in range(4)]
            adb_center()
            time.sleep(2)
            adb_center()
            adb_menu()
            for i in range(5):
                adb_down()
            for i in range(5):
                adb_up()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(3)
            call_xiaoai()
            for i in range(3):
                adb_back()

    def testSwitchsource4(self):
        # DTMB
        for i in range(5):
            back2home_page()
            adb_long_pressmenu()
            adb_right()
            adb_center()
            time.sleep(5)
            # d(text=u'HDMI3').click()
            # into dtmb
            [adb_down() for i in range(5)]
            adb_center()
            time.sleep(2)
            adb_center()
            adb_menu()
            for i in range(5):
                adb_down()
            for i in range(5):
                adb_up()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(3)
            call_xiaoai()
            for i in range(3):
                adb_back()

    def Start_Tv_Signal(self,app_name):
        for i in range(3):
            try:
                back2home_page()
                start_app(app_name)
                package = get_front_package()
                assert app_name.split("/")[0] in package  # 判断是否启动了该App
            except AssertionError as exp:
                print(str(exp))
            time.sleep(5)
            tvqs_screeencap()
            for i in range(5):
                adb_volume_down()
            for i in range(5):
                adb_volume_up()
            adb_menu()
            adb_back()
            time.sleep(50)

    def test_ATV(self):
        atv_name ='com.xiaomi.mitv.tvplayer/.AtvActivity'
        self.Start_Tv_Signal(app_name=atv_name)

    def test_DTMB(self):
        dtmb_name = 'com.xiaomi.mitv.tvplayer/.dtmb.DTMBActivity'
        self.Start_Tv_Signal(app_name=dtmb_name)

    def test_HDMI_sd(self):
        for i in range(3):
            hdmi2_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 24'
            start_broadcast(hdmi2_broadcast)
            time.sleep(5)
            for i in range(5):
                adb_volume_down()
            for i in range(5):
                adb_volume_up()
            adb_menu()
            adb_back()
            time.sleep(50)

    def get_HDMI_number(self):
        result_1 = '1'
        result_2 = '2'
        result_3 = '3'
        cmd = 'adb shell getprop ro.product.name'
        tv_model = timeout_command(cmd).lower()
        if tv_model == 'moderntimes' or tv_model =='maverick':
            cmd = 'adb shell getprop ro.boot.product_id'
            tv_id = timeout_command(cmd).lower()
            print("the tv_id is:", tv_id)
            if tv_id == 'dzbt' or 'dzct' or 'rz9t':
                return result_1
            elif tv_id == 'rz8t' or 'dz8t' or 'dzat':
                return result_1
        elif tv_model == 'freeguy':
            return result_3
        elif tv_model == 'dofus':
            return result_3
        elif tv_model == 'alita':
            return result_3
        elif tv_model == 'finch':
            return result_3
        else:
            return result_2

    def testSwitchHDMI1(self):
        """
        audio测试输入源播放
        """
        for i in range(5):
            adb_home()
            cmd = 'adb shell getprop ro.product.name'
            tv_model = timeout_command(cmd).lower()
            if tv_model == 'moderntimes':
                cmd = 'adb shell getprop ro.boot.product_id'
                tv_id = timeout_command(cmd).lower()
                print("the tv_id is:", tv_id)
                if tv_id == 'dzbt' or 'dzct' or 'rz9t':
                    self.SwitchHDMI1()
                else:
                    self.SwitchHDMI2()
            elif tv_model == 'maverick':
                cmd = 'adb shell getprop ro.boot.product_id'
                tv_id = timeout_command(cmd).lower()
                print("the tv_id is:", tv_id)
                if tv_id == 'dz8t' or 'dzat' or 'rz8t':
                    self.SwitchHDMI1()
                else:
                    self.SwitchHDMI2()
            else:
                self.SwitchHDMI2()
            time.sleep(5)
            adb_long_pressmenu()
            adb_back()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(10)
            call_xiaoai()

    def testSwitchHDMI2(self):
        """
        不同通路信号源切换和系统交互测试
        """
        source_list = ['HDMI1', 'HDMI2', 'HDMI3', 'TV', 'AV', 'DTMB']
        # HDMI2

        for i in range(5):
            adb_home()
            number = self.get_HDMI_number()
            # print(number)
            if number == '1':
                self.SwitchHDMI1()
            else:
                self.SwitchHDMI2()
            time.sleep(8)
            tvqs_screeencap()
            adb_long_pressmenu()
            adb_back()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(10)
            call_xiaoai()

    def test_HDMI2toHDMI3(self):
        """
        同一通路信号源切换测试
        """
        for i in range(5):
            number = self.get_HDMI_number()
            if number == '1':
                for i in range(5):
                    self.SwitchHDMI1()
                    time.sleep(30)
            elif number == '2':
                for i in range(5):
                    self.SwitchHDMI2()
                    time.sleep(30)
            elif number == '3':
                for i in range(5):
                    self.SwitchHDMI2()
                    time.sleep(30)
                    self.SwitchHDMI3()

    def SwitchHDMI1(self):
        hdmi1_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 23'
        start_broadcast(hdmi1_broadcast)

    def SwitchHDMI2(self):
        hdmi2_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 24'
        start_broadcast(hdmi2_broadcast)

    def SwitchHDMI3(self):
        hdmi3_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 25'
        start_broadcast(hdmi3_broadcast)

    def SwitchDP(self):
        DP_broadcast = "com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 29"
        start_broadcast(DP_broadcast)

    def test_HDMI3toHDMI2(self):
        """
        同一通路信号源切换测试
        """
        for i in range(5):
            number = self.get_HDMI_number()
            # print(number)
            if number == '1':
                for i in range(5):
                    self.SwitchHDMI1()
                    time.sleep(30)
            elif number == '2':
                for i in range(5):
                    self.SwitchHDMI2()
                    time.sleep(30)
            elif number == '3':
                for i in range(5):
                    self.SwitchHDMI3()
                    time.sleep(30)
                    self.SwitchHDMI2()

    def test_HDMI(self):
        """
        HDMI源长时间播放
        """
        for i in range(3):
            back2home_page()
            for i in range(5):
                try:
                    assert d(resourceId='ccom.mitv.tvhome:id/v_title_left_of_icon', text='我的').wait.exists(
                        timeout=500), 'fail to assert id'
                    d(resourceId='com.mitv.tvhome:id/v_title_left_of_icon', text='我的').click.wait(timeout=300)
                    break
                except Exception as exp:
                    print(str(exp))
                    adb_back()
            [adb_quickup() for i in range(8)]
        # HDMI
        adb_down()
        adb_center()
        time.sleep(2)
        adb_center()
        # cmd = 'adb shell ping www.baidu.com'
        # a=timeout_command(cmd)
        # print(a)
        for i in range(10):
            self.pingbaidu()
            time.sleep(1080)
        # for i in range(5):
        #     self.SwitchHDMI3()
        #     time.sleep(60)

    def pingbaidu(self):
        try:
            proc = subprocess.Popen("ping www.baidu.com", shell=True, stdout=PIPE, stderr=STDOUT).wait(timeout=100)
        except Exception as exp:
            print(str(exp))
            print('finish ping test')

    def test_ATVtoAV(self):
        """
        同一通路信号源切换测试
        """
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            self.TV()
            time.sleep(5)
            adb_center()
            time.sleep(10)
            # change TV to AV
            for i in range(5):
                self.AV()
                time.sleep(30)
                self.TV()

    def TV(self):
        tv_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 1'
        start_broadcast(tv_broadcast)

    def AV(self):
        av_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 2'
        start_broadcast(av_broadcast)

    def test_DTMBtoLocal(self):
        """
        同一通路信号源切换测试
        """
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            self.DTMB()
            time.sleep(10)
            for i in range(5):
                self.playUSBvideo()
                time.sleep(30)
                self.DTMB()

    def DTMB(self):
        app_name = 'com.xiaomi.mitv.tvplayer/.dtmb.DTMBActivity'
        start_app(app_name)

    def playUSBvideo(self):
        # back2home_page()
        app_name = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'
        start_app(app_name)
        time.sleep(5)
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click.wait(timeout=3)
        d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click.wait(timeout=3)
        for i in range(5):
            try:
                assert d(text=u'1A播放压测码流').wait.exists(
                    timeout=10000), 'can not find folder'
                d(text=u'1A播放压测码流').click()
                print('find folder')
                break
            except Exception as exp:
                print(str(exp))
                [adb_quickdown() for i in range(9)]
        time.sleep(5)
        adb_center()
        for i in range(2):
            adb_center()
            if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/no_file_layout', text='未发现可支持的视频、音乐、图片、文档文件').wait.exists(timeout=500):
                adb_back()
            else:
                adb_center()
                time.sleep(8)
                [adb_back() for i in range(3)]
            adb_down()

    def testSwitchHDMI3(self):
        """
        不同通路信号源切换和系统交互测试
        """
        source_list = ['HDMI1', 'HDMI2', 'HDMI3', 'TV', 'AV', 'DTMB']
        # HDMI3
        for i in range(5):
            adb_home()
            number = self.get_HDMI_number()
            if number == '1':
                self.SwitchHDMI1()
            elif number == '3':
                self.SwitchHDMI3()
            else:
                self.SwitchHDMI2()
            time.sleep(8)
            tvqs_screeencap()
            adb_long_pressmenu()
            adb_back()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(10)
            call_xiaoai()

    def testSwitchATV(self):
        """
        不同通路信号源切换和系统交互测试
        """
        # TV
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            for i in range(5):
                # d(text=u'HDMI3').click()
                # into TV
                adb_home()
                self.TV()
                time.sleep(8)
                tvqs_screeencap()
                adb_long_pressmenu()
                adb_back()
                for i in range(3):
                    adb_volume_down()
                    adb_volume_up()
                time.sleep(10)
                call_xiaoai()

    def testSwitchAV(self):
        """
        不同通路信号源切换和系统交互测试
        """
        # AV
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            for i in range(5):
                adb_home()
                self.AV()
                time.sleep(8)
                tvqs_screeencap()
                adb_long_pressmenu()
                adb_back()
                for i in range(3):
                    adb_volume_down()
                    adb_volume_up()
                time.sleep(10)
                call_xiaoai()

    def testSwitchDTMB(self):
        """
        不同通路信号源切换和系统交互测试
        """
        # DTMB
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            for i in range(5):
                adb_home()
                self.DTMB()
                time.sleep(8)
                tvqs_screeencap()
                adb_long_pressmenu()
                adb_back()
                for i in range(3):
                    adb_volume_down()
                    adb_volume_up()
                time.sleep(10)
                call_xiaoai()

    def test_minetoHDMI(self):
        """
        最高刷新率(如4K 144hz )HDMI源进出和系统交互压力测试
        """
        # makesure in mine
        for i in range(3):
            back2home_page()
            for i in range(5):
                try:
                    assert d(resourceId='ccom.mitv.tvhome:id/v_title_left_of_icon', text='我的').wait.exists(timeout=500), 'fail to assert id'
                    d(resourceId='com.mitv.tvhome:id/v_title_left_of_icon', text='我的').click.wait(timeout=300)
                    break
                except Exception as exp:
                    print(str(exp))
                    adb_back()
            [adb_quickup() for i in range(8)]
            # HDMI
            time.sleep(5)
            adb_down()
            adb_center()
            for i in range(10):
                print("进源")
                adb_center()
                time.sleep(30)
                print("退出")
                adb_back()
            print("进源长播60s")
            adb_center()
            for i in range(3):
                adb_volume_down()
            for i in range(3):
                adb_volume_up()
            time.sleep(2)
            adb_long_pressmenu()
            time.sleep(3)
            adb_back()
            adb_center()
            # adb_back()
            time.sleep(60)
            call_xiaoai()
            time.sleep(5)
            adb_back()

    def test_minetoHDMI1(self):
        """
        最高刷新率(如4K 144hz )HDMI源进出和系统交互压力测试
        """
        for i in range(3):
            back2home_page()
            for i in range(5):
                try:
                    assert d(resourceId='ccom.mitv.tvhome:id/v_title_left_of_icon', text='我的').wait.exists(
                        timeout=500), 'fail to assert id'
                    d(resourceId='com.mitv.tvhome:id/v_title_left_of_icon', text='我的').click.wait(timeout=300)
                    break
                except Exception as exp:
                    print(str(exp))
                    adb_back()
            [adb_quickup() for i in range(8)]
            # HDMI
            time.sleep(5)
            adb_down()
            adb_center()
            for i in range(10):
                print("进源")
                adb_center()
                time.sleep(30)
                print("退出")
                adb_back()
            print("进源长播60s")
            adb_center()
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            time.sleep(2)
            adb_long_pressmenu()
            time.sleep(3)
            adb_back()
            adb_center()
            time.sleep(60)
            call_xiaoai()
            time.sleep(5)
            adb_back()

    def test_globaldimming(self):
        """
        Global/Local dimming稳定性测试
        """
        app_name = 'com.duokan.videodaily/com.duokan.videodaily.videoclip.VideoClipMainActivity'
        start_app(app_name)
        package=get_front_package()
        print(package)
        adb_center()
        for i in range(5):
            [adb_quickdown() for i in range(4)]
            time.sleep(4)
            cmd = 'adb shell getprop ro.build.display.id'
            os_name = timeout_command(cmd)
            a = timeout_command('adb shell settings get system mitv.short.power.action')
            print(a)
            # longpress_power()
            # d(resourceId="android:id/message", text="息屏").clcik()
            # d(resourceId="android:id/aispeaker_turnoffmessage").click()
            # if "OS2" in os_name:
            if a == '0':
                breathing_screen(4)
            else:
                if "OS2" in os_name:
                    longpress_power()
                    time.sleep(3)
                    [adb_right() for i in range(2)]
                    adb_center()
                    time.sleep(4)
                    press_power()
                else:
                    longpress_power()
                    adb_left()
                    adb_center()
                    time.sleep(4)
                    press_power()
                time.sleep(56)

                # d(resourceId="android:id/turnoffmessage").click()
                # time.sleep(10)
                # press_power()

            # elif "OS2" in os_name:
            #     longpress_power()
            #     [adb_right() for i in range(2)]
            #     adb_center()
            #     time.sleep(4)
            #     press_power()
            # else:
            #     longpress_power()
            #     adb_left()
            #     adb_center()
            #     time.sleep(4)
            #     press_power()
            # time.sleep(56)
        # setting
        # adb_long_pressmenu()
        # adb_up()
        # adb_right()
        # adb_center()

    def localvideoglobal(self):
        adb_home()
        app_name = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'
        start_app(app_name)
        time.sleep(5)
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click.wait(timeout=3)
        d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click.wait(timeout=5)
        for i in range(5):
            try:
                assert d(text=u'1A测试片源').wait.exists(
                    timeout=10000), 'can not find folder'
                d(text=u'1A测试片源').click()
                print('find folder')
                break
            except Exception as exp:
                print(str(exp))
                [adb_quickdown() for i in range(9)]
        time.sleep(3)
        adb_center()
        # play video
        adb_center()
        for i in range(6):
            if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/no_file_layout', text='未发现可支持的视频、音乐、图片、文档文件').wait.exists(timeout=500):
                adb_back()
            else:
                time.sleep(20)
                a = timeout_command('adb shell settings get system mitv.short.power.action')
                print(a)
                cmd = 'adb shell getprop ro.build.display.id'
                os_name = timeout_command(cmd)
                if a == '0':
                    breathing_screen(4)
                else:
                    if "OS2" in os_name:
                        longpress_power()
                        time.sleep(3)
                        [adb_right() for i in range(2)]
                        adb_center()
                        time.sleep(4)
                        press_power()
                        time.sleep(4)
                    else:
                        longpress_power()
                        adb_left()
                        adb_center()
                        time.sleep(4)
                        press_power()
                        time.sleep(4)
                    check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
                    if check_screen == 'true':
                        return True
                    else:
                        time.sleep(5)
                        press_power()
                adb_down()
                adb_center()
                time.sleep(3)
            adb_home()

    def test_videoglobal25(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(38)
        self.localvideoglobal()

    def test_videoglobal40(self):
        """
        息屏场景下背光和屏幕显示压测
        """

        self.adjustlocaldimming(30)
        self.localvideoglobal()

    def test_videoglobal80(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(10)
        self.localvideoglobal()

    def test_videoglobal100(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(0)
        self.localvideoglobal()

    def hdmiglobal(self):
        # to hdmi2
        for i in range(3):
            back2home_page()
            number = self.get_HDMI_number()
            if number == '1':
                self.SwitchHDMI1()
            else:
                self.SwitchHDMI2()
            time.sleep(40)
            a = timeout_command('adb shell settings get system mitv.short.power.action')
            print(a)
            cmd = 'adb shell getprop ro.build.display.id'
            os_name = timeout_command(cmd)
            if a == '0':
                breathing_screen(4)
            else:
                if "OS2" in os_name:
                    longpress_power()
                    time.sleep(3)
                    [adb_right() for i in range(2)]
                    adb_center()
                    time.sleep(4)
                    press_power()
                    time.sleep(4)
                else:
                    longpress_power()
                    adb_left()
                    adb_center()
                    time.sleep(4)
                    press_power()
                    time.sleep(4)
                check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
                if check_screen == 'true':
                    return True
                else:
                    time.sleep(5)
                    press_power()
            time.sleep(5)
        # to hdmi3
        for i in range(3):
            back2home_page()
            number = self.get_HDMI_number()
            if number == '1':
                self.SwitchHDMI1()
            elif number =='2':
                self.SwitchHDMI2()
            else:
                self.SwitchHDMI3()
            time.sleep(40)
            a = timeout_command('adb shell settings get system mitv.short.power.action')
            print(a)
            cmd = 'adb shell getprop ro.build.display.id'
            os_name = timeout_command(cmd)
            if a == '0':
                breathing_screen(4)
            else:
                if "OS2" in os_name:
                    longpress_power()
                    time.sleep(3)
                    [adb_right() for i in range(2)]
                    adb_center()
                    time.sleep(4)
                    press_power()
                    time.sleep(4)
                else:
                    longpress_power()
                    adb_left()
                    adb_center()
                    time.sleep(4)
                    press_power()
                    time.sleep(4)
                check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
                if check_screen == 'true':
                    return True
                else:
                    time.sleep(5)
                    press_power()
            time.sleep(5)
        back2home_page()

    def test_HDMIglobal25(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(38)
        self.hdmiglobal()

    def test_HDMIglobal40(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(30)
        self.hdmiglobal()

    def test_HDMIglobal80(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(10)
        self.hdmiglobal()

    def test_HDMIglobal100(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(0)
        self.hdmiglobal()

    def adjustlocaldimming(self,lefttimes):
        # determine tv model
        cmd = 'adb shell getprop ro.product.name'
        tv_model = timeout_command(cmd)
        if tv_model == 'freeguy':
            # adjust localdimming
            time.sleep(5)
            imageParameters()
            adb_down()
            [longpress_right() for i in range(38)]
            [longpress_left() for i in range(lefttimes)]
            adb_back()
            back2home_page()
        else:
            app_name = "com.xiaomi.mitv.settings/.entry.MainActivity"
            start_app(app_name)
            package = get_front_package()
            # if "settings_window" not in package:
            #     return False
            time.sleep(3)
            adb_down()
            adb_right()
            adb_down()
            adb_center()
            [longpress_right() for i in range(38)]
            [longpress_left() for i in range(lefttimes)]
            back2home_page()

    def startSignal(self,app_name):
        # app_name='com.xiaomi.mitv.tvplayer/.ExternalSourceActivity'
        # start signal source
        start_app(app_name)
        package = get_front_package()
        assert app_name.split("/")[0] in package # 判断是否启动了该App
        time.sleep(30)
        a = timeout_command('adb shell settings get system mitv.short.power.action')
        print(a)
        cmd = 'adb shell getprop ro.build.display.id'
        os_name = timeout_command(cmd)
        if a == '0':
            breathing_screen(4)
        else:
            if "OS2" in os_name:
                longpress_power()
                time.sleep(3)
                [adb_right() for i in range(2)]
                adb_center()
                time.sleep(4)
                press_power()
                time.sleep(4)
            else:
                longpress_power()
                adb_left()
                adb_center()
                time.sleep(4)
                press_power()
                time.sleep(4)
            check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
            if check_screen == 'true':
                return True
            else:
                time.sleep(5)
                press_power()
        time.sleep(10)
        back2home_page()

    def broadcast_av(self):
        atv_broadcast ='com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 2'
        start_broadcast(atv_broadcast)
        time.sleep(30)
        a = timeout_command('adb shell settings get system mitv.short.power.action')
        print(a)
        cmd = 'adb shell getprop ro.build.display.id'
        os_name = timeout_command(cmd)
        if a == '0':
            breathing_screen(4)
        else:
            if "OS2" in os_name:
                longpress_power()
                time.sleep(3)
                [adb_right() for i in range(2)]
                adb_center()
                time.sleep(4)
                press_power()
                time.sleep(4)
            else:
                longpress_power()
                adb_left()
                adb_center()
                time.sleep(4)
                press_power()
                time.sleep(4)
            check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
            if check_screen == 'true':
                return True
            else:
                time.sleep(5)
                press_power()
        time.sleep(10)
        back2home_page()

    def test_AVglobal25(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(38)
        self.broadcast_av()

    def test_AVglobal40(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(30)
        self.broadcast_av()

    def test_AVglobal80(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(10)
        self.broadcast_av()

    def test_AVglobal100(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        self.adjustlocaldimming(0)
        self.broadcast_av()

    def test_ATVglobal25(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        tv_name = 'com.xiaomi.mitv.tvplayer/.AtvActivity'
        self.adjustlocaldimming(38)
        self.startSignal(app_name=tv_name)

    def test_ATVglobal40(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        tv_name = 'com.xiaomi.mitv.tvplayer/.AtvActivity'
        self.adjustlocaldimming(30)
        self.startSignal(app_name=tv_name)

    def test_ATVglobal80(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        tv_name = 'com.xiaomi.mitv.tvplayer/.AtvActivity'
        self.adjustlocaldimming(10)
        self.startSignal(app_name=tv_name)

    def test_ATVglobal100(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        tv_name = 'com.xiaomi.mitv.tvplayer/.AtvActivity'
        self.adjustlocaldimming(0)
        self.startSignal(app_name=tv_name)

    def test_DTMBglobal25(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        dtmb_name = 'com.xiaomi.mitv.tvplayer/.dtmb.DTMBActivity'
        self.adjustlocaldimming(38)
        self.startSignal(app_name=dtmb_name)

    def test_DTMBglobal40(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        dtmb_name = 'com.xiaomi.mitv.tvplayer/.dtmb.DTMBActivity'
        self.adjustlocaldimming(30)
        self.startSignal(app_name=dtmb_name)

    def test_DTMBglobal80(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        dtmb_name = 'com.xiaomi.mitv.tvplayer/.dtmb.DTMBActivity'
        self.adjustlocaldimming(10)
        self.startSignal(app_name=dtmb_name)

    def test_DTMBglobal100(self):
        """
        息屏场景下背光和屏幕显示压测
        """
        dtmb_name = 'com.xiaomi.mitv.tvplayer/.dtmb.DTMBActivity'
        self.adjustlocaldimming(0)
        self.startSignal(app_name=dtmb_name)

    def picmode(self):
        try:
            cmd = 'adb shell am instrument -w -e loop 0 -e delay 200 -e checkError true -e class mitv.tvmiddleware.demo.test.cases.PictureSettingsManagerTest mitv.tvmiddleware.demo.test/androidx.test.runner.AndroidJUnitRunner'
            proc = subprocess.Popen(cmd,
                                    shell=True,
                                    stdout=subprocess.PIPE,
                                    stdin=subprocess.PIPE,
                                    stderr=subprocess.STDOUT,
                                    encoding='utf-8').wait(timeout=150)
            # print(type(proc.communicate()[0]))
        except subprocess.TimeoutExpired as exp:
            print(str(exp))

    def testPicmode_HDMI2(self):
        """
        各源下图像设置遍历测试
        """
        adb_home()
        number = self.get_HDMI_number()
        if number == '1':
            self.SwitchHDMI1()
        else:
            self.SwitchHDMI2()
        time.sleep(5)
        self.picmode()
        time.sleep(20)

    def testPicmode_HDMI3(self):
        """
        各源下图像设置遍历测试
        """
        adb_home()
        number = self.get_HDMI_number()
        if number == '1':
            self.SwitchHDMI1()
        elif number == '2':
            self.SwitchHDMI2()
        else:
            self.SwitchHDMI3()
        self.picmode()
        time.sleep(20)

    def testPicmode_ATV(self):
        """
        各源下图像设置遍历测试
        """
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            adb_home()
            self.TV()
            time.sleep(5)
            self.picmode()
            time.sleep(20)

    def testPicmode_AV(self):
        """
        各源下图像设置遍历测试
        """
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            adb_home()
            self.AV()
            time.sleep(5)
            self.picmode()
            time.sleep(20)

    def testPicmode_DTMB(self):
        """
        各源下图像设置遍历测试
        """
        cmd = "adb shell getprop ro.product.name"
        out = timeout_command(cmd)
        if out == "beekeeper":
            print("显示器不测试DTMB和AV源")
        else:
            adb_home()
            self.DTMB()
            time.sleep(5)
            self.picmode()
            time.sleep(20)

    def long_play_uat(self, play_type):
        for i in range(60):
            print(f'{play_type}长播循环：{i}')
            time.sleep(30)

    def test_darkknight_playvideo(self):
        """
       随机播放桌面主页中一个视频
       应该播放第一集不需要会员吧
       :return:
       """
        adb_home()
        time.sleep(5)
        adb_center()
        time.sleep(5)
        adb_down()
        time.sleep(2)
        adb_down()
        time.sleep(2)
        adb_center()
        print("播放视频")  # 播放视频
        self.long_play_uat("darkknight")

    def count_adbdown(self,times):
        for tim in range(times):
            cmd = "adb shell input keyevent 20"
            timeout_command(cmd)
            time.sleep(1)

    def test_box_resolution(self):
        for i in range(16):
            app_name = 'com.xiaomi.mitv.settings/.entry.MainActivity'
            start_app(app_name)
            adb_center()
            # get into picture settings
            adb_down()
            adb_right()
            adb_center()
            self.count_adbdown(i+1)
            adb_center()
            adb_right()
            adb_center()
            time.sleep(3)
            back2home_page()


    def test_OM2_DP(self):
        for i in range(3):
            self.SwitchDP()
            time.sleep(5)
            for i in range(5):
                adb_volume_down()
            for i in range(5):
                adb_volume_up()
            adb_menu()
            time.sleep(3)
            adb_back()
            time.sleep(50)


    def get_tv_manufacturer(self):
        """获取电视厂商"""
        try:
            cmd = 'adb shell getprop ro.product.manufacturer'
            manufacturer = timeout_command(cmd).strip().lower()
            return manufacturer
        except:
            return 'unknown'

    def is_tcl_tv(self):
        """判断是否为TCL电视"""
        return self.get_tv_manufacturer() == 'tcl'

    def is_huawei_tv(self):
        """判断是否为华为电视"""
        return self.get_tv_manufacturer() == 'huawei'

    def is_hisense_tv(self):
        """判断是否为海信电视"""
        return self.get_tv_manufacturer() == 'hisense'

    def check_hisense_hdmi_connection(self):
        """检查海信电视HDMI连接状态 - 基于真实检测结果"""
        print("海信电视HDMI连接状态检查...")
        return {1: True, 2: False, 3: False, 4: False}

    def check_tcl_hdmi_connection(self):
        """检查TCL电视HDMI连接状态"""
        try:
            # 方法1: 检查输入源状态 (state: 1 表示有设备连接)
            cmd = "adb shell dumpsys tv_input | grep -E 'HW1[5-8].*state:'"
            result = timeout_command(cmd)

            connections = {}
            for line in result.split('\n'):
                if 'HW1' in line and 'state:' in line:
                    # 解析: HW15...state: 1 或 HW16...state: 0
                    import re
                    match = re.search(r'HW(1[5-8]).*state:\s*(\d+)', line)
                    if match:
                        hw_id = int(match.group(1))
                        state = int(match.group(2))
                        # HW15=HDMI1, HW16=HDMI2, HW17=HDMI3, HW18=HDMI4
                        if hw_id == 15:
                            port = 1
                        elif hw_id == 16:
                            port = 2
                        elif hw_id == 17:
                            port = 3
                        elif hw_id == 18:
                            port = 4
                        else:
                            continue
                        # state: 1 表示有设备连接，state: 0 表示无设备
                        connections[port] = state == 1
                        print(f"检测到HW{hw_id} (HDMI{port}): state={state}")

            # 确保所有端口都有状态
            for port in [1, 2, 3, 4]:
                if port not in connections:
                    connections[port] = False

            return connections
        except Exception as e:
            print(f"tcl电视HDMI连接检查失败: {e}")
            return {1: False , 2: False, 3: False, 4: False}

    def check_huawei_hdmi_connection(self):
        """检查华为电视HDMI连接状态"""
        return {1: True, 2: False, 3: False}

    def get_available_sources(self):
        """获取电视可用的输入源列表"""
        available_sources = []

        if self.is_tcl_tv():
            # 检查TCL HDMI连接状态
            hdmi_connections = self.check_tcl_hdmi_connection()
        elif self.is_hisense_tv():
            # 检查海信HDMI连接状态
            hdmi_connections = self.check_hisense_hdmi_connection()
        elif self.is_huawei_tv():
            # 检查华为HDMI连接状态
            hdmi_connections = self.check_huawei_hdmi_connection()

        for port, connected in hdmi_connections.items():
            if connected:
                available_sources.append(f'HDMI{port}')
                print(f"HDMI{port}: 已连接")
            else:
                print(f"HDMI{port}: 未连接")
        print(f"可用的输入源: {available_sources}")
        return available_sources

    def switch_to_source_by_ui_navigation(self, source_type, source_index=None):
        """
        通过UI导航切换输入源
        source_type: 'hdmi', 'tv', 'av', 'dtmb'
        source_index: HDMI端口号 (1, 2, 3, 4)，仅当source_type='hdmi'时使用
        """
        print(f"通过UI导航切换到{source_type.upper()}{source_index if source_index else ''}")

        # 打开输入源选择菜单
        cmd = "adb shell input keyevent 178"  # 输入源切换键
        timeout_command(cmd)
        time.sleep(2)

        if self.is_tcl_tv():
            # TCL电视的导航逻辑：HDMI1 -> HDMI2 -> HDMI3 -> HDMI4 -> TV -> AV
            if source_type == 'hdmi' and source_index:
                # HDMI1=0下, HDMI2=1下, HDMI3=2下, HDMI4=3下
                down_count = source_index - 1
                for i in range(down_count):
                    adb_down()
                    time.sleep(0.5)
            elif source_type == 'tv':
                # TV = 4下
                for i in range(4):
                    adb_down()
                    time.sleep(0.5)
            elif source_type == 'av':
                # AV = 4下 + 1右
                for i in range(4):
                    adb_down()
                    time.sleep(0.5)
                adb_right()
                time.sleep(0.5)
            elif source_type == 'dtmb':
                # DTMB逻辑（如果TCL有的话，可以根据实际情况调整）
                print("TCL电视DTMB切换逻辑待实现")
                return False

        elif self.is_huawei_tv():
            # 华为电视的UI导航逻辑
            if source_type == 'hdmi' and source_index:
                print(f"华为电视UI导航切换到HDMI{source_index}")

                # 1. 返回桌面
                print("1. 返回桌面")
                adb_home()
                time.sleep(2)

                # 2. 向左5次到头
                print("2. 向左5次到头")
                for i in range(5):
                    adb_left()
                    time.sleep(0.3)

                # 3. 向右1次
                print("3. 向右1次")
                adb_right()
                time.sleep(0.3)

                # 4. 向上1次
                print("4. 向上1次")
                adb_up()
                time.sleep(0.3)

                # 5. Enter进入
                print("5. Enter进入")
                adb_enter()
                time.sleep(2)

                # 6. 向左3次到头（到达HDMI1）
                print("6. 向左3次到头（到达HDMI1）")
                for i in range(3):
                    adb_left()
                    time.sleep(0.3)

                # 7. 如果要HDMI2，再向右1次
                if source_index == 2:
                    print("7. 向右1次到HDMI2")
                    adb_right()
                    time.sleep(0.3)

                # 8. Enter确认选择
                print("8. Enter确认选择")
                adb_enter()
                time.sleep(2)

                print(f"华为电视UI导航切换到HDMI{source_index}完成")
                return True
            else:
                print("华为电视暂不支持非HDMI源切换")
                return False

        elif self.is_hisense_tv():
            # 海信电视的导航逻辑
            if source_type == 'hdmi' and source_index:
                print(f"广播失败，使用UI导航切换到HDMI{source_index}")
                # 海信电视UI导航：HDMI1=0下, HDMI2=1下, HDMI3=2下, HDMI4=3下
                down_count = source_index - 1
                for i in range(down_count):
                    adb_down()
                    time.sleep(0.5)
            else:
                print("海信电视暂不支持非HDMI源切换")
                return False

        else:
            # 小米或其他品牌，使用原有逻辑
            print("使用默认输入源切换逻辑")
            return False

        # 确认选择
        adb_center()
        time.sleep(3)
        print(f"已切换到{source_type.upper()}{source_index if source_index else ''}")
        return True

    def test_switch_random_source(self):
        manufacturer = self.get_tv_manufacturer()
        print(f"检测到电视厂商: {manufacturer}")

        if self.is_tcl_tv() or self.is_hisense_tv() or self.is_huawei_tv():
            # TCL或海信电视：检查HDMI连接状态
            available_sources = self.get_available_sources()

            if not available_sources:
                print("没有可用的输入源，跳过切换")
                return

            # 从可用的输入源中随机选择
            selected_source = random.choice(available_sources)
            print("=================================================")
            print(f"           {selected_source}")

            if selected_source.startswith('HDMI'):
                hdmi_port = int(selected_source[-1])  # 提取HDMI端口号
                self.switch_to_source_by_ui_navigation('hdmi', hdmi_port)
        else:
            # 小米或其他品牌，使用原有逻辑
            case_id = random.randrange(4)
            print("=================================================")

            if case_id == 0:
                if self.get_HDMI_number() == '1':
                    print("           HDMI1")
                    self.SwitchHDMI1()
                elif self.get_HDMI_number() == '2':
                    print("           HDMI2")
                    self.SwitchHDMI2()
                elif self.get_HDMI_number() == '3':
                    print("           HDMI3")
                    self.SwitchHDMI3()
            elif case_id == 1:
                print("           TV")
                self.TV()
            elif case_id == 2:
                print("           AV")
                self.AV()
            elif case_id == 3:
                print("           DTMB")
                self.DTMB()

        print("=================================================")
        time.sleep(30)
        adb_home()



    def test_aivoice_insource(self):
        case_id = random.randrange(3)
        # case_id = 0
        if case_id == 0:
            print("=================================================\n")
            if self.get_HDMI_number() == '1':
                print("           HDMI\n")
                self.SwitchHDMI1()
            elif self.get_HDMI_number() == '2':
                print("           HDMI2\n")
                self.SwitchHDMI2()
            elif self.get_HDMI_number() == '3':
                print("           HDMI3\n")
                self.SwitchHDMI3()
            print("=================================================\n")
        elif case_id == 1:
            print("=================================================\n")
            print("           AV\n")
            print("=================================================\n")
            self.AV()
        elif case_id == 2:
            print("=================================================\n")
            print("           DTMB\n")
            print("=================================================\n")
            self.DTMB()
        time.sleep(30)
        self.adjust_Aivoice()
        time.sleep(30)

    def adjust_Aivoice(self):
        start_service(" com.xiaomi.mitv.settings/.TvSettingService -a com.xiaomi.mitv.settings.sound_settings")
        if d(text='杜比音效').exists:
            d(text='杜比音效').click()
            d(text='杜比音效').click()
            [adb_down() for i in range(2)]
            adb_center()
            adb_down()
        elif d(text='小米音效').exists:
            [adb_down() for i in range(3)]
        adb_center()
        [adb_down() for i in range(2)]
        [adb_left() for i in range(10)]
        [adb_right() for i in range(random.randrange(11))]
