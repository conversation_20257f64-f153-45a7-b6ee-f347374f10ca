 #!/usr/bin/python
#-*- encoding:utf-8 -*-
import unittest
from uiautomator import device as d
import time
import random
import subprocess
from testconfig import config



class WaterTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.wakeup()
        time.sleep(10)
        d.press.back()
        time.sleep(1)
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """

    def Water_Range(self):
        for i in range(5):
            d.press.right()
            time.sleep(1)
        for i in range(6):
            d.press.left()
            time.sleep(1)
        for i in range(3):
            d.press.up()
            time.sleep(1)
            d.press.down()
            time.sleep(1)

    def back_home(self):
        d.press.home()
        d.wait.update(timeout=5000, package_name="com.xiaomi.dsignage.home")
        assert d(resourceId="android:id/content").exists, 'loading home timeout'

    def Down_back(self):
        time.sleep(2)
        d.press.down()
        time.sleep(2)
        d.press.back()

    #浏览桌面
    def testWater_PatchWall(self):
        for i in range(3):
            self.back_home()
            self.Water_Range()

    # 电视管家
    def testWater_Tvmanager(self):
        d.press.home()
        self.back_home()
        for _ in range(random.randint(5,15)):
            d.server.adb.raw_cmd('shell am start -n  com.xiaomi.mitv.tvmanager/.boost.ProcessManagerActivity')
            time.sleep(3)
            d.press.enter()
            time.sleep(3)
            self.back_home()
            time.sleep(30)

    #launch and exit mitvshow 小米演示
    def testWater_Mitvshow(self):
        self.back_home()
        for _ in range(random.randint(15,18)):
            d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitvshow/.activity.ScreenSaverActivity')
            d.wait.update(timeout=5000, package_name="com.xiaomi.mitvshow"), 'not at mitvshow'
            time.sleep(10)
            d.press.enter()
            time.sleep(10)
            self.back_home()

     #u盘播放
    def testWater_DsignageShow(self):
        self.back_home()
        # launch and exit usb
        for _ in range(random.randint(13,18)):
            d(text="已接入").click.wait(timeout=3000)
            d.press.enter()
            #d.server.adb.raw_cmd('shell am start -n com.xiaomi.dsignage.show/com.xiaomi.dsignage.player_ui.UsbPlayerActivity')
            d.wait.update(timeout=5000, package_name="com.xiaomi.dsignage.show"), 'not at dsignage.show'
            time.sleep(20)
            self.back_home()
        #swtich time
        #d.server.adb.raw_cmd ('shell am start -n com.xiaomi.dsignage.show/com.xiaomi.dsignage.player_ui.UsbPlayerActivity')
        d(text="已接入").click.wait(timeout=3000)
        d.press.enter()
        d.wait.update(timeout=5000, package_name="com.xiaomi.dsignage.show"), 'not at dsignage.show'
        for _ in range(random.randint(5,10)):
            d.press.menu()
            time.sleep(2)
            d.press.right()
            time.sleep(2)
            d.press.enter()
            time.sleep(10)
            try:
                res = subprocess.check_call('adb shell screencap -p /sdcard/screencap.png',shell=True)
                print ('res:', res)
            except subprocess.CalledProcessError as exc:
                print ('returncode:', exc.returncode)
                print ('cmd:', exc.cmd)
                print ('output:', exc.output)
        self.back_home()
            #switch video
        for _ in range(random.randint(5,10)):
            #d.server.adb.raw_cmd('shell am start -n com.xiaomi.dsignage.show/com.xiaomi.dsignage.player_ui.UsbPlayerActivity')
            d(text="已接入").click.wait(timeout=3000)
            d.press.enter()
            d.wait.update(timeout=5000, package_name="com.xiaomi.dsignage.show"), 'not at dsignage.show'
            for _ in range(random.randint(5, 10)):
                d.press.left()
                time.sleep(1)
            for _ in range(random.randint(6, 12)):
                d.press.right()
                time.sleep(1)
            self.back_home()

    #设置浏览一二级菜单
    def testWater_Settings(self):
        d.press.home()
        for _ in range(random.randint(15,20)):
            d.server.adb.raw_cmd("shell am start -n com.xiaomi.mitv.settings/.entry.MainActivity")
            d.wait.update(timeout=5000, package_name="com.xiaomi.mitv.settings"), 'not at settings'
            d(text="声音与显示").click.wait(timeout=5000)
            time.sleep(2)
            d.press.enter()
            self.Down_back()
            d.press.back()
            d.wait.update(timeout=5000, package_name="com.xiaomi.mitv.settings"), 'not at settings'
            d(text="网络").click()
            time.sleep(2)
            d.press.enter()
            self.Down_back()
            d(text="通用设置").click()
            time.sleep(2)
            d.press.enter()
            time.sleep(2)
            for _ in range(random.randint(3,5)):
                d.press.down()
                time.sleep(2)
            for _ in range(random.randint(2,4)):
                d.press.up()
                time.sleep(2)
            d.press.back()
            d(text="关于").click()
            time.sleep(2)
            d.press.enter()
            for _ in range(random.randint(2,6)):
                d.press.down()
                time.sleep(1)
            for _ in range(random.randint(3,7)):
                d.press.up()
                time.sleep(1)
            d.press.back()
            self.back_home()








