#!/usr/bin/python
# -*- coding:utf-8 -*-

import time
import unittest
import random
from uiautomator import device as d
from script.testcases.adb_command import *
from script.testcases.PhotonEngine import AppStartTest

keys = ['up', 'down', 'left', 'right']

PLAY_time = 300
PLAY_TIME = 60


class VideoPlayerTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        try:
            d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
            d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
            d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
            d.watcher("EXIT_VST_THIRD_APP").when(
                textContains="退出").click(text="退出")
            d.watcher("EXIT_MOLI_THIRD_APP").when(
                textContains="退出").click(text="退出")
            d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
                textContains="确定退出电视猫视频？").click(text="确定")
            d.watcher("EXIT_XUNLEI_THIRD_APP").when(
                textContains="是否退出迅雷看看？").click(text="确定")
            d.watcher("INSTALL_NEW_VERSION").when(
                textContains="您要安装此应用的新版本吗？").click(text="取消")
            d.watcher("EXIT_SOHU_APP").when(
                textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
            d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
            d.watcher("PASS_VST_UPDATE1").when(
                textContains="下次更新").click(text="下次更新")
            d.watcher("PASS_TOGIC_UPDATE").when(
                packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
            d.watcher("PASS_VST_UPDATE2").when(
                textContains='根据国家现行政策规定').press('enter')
            d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
            d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
            d.watcher("PASS_STOP_PLAY").when(
                textContains='确认退出').click(text="确认退出")

            d.watcher("PASS_APP_Popup").when(text="按【返回】可以关闭弹窗哦").press('back')
            d.watcher("PASS_APPKEEP_Popup1").when(text="按返回键退出播放").press('back')
            d.watcher("PASS_APPKEEP_Popup2").when(
                textContains="访问您设备上的照片、媒体内容和文件吗？").press('enter')
            d.watcher("PASS_APPAIQIYI_Popup").when(
                textContains="按【返回键】").press('back')
            d.watcher("PASS_APPBilibili_Popup").when(
                text="用户协议及隐私政策 ").press('enter')
            d.watcher("PASS_APPQQmusic_Popup").when(
                textContains="我知道了").press('enter')
            d.watcher("PASS_APPPPsport_Popup").when(
                textContains="同意").press('enter')
            d.watcher("PASS_APPPPsport_Popup2").when(
                textContains="我知道了").press('enter')
            d.watcher("PASS_APPPPsport_Popup2").when(
                textContains="允许CIBN聚体育拨打电话和管理通话吗？").press('enter')
            d.watcher("PASS_APPCloudvideo_popup").when(
                textContains="再看看").press('enter')


            d.watcher(("Install app-uiautomator-test.apk")).when(textContains="app-uiautomator-test.apk").press.up.center.down.center()  # not work

            d.wakeup()
            time.sleep(10)
            back2home_page()

        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        # time.sleep(1)
        # d.press.home()
        except IOError as exp:
            print(str(exp))
            self.restart_uiautomator()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")

        # d.press.home()
        back2home_page()


    def restart_uiautomator(self):
        for i in range(3):
            d.server.stop()
            time.sleep(5)
            d.server.start()
            time.sleep(5)

    def press_wait_for_new_window(self,direction,timeout=2000):
        """
        点击导航键，并等待新窗口加载，默认加载时间为2000毫秒
        这里等待新窗口不指定包名，如果需要指定包名，则自己使用d.wait.update(timeout=xxx,package=xxxx)
        """
        if direction == "home":
            d.press.home()
        elif direction == 'back':
            d.press.back()
        elif direction == "up":
            d.press.up()
        elif direction == "down":
            d.press.down()
        elif direction == "left":
            d.press.left()
        elif direction == "right":
            d.press.right()
        elif direction == "enter":
            d.press.enter()
        elif direction == "center":
            d.press.center()
        d.wait.update(timeout=timeout)

    '''
        def testPlayPatchVideo(self):
        print("INSTRUMENTATION_STATUS: title=播放视频")    # 用例中文描述
        for i in range(3):
            print("INSTRUMENTATION_STATUS: CaseRound={}".format(i))    # case内部循环第几轮
            d.press.home()
            d.wait.update(timeout=2000)
            d.press.back()
            d.wait.update(timeout=2000)
            d.press.down()
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000, package_name="com.mitv.mivideoplayer")   # 进入这个包
            d.press.left()   # 左一步是为了让他播放第一集，否则进来默认是“开通会员”控件
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000)
            # d(resourceId="com.mitv.tvhome:id/di_img")[1].click() # 开通影视会员右侧的第一个视频
            # time.sleep(2)
            # d.press.enter()
            # time.sleep(2)
            # d(resourceId="com.mitv.mivideoplayer:id/play_btn").click()  # 点击播放

            # assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'video not start!'
            if d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=200):
                time.sleep(100)
            # assert (i < 1)
            print("INSTRUMENTATION_STATUS: CaseStep=testPlayPatchVideo round {} finished".format(i))





            # 桌面向上选择用户反馈
            # self.touch_up()
            # print("INSTRUMENTATION_STATUS: CaseStep=用户反馈")
            # time.sleep(2)
            # assert d(text='用户反馈').wait.exists(timeout=6000), '用户反馈icon not find '
            # d(text='用户反馈').click()
            # time.sleep(2)
            # d.press.enter()
            # time.sleep(2)
            # for _ in range(3):
            #     d.press.enter()
            #     time.sleep(2)
            #     for _ in range(6):
            #         d.press.down()
            #     time.sleep(2)
            #     d.press.back()
            #     time.sleep(2)
            #     d.press.right()
            #     time.sleep(2)
            print("INSTRUMENTATION_STATUS: CaseStep=testPlayPatchVideo round {} finished".format(i))
    '''

    def testPatchwall_video(self):
        """
        随机播放桌面主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        adb_home()
        time.sleep(5)
        adb_center()
        time.sleep(5)
        adb_down()
        time.sleep(2)
        adb_down()
        time.sleep(2)
        adb_center()
        played = 0
        while played < 5:
            print("check played time:", played)
            played += 1
            print("播放视频")  # 播放视频
            time.sleep(5)
            print("调节音量")
            for v in range(5):  # 调节音量
                adb_volume_up()
                time.sleep(1)
            for o in range(5):
                adb_volume_down()
                time.sleep(1)
            time.sleep(PLAY_time)
            call_xiaoai()
            time.sleep(5)
            print("暂停")
            for i in range(3):
                adb_center()  # 暂停
                time.sleep(5)
                adb_center()
                time.sleep(10)
            print("快进")
            for r in range(10):  # 快进
                adb_right()
            adb_center()
            time.sleep(10)
            print("快退")
            for l in range(4):  # 快退
                adb_left()
            adb_center()
            time.sleep(10)
            a = timeout_command('adb shell settings get system mitv.short.power.action')
            print(a)
            print("调节音量")
            for v in range(5):  # 调节音量
                adb_volume_up()
                time.sleep(1)
            for o in range(5):
                adb_volume_down()
                time.sleep(1)
            print("进退详情页")
            for q in range(3):  # 进退详情页
                # time.sleep(5)
                adb_back()
                time.sleep(5)
                adb_center()
                time.sleep(20)
                call_xiaoai()
            frozenxiaoai()

    def testOnline_video(self):
        """
        随机播放桌面主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        adb_home()
        time.sleep(5)
        adb_center()
        time.sleep(5)
        adb_down()
        time.sleep(2)
        adb_down()
        time.sleep(2)
        adb_center()
        print("播放视频")  # 播放视频
        time.sleep(PLAY_time)
        for i in range(3):
            print("播放视频浏览菜单")
            [adb_down() for i in range(2)]  # 播放视频浏览菜单
            #time.sleep(2)
            [adb_right() for i in range(5)]
            [adb_left() for i in range(5)]
            adb_back()
            time.sleep(60)
            print("切集")
            adb_down()  # 切集
            time.sleep(2)
            adb_right()
            adb_center()
            time.sleep(120)
        print("切换清晰度")
        [adb_down() for i in range(2)]  # 切换清晰度
        adb_right()
        adb_up()
        num = 1
        print("check num:", num)
        if num % 2 == 0:
            adb_left()
            adb_center()
        else:
            adb_right()
            adb_center()
        num += 1
        adb_right()
        adb_center()
        time.sleep(30)
        # breathing_screen(4)
        time.sleep(5)
        call_xiaoai()
        frozenxiaoai()

    def testYunshiting(self):
        # for i in range(3):
            # assert d.server.adb.raw_cmd(
            #     'shell am start -n com.ktcp.video/.activity.MainActivity'), 'can not launch Yunshiting'
            # d.wait.update(timeout=10000, package_name='com.ktcp.video')
            # d(text='精选').wait.exists(timeout=6000)
            # d.press.back()
            # d.wait.update(timeout=2000)
        back2home_page()
        app_name = "com.ktcp.video/.activity.MainActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # 同意用户协议
        if "com.ktcp.video/.activity.PrivacyAgreementActivity" in package:
            adb_center()
            time.sleep(2)
        d(text='精选').wait.exists(timeout=6000)
        if d(text='精选').exists:
            d(text='精选').click()
        d.wait.update(timeout=2000)
        for _ in range(3):
            d.press.right()
            d.wait.update(timeout=2000)
        d.press.down()
        d.wait.update(timeout=2000)
        played = 0
        while played < MOVIES:
            print("check played time:",played)
            d.press.enter()
            d.wait.update(timeout=3000)
            d.press.left()
            d.wait.update(timeout=2000)
            d.press.enter()
            played += 1
            print("播放视频")
            time.sleep(10)
            print("调节音量")
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
            time.sleep(PLAY_time)
            call_xiaoai()
            print("暂停")
            adb_center()
            time.sleep(2)
            adb_center()
            print("切集")
            time.sleep(1)
            adb_down()
            adb_right()
            adb_center()
            print("切集等待")
            time.sleep(100)
            time.sleep(PLAY_TIME)
            d.press.back()
            if d(textContains="退出播放").wait.exists(timeout=6000):
                d(textContains='退出播放').click()
            d.press.back()
            d.wait.update(timeout=2000)
            d.press.down()
            d.wait.update(timeout=2000)
            # breathing_screen(4)
            time.sleep(5)
            start_app(app_name)
        for _ in range(3):
            d.press.back()
            d.wait.update(timeout=1000)
        if d(textContains="退出了").wait.exists(timeout=6000):
            d(textContains='退出了').click()
            d.wait.update(timeout=5000)
        frozenxiaoai()


    def testBilibili(self):
        # for i in range(3):
            # assert d.server.adb.raw_cmd(
            #     'shell am start -n com.xiaodianshi.tv.yst/.ui.main.MainActivity'), 'can not launch bilibili'
            # d.wait.update(timeout=12000, package_name='com.xiaodianshi.tv.yst')
            # d(text='推荐').wait.exists(timeout=6000)
        back2home_page()
        app_name = "com.xiaodianshi.tv.yst/.ui.main.MainActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # 处理弹窗
        if "com.xiaodianshi.tv.yst/com.xiaodianshi.tv.yst.ui.messagedialog.MessageDialogActivity" in package:
            adb_back()
        # 同意用户协议
        if "com.xiaodianshi.tv.yst/.ui.introduction.IntroductionActivity" in package:
            adb_center()
        d.press.down()
        d.wait.update(timeout=2000)
        played = 0
        while played < 10:
            print("播放视频")
            print("check played time:", played)
            if d(textContains="全屏").wait.exists(timeout=2000):
                print("识别到全屏")
                d.press.enter()
            else:
                print("切换视频")
                d.press.down()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                # played += 1
                # continue
                print("调节音量")
                for i in range(5):
                    adb_volume_down()
                    adb_volume_up()
                print("快进")
                for i in range(5):
                    adb_right()
                adb_center()
                time.sleep(5)
            print("长播")
            call_xiaoai()
            time.sleep(PLAY_TIME)
            print("返回播放首页")
            d.press.back()
            d.wait.update(timeout=2000)
            # played += 1
            d.press.down()
            d.wait.update(timeout=2000)
            # time.sleep(15)
            # breathing_screen(4)
            time.sleep(5)
            start_app(app_name)
            played +=1

        for _ in range(5):
            d.press.back()
            d.wait.update(timeout=1000)
        d.press.enter()
        frozenxiaoai()

    def testAiQiYi(self):
        back2home_page()
        app_name = "com.gitvdemo.video/com.gala.video.app.epg.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.gitvdemo.video/com.gala.video.app.epg.HomeActivity'), 'can not launch AiQiYi'
        # time.sleep(5)
        # d.wait.update(timeout=5000, package_name='com.gitvdemo.video')
        # d(text='推荐').wait.exists()

        if d(textContains="同意并继续").exists:  # 同意用户协议
            adb_center()
            time.sleep(2)
        # 存储权限
        package = get_front_package()
        if "com.android.permissioncontroller/.permission.ui.GrantPermissionsActivity" not in package:
            adb_center()
            time.sleep(2)
        print("退出广告页推送")
        adb_back()
        '''
                if d(textContains="立即更新").wait.exists(timeout=15000):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()

        if d(textContains="解码器补丁").wait.exists(timeout=15000):
            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.down()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.enter()
        d.press.down()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        '''
        played = 0
        adb_right()
        adb_down()
        adb_center()
        print("小窗播放")
        time.sleep(10)
        adb_back()
        time.sleep(5)
        adb_down()
        adb_center()
        time.sleep(10)
        #         d.press.enter()
        while played < MOVIES:
            print("check played time:", played)
            played += 1
            print("播放视频")  # 播放视频
            time.sleep(2)
            print("调节音量")
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
                call_xiaoai()
            time.sleep(PLAY_time)
            print("快进")
            for i in range(5):
                adb_right()
            adb_center()
            time.sleep(5)
            print("暂停")
            d.press.enter()
            time.sleep(10)
            adb_center()
            time.sleep(2)
            print("切换详情页")
            adb_back()
            time.sleep(3)
            adb_center()
            time.sleep(10)
            print("切换分辨率")  # 切换分辨率
            adb_down()
            adb_right()
            adb_center()
            num = 1
            print("check num:", num)
            if num % 2 == 0:
                adb_left()
                adb_center()
            else:
                adb_right()
                adb_center()
            # breathing_screen(4)
            time.sleep(5)
            start_app(app_name)
            num += 1
            '''
            if d(text="全屏").wait.exists(timeout=5000):
                d.press.left()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.enter()
            else:
                played += 1
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                continue

            time.sleep(PLAY_TIME)
            for _ in range(2):
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
            played += 1
            d.wait.update(timeout=2000)
            '''
        for _ in range(3):
            d.press.back()
            d.wait.update(timeout=1000)
        if d(textContains="退出不看了").wait.exists(timeout=6000):
            d(textContains='退出不看了').click()
            d.wait.update(timeout=5000)
        adb_home()
        frozenxiaoai()




    def testYouku(self):
        back2home_page()
        app_name = "com.cibn.tv/com.youku.tv.home.activity.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.cibn.tv/com.youku.tv.home.activity.HomeActivity'), 'can not launch Youku'
        '''
        assert d.server.adb.raw_cmd('shell am start -n com.cibn.tv/com.yunos.tv.yingshi.home.HomeActivity'), 'can not launch Youku'
        '''

        # time.sleep(2)
        # d.wait.update(timeout=2000, package_name='com.cibn.tv')
        if d(textContains="同意").exists:
            d(textContains="同意").click()
            # adb_center()
            time.sleep(2)
        if d(textContains="是否将优酷XL设置为开机自启动？").wait.exists(timeout=6000):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
        if d(textContains="立即更新").wait.exists(timeout=6000):
            d.press.back()
        # time.sleep(4)
        d.wait.update(timeout=4000)
        # assert d(text='电视剧').wait.exists(timeout=10000), "优酷-电视剧菜单 not found on screen"

        played = 0
        # assert d(text = '电影').exists(), '非电影界面'

        while played < 10:
            print("播放视频")
            played += 1
            print("check played time:", played)
            d.press.enter()
            if d(textContains="全屏").wait.exists(timeout=2000):
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.left()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.enter()
            else:
                d.press.back()
                # time.sleep(1)
                d.wait.update(timeout=1000)
                d.press.down()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                played += 1
                continue
            time.sleep(PLAY_TIME)
            print("调节音量")
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
            print("快进")
            for i in range(5):
                adb_right()
            adb_center()
            time.sleep(5)
            print("暂停")
            d.press.enter()
            time.sleep(10)
            adb_center()
            time.sleep(2)
            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Youku-播放视频 未开始！'

            if d(textContains="非常抱歉，该节目暂时无法在TV端播放").wait.exists(timeout=6000):
                d.press.right()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                d.press.enter()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.back()
            # time.sleep(3)
            d.wait.update(timeout=3000)
            d.press.back()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.down()
            # breathing_screen(4)
            time.sleep(5)
            start_app(app_name)

        for _ in range(5):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.home()
        frozenxiaoai()


    def test_Mangotv(self):
        back2home_page()
        app_name = "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意").exists:
            adb_center()
            time.sleep(2)
        adb_center()  # 跳过开机广告1
        played = 0
        adb_back()  # 跳过广告2

        while played < 10:
            print("check played time:", played)
            d.press.enter()
            d.wait.update(timeout=3000)
            d.press.left()
            d.wait.update(timeout=2000)
            d.press.enter()
            played += 1
            print("播放视频")
            time.sleep(PLAY_TIME)
            d.press.back()
            if d(textContains="退出播放").wait.exists(timeout=6000):
                d(textContains='退出播放').click()
            d.press.back()
            d.wait.update(timeout=2000)
            d.press.down()
            d.wait.update(timeout=2000)
            call_xiaoai()
            # breathing_screen(4)
            time.sleep(5)
            start_app(app_name)
        for _ in range(3):
            d.press.back()
            d.wait.update(timeout=1000)
        if d(textContains="狠心退出").wait.exists(timeout=6000):
            d(textContains='狠心退出').click()
            d.wait.update(timeout=5000)
        frozenxiaoai()

    def test_changba(self):
        back2home_page()
        app_name = "com.changba.sd/com.changba.tv.module.spash.ui.SpashActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # time.sleep(5)
        # assert d.server.adb.raw_cmd(
        #     'shell am start -n com.changba.sd/com.changba.tv.module.main.ui.MainActivity'), 'cannot launch changba'
        # time.sleep(10)
        d.wait.update(timeout=10000, package_name='com.changba.sd')
        d.press.enter()
        # time.sleep(1)
        d.wait.update(timeout=1000)
        d.press.enter()
        self.pressRight(5)
        self.pressLeft(5)
        for i in range(5):
            d.press.enter()
            # time.sleep(5)
            d.wait.update(timeout=5000)
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.down()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        self.pressBack(3)
        d.press.home()
        frozenxiaoai()

    def pressRight(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.right()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def pressLeft(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.left()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass
    def pressBack(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.back()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass

    def test_huya(self):
        # 云视听虎电竞
        back2home_page()
        app_name = "com.huya.nftv/.startup.StartupActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意并继续").wait.exists(timeout=6000):
            d(textContains="同意并继续").click()
        #if "com.huya.nftv/com.huya.nftv.startup.StartupActivity" in package:
            #adb_center()
        #print('success')
        if d(textContains="我知道了").wait.exists(timeout=6000):
            d(textContains="我知道了").click()
        adb_down()
        adb_down()
        adb_center()
        for i in range(5):
            # step1
            for i in range(3):
                adb_volume_down()
                adb_volume_up()
            # step2
            adb_long_pressmenu()
            adb_back()
            # step3
            call_xiaoai()
            time.sleep(40)
            # step4
            adb_down()
            # step5
            [adb_quickright() for i in range(3)]
            time.sleep(30)
            # step6
            adb_center()
            time.sleep(5)
            adb_center()
            # step7
            [adb_quickleft() for i in range(3)]
            time.sleep(30)
            #adb_right()
            adb_down()
            [adb_quickright() for i in range(4)]
            [adb_quickleft() for i in range(4)]
            adb_back()
            adb_back()
            adb_down()
            adb_center()
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
        frozenxiaoai()

    def test_miguvideo(self):
        app_name = 'cn.miguvideo.migutv/.SplashActivity'
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").wait.exists(timeout=6000):
            d(textContains="同意并继续").click()
        [adb_quickdown() for i in range(5)]
        adb_back()
        # switch tab
        [adb_quickleft() for i in range(14)]
        [adb_quickright() for i in range(15)]
        adb_back()
        # playvideo
        [adb_down() for i in range(2)]
        adb_center()
        adb_center()
        # volume
        adb_volume_up()
        adb_volume_down()
        # 反复进退
        for i in range(5):
            adb_back()
            adb_center()
        call_xiaoai()
        time.sleep(60)
        adb_menu()
        adb_back()
        # breathing_screen()
        time.sleep(5)
        start_app(app_name)
        frozenxiaoai()




    def test_HuashuTV(self):
        #华数鲜时光
        adb_home()
        app_name = "com.ixigua.android.tv.wasu/com.ixigua.android.business.tvbase.base.app.schema.AdsAppActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").exists:
            adb_center()
            time.sleep(2)
        adb_back()
        if d(textContains="残忍退出").exists:
            adb_right()
            adb_center()
            time.sleep(2)
        else:
            adb_right()
            adb_left()
            adb_back()
            adb_right()
            adb_center()
        time.sleep(60)
        [adb_quickright() for i in range(10)]
        call_xiaoai()
        for i in range(3):
            adb_volume_down()
            adb_volume_up()
        adb_back()
        # breathing_screen()
        time.sleep(5)
        start_app(app_name)
        frozenxiaoai()


    def test_Dianshimao(self):
        #云视听电视猫
        back2home_page()
        app_name = "com.moretv.android/.StartActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        for _ in range(2):
            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)
        played = 0
        while played < MOVIES:
            d.press.enter()
            # time.sleep(3)
            d.wait.update(timeout=3000)
            if d(textContains="全屏").wait.exists(timeout=2000):
                d.press.enter()
            else:
                d.press.back()
                # time.sleep(1)
                d.wait.update(timeout=1000)
                d.press.down()
                # time.sleep(2)
                d.wait.update(timeout=2000)
                played += 1
                continue
            # assert d(className='android.view.View').wait.exists(timeout=20000), 'Dianshimao-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            for _ in range(2):
                d.press.back()
                # time.sleep(2)
                d.wait.update(timeout=2000)
            d.press.right()
            played += 1
            # time.sleep(2)
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
            d.wait.update(timeout=2000)
        for _ in range(3):
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
        d.press.enter()
        frozenxiaoai()


    def pressDown(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.down()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass



    def pessUp(self, n):
        if isinstance(n, int):
            for i in range(n):
                d.press.up()
                # time.sleep(1)
                d.wait.update(timeout=1000)
        else:
            pass



    def test_CctvNew(self):
        #央视频
        back2home_page()
        app_name = "com.newtv.cboxtv/com.newtv.host.LauncherActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意并继续").exists:
            adb_center()
        adb_down()
        adb_down()
        adb_center()
        for i in range(3):
            adb_volume_down()
            adb_volume_up()
        for i in range(5):
            adb_left()
            adb_down()
            adb_center()
            time.sleep(40)
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
        frozenxiaoai()

    def test_KuaiTv(self):
        #云视听快TV
        adb_home()
        app_name = "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").exists:
            adb_center()
        time.sleep(5)
        adb_center()
        adb_back()
        adb_back()
        time.sleep(60)
        [adb_quickright() for i in range(10)]
        call_xiaoai()
        # breathing_screen()
        time.sleep(5)
        start_app(app_name)
        # time.sleep(100)
        frozenxiaoai()

    def test_TVhome(self):
        #电视家3.0
        back2home_page()
        app_name = "com.tvrun.run/com.dianshijia.newlive.entry.SplashActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if "com.android.packageinstaller/com.android.packageinstaller.permission.ui.GrantPermissionsActivity" in package:
            adb_center()
        for i in range(5):
            adb_down()
            time.sleep(30)
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
                call_xiaoai()
                # 菜单切换
                adb_center()
                adb_quickleft()
                adb_down()
                adb_right()
                [adb_quickdown() for i in range(3)]
                adb_center()
                time.sleep(10)
                call_xiaoai()
                # breathing_screen()
                time.sleep(5)
                start_app(app_name)
        frozenxiaoai()



    def test_qqmusic(self):
        for i in range(10):
            back2home_page()
            app_name = "com.tencent.qqmusictv/.examples.NewMainActivity"
            start_app(app_name)
            package = get_front_package()
            # assert app_name.split("/")[0] in package  # 判断是否启动了该App
            print(package)
            for i in range(4):
                adb_center()
            time.sleep(30)
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
            frozenxiaoai()

    def test_kugou(self):
        for i in range(10):
            back2home_page()
            app_name = "com.dangbei.dbmusic/com.dangbei.dbmusic.model.welcome.ui.WelcomeActivity"
            start_app(app_name)
            package = get_front_package()
            # assert app_name.split("/")[0] in package  # 判断是否启动了该App
            print(package)
            """pop up window1"""
            if d(textContains="同意并继续").exists:
                adb_left()
                adb_center()
                time.sleep(2)
            """pop up window2"""
            if d(textContains="我知道了").exists:
                [adb_down() for i in range(3)]
                adb_center()
            """pop up window3"""
            if 'com.android.packageinstaller/com.android.packageinstaller.permission.ui.GrantPermissionsActivity' in package:
                adb_center()
            """pop up window4&5"""
            for i in range(3):
                adb_center()
            adb_up()
            adb_down()
            """play music """
            adb_right()
            adb_down()
            adb_center()
            time.sleep(2)
            adb_center()
            for i in range(5):
                adb_volume_down()
                adb_volume_up()
            time.sleep(30)
            frozenxiaoai()

    def testVideoHeadline(self):
        app_name ='com.duokan.videodaily/com.duokan.videodaily.videoclip.VideoClipMainActivity'
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        # setting
        adb_long_pressmenu()
        adb_up()
        adb_right()
        adb_center()
        for i in range(3):
            adb_quickright()
            adb_back()
            adb_quickdown()
        adb_back()
        adb_down()
        adb_back()
        for i in range(5):
            adb_down()
            adb_center()
            time.sleep(10)
            adb_back()
            call_xiaoai()
            for i in range(3):
                adb_volume_up()
                adb_volume_down()
                call_xiaoai()
                # breathing_screen()
                time.sleep(5)
                start_app(app_name)
        frozenxiaoai()

    def test_xiaojing(self):
        app_name = 'com.xiaojing.tv/com.tv.core.main.LiveActivity'
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        for i in range(4):
            adb_volume_down()
            adb_volume_up()
        for i in range(4):
            adb_down()
            time.sleep(30)
            call_xiaoai()
            adb_long_pressmenu()
        # 菜单切换
        adb_center()
        adb_quickleft()
        adb_down()
        adb_right()
        adb_center()
        time.sleep(10)
        # call_xiaoai()
        # breathing_screen()
        time.sleep(5)
        start_app(app_name)
        frozenxiaoai()

    def test_xiaojie(self):
        app_name = 'com.xiaojie.tv/com.tv.core.main.LiveActivity'
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="明天提醒").wait.exists(timeout=6000):
            d(textContains='明天提醒').click()
            adb_center()
        for i in range(4):
            adb_down()
            adb_volume_down()
            adb_volume_up()
            call_xiaoai()
            # breathing_screen()
            time.sleep(5)
            start_app(app_name)
            adb_long_pressmenu()
            adb_back()
        frozenxiaoai()



    def test_localvideo(self):
        back2home_page()
        app_name = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'
        start_app(app_name)
        time.sleep(3)
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click.wait(timeout=3)
        d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click.wait(timeout=3)
        for i in range(5):
            try:
                assert d(text=u'1A播放压测码流').wait.exists(
                    timeout=10000), 'can not find folder'
                d(text=u'1A播放压测码流').click()
                print('find folder')
                break
            except Exception as exp:
                print(str(exp))
                [adb_quickdown() for i in range(9)]
        time.sleep(5)
        adb_center()
        for i in range(30):    # 多循环几次
            adb_center()
            if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/no_file_layout', text='未发现可支持的视频、音乐、图片、文档文件').wait.exists(timeout=500):
                adb_back()
            else:
                adb_center()
                adb_volume_up()
                adb_volume_down()
                time.sleep(5)
                adb_long_pressmenu()
                time.sleep(3)
                adb_back()
                time.sleep(30)
                [adb_back() for i in range(2)]
            adb_down()
        frozenxiaoai()


    def test_netease(self):
        print("INSTRUMENTATION_STATUS: title=启动网易云音乐应用")
        app_name = "com.netease.cloudmusic.tv/com.netease.cloudmusic.app.LoadingActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意协议并进入").exists:
            adb_center()
        if d(textContains="允许").exists:
            adb_center()
        print("INSTRUMENTATION_STATUS: CaseStep=等待三方应用内广告预加载")
        time.sleep(10)
        for i in range(10):
            print("INSTRUMENTATION_STATUS: CaseStep=随机播放音乐")
            adb_center()
            time.sleep(10)
            [adb_volume_up() for i in range(5)]
            call_xiaoai()
            time.sleep(30)
            [adb_volume_down() for i in range(5)]
            adb_long_pressmenu()
            adb_back()
            time.sleep(2)
            [adb_back() for i in range(2)]
            adb_right()





    def test_children(self):
        print("INSTRUMENTATION_STATUS: title=启动儿童思维训练应用")
        app_name = 'com.lutongnet.nldmx/.MainActivity'
        start_app(app_name)
        [adb_center() for i in range(2)]
        package = get_front_package()
        print(package)
        print("INSTRUMENTATION_STATUS: CaseStep=等待三方应用内广告预加载")
        time.sleep(10)
        call_xiaoai()
        time.sleep(2)
        adb_long_pressmenu()
        adb_back()



    def test_ysgc(self):
        print("INSTRUMENTATION_STATUS: title=启动影视工厂TV应用")
        app_name = 'com.ysgctv.vip/.modules.splash.SplashActivity'
        start_app(app_name)
        package = get_front_package()
        print(package)
        print("INSTRUMENTATION_STATUS: CaseStep=等待三方应用内广告预加载")
        time.sleep(10)
        for i in range(3):
            assert d(resourceId='com.ysgctv.vip:id/nav_item_title', text='推荐').wait.exists(timeout=300), 'launch ysgc failed'
            for i in range(2):
                d(resourceId='com.ysgctv.vip:id/nav_item_title', text='推荐').click.wait(timeout=300)
            print("INSTRUMENTATION_STATUS: CaseStep=随机播放推荐页视频")
            adb_down()
            for i in range(2):
                adb_down()
                adb_center()
                if d(text='全屏').wait.exists(timeout=3):
                    d(text='全屏').click.wait(timeout=3)
                    [adb_volume_up() for i in range(5)]
                    time.sleep(60)
                    call_xiaoai()
                    adb_long_pressmenu()
                    adb_back()
                    [adb_volume_down() for i in range(5)]
                    time.sleep(5)
                    [adb_back() for i in range(2)]
                else:
                    adb_back()
                    adb_down()
        adb_back()

    def test_douyu(self):
        print("INSTRUMENTATION_STATUS: title=启动斗鱼应用")
        app_name = 'com.douyu.xl.douyutv/.componet.SplashActivity'
        start_app(app_name)
        package = get_front_package()
        print(package)
        print("INSTRUMENTATION_STATUS: CaseStep=等待三方应用内广告预加载")
        time.sleep(10)
        if d(resourceId='com.douyu.xl.douyutv:id/arg', text='精选推荐').wait.exists(timeout=500):
            for i in range(2):
                d(resourceId='com.douyu.xl.douyutv:id/arg', text='精选推荐').click.wait(timeout=300)

        else:
            print('launch douyu failed')
        adb_down()
        adb_center()
        [adb_volume_up() for i in range(5)]
        time.sleep(60)
        call_xiaoai()
        adb_long_pressmenu()
        adb_back()
        [adb_volume_down() for i in range(5)]
        time.sleep(5)

    def adjust_Aivoice(self):
        start_service(" com.xiaomi.mitv.settings/.TvSettingService -a com.xiaomi.mitv.settings.sound_settings")
        if d(text='杜比音效').exists:
            d(text='杜比音效').click()
            d(text='杜比音效').click()
            [adb_down() for i in range(2)]
            adb_center()
            adb_down()
        elif d(text='小米音效').exists:
            [adb_down() for i in range(3)]
        adb_center()
        [adb_down() for i in range(2)]
        [adb_left() for i in range(10)]
        [adb_right() for i in range(random.randrange(11))]

    def test_AIvoice(self):
        start_app("com.ktcp.video/.activity.MainActivity")
        start_app("com.cibn.tv/com.youku.tv.home.activity.HomeActivity")
        start_app("com.gitvdemo.video/com.gala.video.app.epg.HomeActivity")
        start_app("com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity")
        start_app("com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity")
        start_app("com.tencent.qqmusictv/.examples.NewMainActivity")
        self.testOnline_video_os3()
        self.adjust_Aivoice()
        time.sleep(30)
        self.test_localvideo()
        self.adjust_Aivoice()
        time.sleep(30)

    def testOnline_video_os3(self):
        """
        随机播放桌面主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        adb_home()
        time.sleep(5)
        adb_left()
        time.sleep(2)
        [adb_down() for i in range(6)]
        time.sleep(5)
        adb_right()
        time.sleep(2)
        adb_center()
        time.sleep(5)
        print("播放视频")  # 播放视频
        [adb_left() for i in range(4)]
        adb_center()
        time.sleep(5)
        time.sleep(PLAY_time)
        for i in range(3):
            print("播放视频浏览菜单")
            [adb_down() for i in range(2)]  # 播放视频浏览菜单
            # time.sleep(2)
            [adb_right() for i in range(5)]
            [adb_left() for i in range(5)]
            adb_back()
            time.sleep(60)
            print("切集")
            adb_down()  # 切集
            time.sleep(2)
            adb_right()
            adb_center()
            time.sleep(120)
        print("切换清晰度")
        [adb_down() for i in range(2)]  # 切换清晰度
        adb_right()
        adb_up()
        num = 1
        print("check num:", num)
        if num % 2 == 0:
            adb_left()
            adb_center()
        else:
            adb_right()
            adb_center()
        num += 1
        adb_right()
        adb_center()
        time.sleep(30)
        # breathing_screen(4)
        time.sleep(5)
        call_xiaoai()
        frozenxiaoai()


