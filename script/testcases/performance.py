#!/usr/bin/python
# -*- coding:utf-8 -*-
import time
import unittest
import random
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import os
import time

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
PLAY_TIME_LONG = config_.getint('play_options','play_time_long')
THRESHOLD = config_.getint('play_options','threshold')

# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# THRESHOLD = int(config['play_options']['threshold'])
PLAY_time = 300
PLAY_TIME = 60


class systemappTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
        d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
        d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
        d.watcher("EXIT_VST_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
            textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(
            textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(
            packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("PASS_STOP_PLAY").when(
            textContains='确认退出').click(text="确认退出")

        d.watcher("PASS_APP_Popup").when(text="按【返回】可以关闭弹窗哦").press('back')
        d.watcher("PASS_APPKEEP_Popup1").when(text="按返回键退出播放").press('back')
        d.watcher("PASS_APPKEEP_Popup2").when(
            textContains="访问您设备上的照片、媒体内容和文件吗？").press('enter')
        d.watcher("PASS_APPAIQIYI_Popup").when(
            textContains="按【返回键】").press('back')
        d.watcher("PASS_APPBilibili_Popup").when(
            text="在屏幕顶端按上键可进入个人中心").press('enter')
        d.watcher("PASS_APPQQmusic_Popup").when(
            textContains="我知道了").press('enter')
        d.watcher("PASS_APPPPsport_Popup").when(
            textContains="同意").press('enter')
        d.watcher("PASS_APPPPsport_Popup2").when(
            textContains="我知道了").press('enter')
        d.watcher("PASS_APPPPsport_Popup2").when(
            textContains="允许CIBN聚体育拨打电话和管理通话吗？").press('enter')

        # d.wakeup()
        # time.sleep(10)
        d.wakeup()
        d.wait.update(timeout=10000)
        d.press.home()
        d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd(
        		    'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        d.press.enter()
        time.sleep(1)
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")

        d.press.home()

    def launchhome(self):
        time.sleep(2)
        for i in range(20):
            d.press.down()
            time.sleep(1)
        for i in range(10):
            d.press.up()
            time.sleep(1)
        for i in range(10):
            d.press.enter()
            time.sleep(2)
            if d(text='选集').wait.exists(timeout=6000):
                d.press.left()
                time.sleep(2)
                d.press.enter()
                time.sleep(PLAY_time)
                break
            elif d(textContains='全屏').wait.exists(timeout=6000):
                d.press.left()
                time.sleep(2)
                d.press.enter()
                time.sleep(PLAY_time)
                break
            else:
                d.press.back()
                time.sleep(1)
                d.press.down()
        time.sleep(2)
        d.press.home()

    # Patchwall

    def testlaunchHome(self):
        # 精选
        assert d(text='精选').wait.exists(timeout=6000), 'not at home'
        d(text='精选').click()
        time.sleep(2)
        self.launchhome()
        # VIP
        assert d(text='精选').wait.exists(timeout=6000), 'not at home'
        d(text='精选').click()
        time.sleep(2)
        d.press.right()
        self.launchhome()
        # 儿童
        assert d(text='儿童').wait.exists(timeout=6000), 'not at home'
        d(text='儿童').click()
        time.sleep(4)
        d.press.down()
        time.sleep(1)
        if d(text='同意并继续').wait.exists(timeout=2000):
            d.press.enter()
            time.sleep(1)
        self.launchhome()
        # 我的
        assert d(text='我的').wait.exists(timeout=6000), 'not at home'
        d(text='我的').click()
        time.sleep(2)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(PLAY_time)
        # 左右切tab
        time.sleep(1)
        assert d(text='精选').wait.exists(timeout=6000), 'not at home'
        d(text='精选').click()
        for i in range(20):
            d.press.right()
            time.sleep(1)
        for i in range(25):
            d.press.left()
            time.sleep(1)

    # 在线视频切换设置和分辨率

    def switchmenu(self):
        d.press.enter()
        time.sleep(1)
        d.press.enter()
        time.sleep(5)
        d.press.menu()
        time.sleep(1)
        d.press.enter()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(5)
        # 浏览一级二级菜单
        d.press.menu()
        time.sleep(2)
        for _ in range(5):
            d.press.right()
            time.sleep(1)
            d.press.left()
            time.sleep(1)
            d.press.down()
            time.sleep(1)
        time.sleep(10)
        d.press.menu()
        time.sleep(2)
        for _ in range(5):
            d.press.right()
            time.sleep(1)
            d.press.left()
            time.sleep(1)
            d.press.up()
            time.sleep(1)
        time.sleep(10)
        d.press.menu()
        time.sleep(2)
        for _ in range(8):
            d.press.down()
        time.sleep(2)
        for _ in range(7):
            d.press.right()
            time.sleep(2)

    def testVideoplayer(self):
        assert d(text='精选').wait.exists(timeout=6000), 'not at home'
        d(text='精选').click()
        time.sleep(1)
        d.press.down()
        time.sleep(2)
        for i in range(10):
            d.press.enter()
            time.sleep(2)
            if d(text='选集').wait.exists(timeout=6000):
                d.press.left()
                time.sleep(2)
                d.press.enter()
                # time.sleep(PLAY_time)
                for _ in range(10):
                    d.press.menu()
                    if d(text='清晰度').wait.exists(timeout=6000):
                        # 切换清晰度
                        self.testlaunchmenu()
                        break
                    else:
                        time.sleep(20)
                        continue
                break
            elif d(textContains='全屏').wait.exists(timeout=6000):
                d.press.left()
                time.sleep(2)
                d.press.enter()
                # time.sleep(PLAY_time)
                for _ in range(10):
                    d.press.menu()
                    if d(text='清晰度').wait.exists(timeout=6000):
                        # 切换清晰度
                        self.switchmenu()
                    else:
                        time.sleep(20)
                        continue
                break
            else:
                d.press.back()
                time.sleep(1)
                d.press.down()

    # 时尚画报

    def testlaunchGallery(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.tv.gallery/.GalleryActivity'), 'fail to launch gallery'
        # 上下浏览五分钟
        time.sleep(3)
        d.press.down()
        for _ in range(30):
            time.sleep(1)
            d.press.right()
        for _ in range(20):
            time.sleep(1)
            d.press.left()
        time.sleep(2)
        for _ in range(10):
            d.press.down()
        time.sleep(1)
        d.press.back()
        # 画报左右浏览
        time.sleep(1)
        d.press.left()
        time.sleep(1)
        d.press.enter()
        time.sleep(1)
        for _ in range(20):
            d.press.right()
            time.sleep(1)
        for _ in range(20):
            d.press.left()
            time.sleep(1)

    # 天气

    def testweather(self):
        time.sleep(3)
        assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.tweather/.TWeatherActivity'), 'cannot launch weather'
        for __index in range(10):
            time.sleep(3)
            d.press.right()
            time.sleep(3)
            if d(resourceId='com.xiaomi.tweather:id/add').exists:
                break

        assert d(text='城市管理').wait.exists(
            timeout=6000), '城市管理not exists'  # 添加一个城市

        d(resourceId='com.xiaomi.tweather:id/add').click()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d(text='北京').click()
        time.sleep(2)
        d.press.back()
        time.sleep(1)
        for _ in range(20):
            d.press.right()
            time.sleep(2)
            d.press.left()
            time.sleep(2)

        for __cities_index in range(8):
            d.press.right()
            time.sleep(3)
            if d(resourceId="com.xiaomi.tweather:id/delete").exists:
                break

        assert d(text='城市管理').wait.exists(
            timeout=6000), '城市管理not exists'  # 删除添加的城市
        d(resourceId="com.xiaomi.tweather:id/delete").click()
        time.sleep(2)
        # 上下浏览天气
        for i in range(8):
            d.press.left()
            time.sleep(2)
            if d(text="语音播报").exists:
                d.press.back()
                break
        for _ in range(5):
            for i in range(5):
                d.press.up()
                time.sleep(2)
            for i in range(5):
                d.press.down()
                time.sleep(2)
        d.press.back()
        time.sleep(2)
        for i in range(8):
            d.press.left()
            time.sleep(2)
            if d(text="语音播报").exists:
                for _ in range(20):
                    for _ in range(5):
                        d.press.down()
                    for _ in range(5):
                        d.press.up()
                time.sleep(1)
                d(text="语音播报").click()
                time.sleep(1)
                d.press.enter()
                time.sleep(20)
                break

    # ......................................................
    # 日历

    def testlaunchCalendar(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.mitv.calendar/.CalendarActivity'), 'cannot launch calendar'
        time.sleep(3)
        if d(text='是否授权小米帐号？').wait.exists(timeout=5000):
            d(text='取消').click()
            time.sleep(2)
        '''
        d(text='节日').click()
        time.sleep(2)
        assert d(text='国庆节').wait.exists(
            timeout=6000), '国庆节not exists'  # 进入节日中的国庆节再返回
        d(text='国庆节').click()
        time.sleep(2)
        d.press.back()
        '''
        # 浏览月盘
        for _ in range(10):
            d.press.up()
        for _ in range(12):
            d.press.left()
            time.sleep(1)
        for _ in range(3):
            d.press.down()
        time.sleep(1)
        for _ in range(100):
            d.press.right()
        time.sleep(1)
        for _ in range(100):
            d.press.left()
        time.sleep(1)
        d.press.home()
        time.sleep(1)

        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.mitv.calendar/.CalendarActivity'), 'cannot launch calendar again'
        time.sleep(3)
        # 浏览节日列表
        d(text='节日').click()
        time.sleep(1)
        festival_list = ['元旦', '腊八', '情人节', '除夕',
                         '春节', '元宵', '清明', '劳动节', '端午', '中秋']
        for _ in range(5):
            d.press.down()
            time.sleep(1)
        d.press.right()
        time.sleep(1)
        for _ in range(4):
            d.press.up()
            time.sleep(1)

        for i in range(10):
            d(text=festival_list[i]).click()
            time.sleep(2)
            d(text='节日').click()
            time.sleep(2)
        d.press.home()

    # testlaunchCalendar()
    # 视频头条

    def testPlayDuokanVideo(self):

        d.press.home()
        time.sleep(1)
        d.press.home()
        time.sleep(2)
        for _ in range(2):
            d.press.down()
            time.sleep(2)
        d.press.right()
        time.sleep(1)
        d.press.enter()
        assert d(packageName='com.duokan.videodaily').wait.exists(
            timeout=20000), 'duokanvideo not start!'
        for _ in range(40):
            d.press.down()
        time.sleep(2)
        for _ in range(30):
            d.press.up()
        time.sleep(2)
        for _ in range(30):
            d.press.down()
            time.sleep(2)
            d.press.enter()

        d.press.home()

    # 电视商城

    def testlaunchTVshop(self):
        assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.shop/.ShopHomeActivity'), 'fail to launch TVshop'
        # 左右切换Tab页
        for i in range(20):
            for _ in range(2):
                d.press.right()
                time.sleep(1)
            for _ in range(2):
                d.press.left()
                time.sleep(1)
        # 上下浏览商品
        for _ in range(2):
            d.press.left()  # 确保在首页
        time.sleep(1)
        d.press.down()
        for _ in range(10):
            time.sleep(1)
            d.press.down()
            for _ in range(5):
                time.sleep(1)
                d.press.right()
        time.sleep(1)
        d.press.back()

        # 浏览电视详情
        time.sleep(2)
        d.press.down()
        time.sleep(2)
        d.press.down()
        time.sleep(2)
        d.press.right()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        # 点击进入电视列表
        for _ in range(5):
            d.press.enter()
            if d(className='com.mitv.shop.videoplayer.videoview.VideoView').wait.exists(timeout=2000):
                time.sleep(30)
            for _ in range(20):
                d.press.down()
            time.sleep(2)
            d.press.back()
            time.sleep(1)
            d.press.right()
        d.press.home()

    # 应用商店

    def testLaunchAndExitAppStore(self):
        """
    launch  app store and exit
        """
        d.server.adb.raw_cmd('shell am start -n com.mitv.tvhome/.AppStoreActivity')
        time.sleep(3)
        assert d(text='推荐').wait.exists(
            timeout=20000), 'launch App Store failed!'
        d(text='推荐').click()
        time.sleep(3)
        # 上下浏览4分钟
        for _ in range(4):
            for i in range(20):
                d.press.down()
                time.sleep(1)
            for i in range(15):
                d.press.up()
                time.sleep(1)
        # 左右浏览2分钟
        for _ in range(2):
            for i in range(20):
                d.press.right()
                time.sleep(1)
            for i in range(20):
                d.press.left()
                time.sleep(1)

    # 设置

    def testsetting(self):
        d.press.home()
        time.sleep(2)
        for i in range(3):
            d.press.up()
            time.sleep(1)
        assert d(text='设置').wait.exists(timeout=6000), '设置icon not find '
        d(text='设置').click()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        for _ in range(8):
            d.press.enter()
            time.sleep(2)
            for _ in range(5):
                d.press.down()
                time.sleep(1)
            for _ in range(5):
                d.press.up()
                time.sleep(1)
            d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(2)

    # 用户手册

    def testLaunchHandbook(self):
        assert d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.handbook/.MainActivity'), 'open handbook failed'

        for i in range(12):
            time.sleep(1)
            d.press.enter()
            for i in range(3):
                d.press.down()
                time.sleep(2)
            d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(1)
        d.press.back()

    # 电视管家
    def testlaunchTVmanager(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.mitv.tvmanager/.MainTvManagerActivity'), 'cannot launch TVmanager'
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        for _ in range(4):
            for _ in range(9):
                d.press.right()
                time.sleep(2)
            for _ in range(9):
                d.press.left()
                time.sleep(2)

    # 定时提醒

    def testclock(self):

        d.server.adb.raw_cmd(
            'shell am start -n com.mitv.alarmcenter/.ui.AlarmCenterActivity'), 'open notification failed'
        # 计数器
        add = 0

        # 建10个提醒
        while add < 10:
            if d(resourceId='com.mitv.alarmcenter:id/add_alarm').wait.exists(timeout=2000):
                d(resourceId='com.mitv.alarmcenter:id/add_alarm').click()
                time.sleep(1)
                if d(text='编辑时间').wait.gone(timeout=2000):
                    d.press.enter()
                    time.sleep(2)

                d.press.left()
                time.sleep(1)
                d.press.left()
                time.sleep(1)
                d.press.down()
                time.sleep(1)

                for _ in range(2):
                    d.press.enter()
                    time.sleep(1)

                add += 1

            else:
                d.press.down()
        # 浏览提醒
        for _ in range(4):
            for _ in range(10):
                d.press.down()
                time.sleep(1)
            for _ in range(10):
                d.press.up()
                time.sleep(1)

    # 无线投屏
    def testscreenproject(self):
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.smartshare/.MainActivity')
        assert d(text=u'手机投屏').wait.exists(timeout=4000), 'not in 投屏'

        for _ in range(4):
            for _ in range(3):
                d.press.right()
                time.sleep(2)
            d.press.down()
            time.sleep(2)
            for _ in range(3):
                d.press.left()
                time.sleep(2)
            d.press.down()
            time.sleep(2)
            d.press.right()
            time.sleep(2)

    # 相册

    def testgallery(self):
        d.server.adb.raw_cmd('shell am start -n com.mitv.gallery/.activity.HomePageActivity')

        for _ in range(2):
            d.press.right()
            time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(1)
        # 浏览相册2min
        for _ in range(6):
            for _ in range(3):
                d.press.down()
                time.sleep(1)
            for _ in range(3):
                d.press.right()
                time.sleep(1)
            for _ in range(3):
                d.press.down()
                time.sleep(1)
            for _ in range(3):
                d.press.left()
                time.sleep(1)
        # 点击图片并切换浏览2min
        d.press.enter()
        time.sleep(1)
        for _ in range(8):
            for _ in range(5):
                d.press.right()
                time.sleep(1)
            for _ in range(5):
                d.press.left()
                time.sleep(1)

    # 高清播放器
    def testLaunchMediaExplorer(self):
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity')
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        # 左右切换tab页
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click()
        time.sleep(1)
        for _ in range(8):
            for _ in range(4):
                d.press.right()
                time.sleep(1)
            for _ in range(4):
                d.press.left()
                time.sleep(1)
        # 浏览U盘内文件
        time.sleep(1)
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        assert d(text=u'千张图片').wait.exists(
            timeout=10000), 'can not find U disk'
        d(text=u'千张图片').click()
        time.sleep(1)
        for _ in range(5):
            for _ in range(20):
                d.press.down()
                time.sleep(1)
            for _ in range(10):
                d.press.up()
                time.sleep(1)

    # 画中画浏览菜单

    def testtvplayer(self):
        d.press.home()
        assert d(text='我的').wait.exists(
            timeout=20000), 'no at home'
        d(text='我的').click()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(1)
        # 进源浏览菜单
        for _ in range(3):
            d.press.menu()
            if d(text='图像').wait.gone(
                    timeout=2000):
                time.sleep(2)
                d.press.menu()
            for _ in range(3):
                for _ in range(2):
                    time.sleep(1)
                    d.press.down()
                for _ in range(2):
                    time.sleep(1)
                    d.press.up()
            for _ in range(3):
                d.press.right()
                time.sleep(1)
                for _ in range(4):
                    time.sleep(1)
                    d.press.down()
                d.press.left()
                time.sleep(1)
                d.press.down()
                time.sleep(1)
            d.press.menu()
            time.sleep(1)

    # 高清播放器播放本地视频

    def testlocalvideo(self):
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity')
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'

        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        assert d(text=u'电影').wait.exists(
            timeout=10000), 'can not find U disk'
        d(text=u'电影').click()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(5)
        for _ in range(2):
            d.press.menu()
            time.sleep(2)
            for _ in range(4):
                d.press.right()
                time.sleep(1)
                d.press.left()
                time.sleep(1)
                d.press.down()
                time.sleep(1)
            time.sleep(10)
            d.press.menu()
            time.sleep(2)
            for _ in range(4):
                d.press.right()
                time.sleep(1)
                d.press.left()
                time.sleep(1)
                d.press.up()
                time.sleep(1)
            time.sleep(10)

    # 通知中心

    def testlaunchNotification(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.mitv.systemui/.notification.notificationcenter.NotificationCenterActivityAlias'), 'open notification failed'
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        for _ in range(2):
            for _ in range(4):
                d.press.down()
                time.sleep(1)
            for _ in range(4):
                d.press.up()
                time.sleep(1)

    # 游戏中心

    def testbrowseGamecenter(self):
        """
launch  app store and exit
"""
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mibox.gamecenter/.MainActivity')
        time.sleep(2)
        assert d(text="精品").wait.exists(
            timeout=2000), 'Game Center icon not found!'
        d(text='精品').click()
        time.sleep(2)
        for _ in range(4):
            for _ in range(3):
                d.press.right()
                time.sleep(1)
            for _ in range(4):
                d.press.left()
                time.sleep(1)

        d.press.down()
        time.sleep(1)
        for _ in range(10):
            d.press.enter()
            time.sleep(3)
            d.press.back()
            time.sleep(1)
            d.press.right()
            time.sleep(1)
        for _ in range(10):
            d.press.enter()
            time.sleep(3)
            d.press.back()
            time.sleep(1)
            d.press.left()
            time.sleep(1)

    # 智能助手

    def testLaunchAssistant(self):
        # 视频头条
        d.press.home()
        time.sleep(1)
        for _ in range(5):
            d.press.right()
        d.press.home()
        time.sleep(2)
        for _ in range(2):
            d.press.down()
            time.sleep(2)
        d.press.right()
        time.sleep(2)
        d.press.enter()
        time.sleep(3)   # wait for tv response
        assert d(packageName='com.duokan.videodaily').wait.exists(
            timeout=20000), 'duokanvideo not start!'
        d.press.enter()
        # 打开智能助手
        for _ in range(2):
            Assistanticon = ['信号源', '网络', '截屏', '投屏', '设置']
            for i in range(5):
                d.server.adb.cmd(
                    'adb shell input keyevent --longpress KEYCODE_MENU')
                if d(packageName="com.xiaomi.mitv.smartpanel").wait.exists(
                        timeout=10000):
                    time.sleep(1)
                    d(text=Assistanticon[i]).click()
                    time.sleep(1)
                    for _ in range(4):
                        d.press.down()
                        time.sleep(1)
                    d.press.back()
                else:
                    d.press.home()

        d.press.home()
        time.sleep(1)

        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity')
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'

        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        assert d(text=u'电影').wait.exists(
            timeout=10000), 'can not find U disk'
        d(text=u'电影').click()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(5)
        for _ in range(2):
            for i in range(5):
                os.system(cmd)
                if d(packageName="com.xiaomi.mitv.smartpanel").wait.exists(
                        timeout=10000):
                    time.sleep(1)
                    d(text=Assistanticon[i]).click()
                    time.sleep(1)
                    for _ in range(4):
                        d.press.down()
                        time.sleep(1)
                    d.press.back()
                else:
                    d.press.home()

    # ...........................................................................

    def browse_app(self):
        for _ in range(20):
            d.press.down()
            time.sleep(1)
        for _ in range(10):
            d.press.up()
            time.sleep(1)
        d.press.back()
        time.sleep(2)
        for _ in range(20):
            d.press.right()
            time.sleep(1)
        for _ in range(7):
            d.press.left()
            time.sleep(1)

    def PlayVideo_app(self):
        d.press.back()
        time.sleep(2)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(5)
        d.press.left()
        time.sleep(1)
        d.press.enter()
        time.sleep(PLAY_TIME)
        for _ in range(3):
            d.press.back()
            time.sleep(1)

    # 云视听极光

    def testPlayVideoFromYunshiting(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.ktcp.video/.activity.MainActivity'), 'can not launch Yunshiting'

        time.sleep(4)

        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        d.press.back()
        time.sleep(10)
        self.browse_app()
        self.PlayVideo_app()

    # 银河奇异果

    def testPlayVideoFromAiQiYi(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.gitvdemo.video/com.gala.video.app.epg.HomeActivity'), 'can not launch AiQiYi'
        time.sleep(5)

        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        time.sleep(2)
        self.browse_app()
        self.PlayVideo_app()

    # 酷喵

    def testPlayVideoFromYouku(self):

        assert d.server.adb.raw_cmd(
            'shell am start -n com.cibn.tv/com.youku.tv.home.activity.HomeActivity'), 'can not launch Youku'
        time.sleep(2)
        if d(textContains="是否将优酷XL设置为开机自启动？").wait.exists(timeout=6000):
            d.press.right()
            time.sleep(2)
            d.press.enter()
        if d(textContains="立即更新").wait.exists(timeout=6000):
            d.press.back()
        time.sleep(4)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        time.sleep(2)
        self.browse_app()
        self.PlayVideo_app()
        # 云视听小电视

    def testBilibili(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaodianshi.tv.yst/.ui.main.MainActivity'), 'can not launch bilibili'
        time.sleep(4)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        time.sleep(2)
        self.browse_app()
        self.PlayVideo_app()
        # 电视家3.0

    def testdianshijia(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.dianshijia.newlive/.entry.SplashActivity'), 'can not launch dianshijia'
        time.sleep(4)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        time.sleep(8)
        d.press.enter()
        time.sleep(1)
        d.press.left()
        for _ in range(10):
            time.sleep(1)
            d.press.down()
        for _ in range(15):
            time.sleep(1)
            d.press.up()
        for _ in range(2):
            time.sleep(1)
            d.press.down()
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        for _ in range(15):
            time.sleep(1)
            d.press.down()
        for _ in range(15):
            time.sleep(1)
            d.press.up()
        time.sleep(1)
        d.press.back()
        time.sleep(PLAY_TIME)

    # 芒果TV

    def testMangoTv(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.hunantv.license/com.mgtv.tv.launcher.LauncherActivity'), 'can not launch dianshijia'
        time.sleep(10)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        d.press.up()
        time.sleep(1)
        self.browse_app()
        self.PlayVideo_app()

        # QQ音乐

    def testQQmusic(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.tencent.qqmusictv/.examples.NewMainActivity'), 'can not launch dianshijia'
        time.sleep(5)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        for _ in range(10):
            d.press.down()
            time.sleep(1)
        for _ in range(10):
            d.press.up()
            time.sleep(1)
        for _ in range(6):
            d.press.right()
            time.sleep(1)
        for _ in range(6):
            d.press.left()
            time.sleep(1)
        self.PlayVideo_app()

    # 云视听虎电竞

    def testhuya(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.huya.nftv/com.duowan.kiwitv.main.HomePage'), 'can not launch dianshijia'
        time.sleep(5)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        self.browse_app()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.back()

    # 唱吧

    def testChangBaSd(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.changba.sd/com.changba.tv.module.main.ui.MainActivity'), 'cannot launch changba'
        time.sleep(10)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        for _ in range(10):
            d.press.down()
            time.sleep(1)
        d.press.back()
        time.sleep(1)
        for _ in range(10):
            d.press.down()
            time.sleep(1)
        d.press.back()
        time.sleep(1)
        for _ in range(2):
            d.press.left()
            time.sleep(1)
        for _ in range(10):
            d.press.right()
            time.sleep(1)
        for _ in range(4):
            d.press.down()
            time.sleep(1)
        d.press.enter()
        time.sleep(2)
        d.watchers.run()
        time.sleep(PLAY_TIME)
        for _ in range(3):
            d.press.back()
            time.sleep(1)

    # keep

    def testkeep(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.gotokeep.androidtv/.business.main.activity.TvMainActivity'), 'no in keep'
        time.sleep(10)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        for _ in range(20):
            d.press.down()
            time.sleep(1)
        for _ in range(20):
            d.press.up()
            time.sleep(1)
        for _ in range(5):
            d.press.left()
            time.sleep(1)
            d.press.right()
            time.sleep(1)
        for _ in range(5):
            d.press.down()
            time.sleep(1)
        for _ in range(2):
            d.press.enter()
            time.sleep(2)
        d.watchers.run()
        time.sleep(PLAY_TIME)
        d.press.back()
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        d.press.enter()
        for _ in range(3):
            time.sleep(1)
            d.press.back()

    # 按上键可以回到主页的app

    def browse_APP_UPAndDown(self):
        for _ in range(20):
            d.press.down()
            time.sleep(1)
        for _ in range(25):
            d.press.up()
            time.sleep(1)

    # 每日瑜伽

    def testdailyvjia(self):
        assert d.server.adb.raw_cmd('shell am start -n com.dailyyoga.tv/.SplashActivity'), 'no in dailyyoga'
        time.sleep(10)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)
        self.browse_APP_UPAndDown()
        for _ in range(5):
            for _ in range(2):
                d.press.right()
                time.sleep(1)
            for _ in range(2):
                d.press.left()
                time.sleep(1)

            # 视频播放
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(PLAY_TIME)
        for _ in range(3):
            time.sleep(1)
            d.press.back()

    # CIBN聚体育

    def testPptvTvsports(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.pptv.tvsports/.activity.StartActivity'), 'can not launch pptv tvsports'

        time.sleep(12)
        for _ in range(4):
            d.watchers.run()
            time.sleep(1)
        self.browse_app()
        self.PlayVideo_app()
        for _ in range(3):
            time.sleep(1)
            d.press.back()

    # CCTV新视听

    def testnewtv(self):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.newtv.cboxtv/com.newtv.host.LauncherActivity'), 'can not launch pptv tvsports'

        time.sleep(10)
        for _ in range(2):
            d.watchers.run()
            time.sleep(1)

        self.browse_app()
        d.press.back()
        time.sleep(2)
        d.press.up()
        time.sleep(1)
        for _ in range(2):
            d.press.down()
            time.sleep(1)
        d.press.enter()
        time.sleep(5)
        d.press.left()
        time.sleep(1)
        d.press.enter()
        time.sleep(PLAY_TIME)
        for _ in range(3):
            d.press.back()
            time.sleep(1)
