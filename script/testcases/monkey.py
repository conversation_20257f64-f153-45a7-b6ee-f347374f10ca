#!/usr/bin/python
# -*- coding:utf-8 -*- 

import sys
import shlex
import subprocess
import unittest
from uiautomator import device as d
import time
from script.testcases.adb_command import *

def execCLI(package_name,running_time = 1000):
    """
    执行monkey然后到时间杀掉monkey
    :param package_name: 执行monkey的包
    :param running_time: monkey执行时长
    """
    # try:
    #     basic_cmd = "adb shell monkey -p %s --throttle 500 -s 500 --ignore-crashes --ignore-timeouts --monitor-native-crashes --pct-appswitch 30 --pct-majornav 40 --pct-syskeys 30 99999"
    #     state = Popen(basic_cmd % package_name, shell=True, stdout=PIPE, stderr=STDOUT)
    #     state.wait(timeout=500)
    #     print(state.communicate()[0])
    # except Exception as e:
    #     print(e)
    #     print('monkeytest took about ten minutes')
    # finally:
    #     print("finally")
    #     cmd = 'adb shell pkill monkey'
    #     timeout_command(cmd)
    basic_cmd = "adb shell monkey -p {} --throttle 500 -s 500 --ignore-crashes --ignore-timeouts --monitor-native-crashes --pct-appswitch 30 --pct-majornav 40 --pct-syskeys 30 99999".format(package_name)
    Popen(basic_cmd, shell=True, stdout=PIPE, stderr=STDOUT)     # 不等待执行完毕就往下走
    start_time = time.time()
    print("start monkey time:",time.ctime())
    while time.time() - start_time < running_time:   # 倒计时
        time.sleep(30)
    cmd = 'adb shell am force-stop {}'.format(package_name)    # stop monkey
    timeout_command(cmd)
    cmd = 'adb shell pkill monkey'
    timeout_command(cmd)
    cmd = 'adb shell busybox pkill com.android.commands.monkey'
    timeout_command(cmd)



class MonkeyTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.wakeup()
        back2home_page()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d.wait.update(timeout=10000)
        # d.press.home()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        for _ in range(4):
            d.press.back()

        d.wakeup()
        for _ in range(2):
            d.press.back()
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        for _ in range(2): d.press.back()
        d.press.home()
        time.sleep(5)

    def testCalendar(self):
        execCLI("com.xiaomi.mitv.calendar")

    def testAppstore(self):
        execCLI("com.xiaomi.mitv.appstore")

    def testGamecenter(self):
        execCLI("com.xiaomi.mibox.gamecenter")

    def testMediaexplorer(self):
        execCLI("com.xiaomi.mitv.mediaexplorer")

    def testTweather(self):
        execCLI("com.xiaomi.tweather")

    def testUmifrontend(self):
        execCLI("com.mi.umifrontend")

    def testShop(self):
        execCLI("com.xiaomi.mitv.shop")

    def testGallery(self):
        execCLI("com.xiaomi.tv.gallery")

    def testPatch(self):
        execCLI("com.mitv.tvhome")

    def testVideoDaily(self):
        execCLI("com.duokan.videodaily")

    def testSetting(self):
        execCLI("com.xiaomi.mitv.settings")

    def testAlarm(self):
        execCLI("com.mitv.alarmcenter")

    def testHandBook(self):
        execCLI("com.xiaomi.mitv.handbook")

    def testNotification(self):
        execCLI("com.xiaomi.mitv.systemui")

    def testGalleryphoto(self):
        execCLI("com.mitv.gallery")

    def testKtcp(self):
        execCLI("com.ktcp.video")

    def testBili(self):
        execCLI("com.xiaodianshi.tv.yst")

    def testAqy(self):
        execCLI("com.gitvdemo.video")

    def testYk(self):
        execCLI("com.cibn.tv")

    def testJJ(self):
        execCLI("cn.jj.tv")

    def testchangba(self):
        execCLI("com.changba.sd")

    def testwps(self):
        execCLI("cn.wps.moffice_i18n_TV")

    def testMg(self):
        execCLI("com.hunantv.license")

    def testVoice(self):
        execCLI("com.xiaomi.voicecontrol")

    def testSmart(self):
        execCLI("com.xiaomi.mitv.smartshare")

    def testChildren(self):
        execCLI("com.lutongnet.nldmx")

    def testYuetv(self):
        execCLI(" com.sohuott.tv.vod")

    def testCctvNew(self):
        execCLI("com.newtv.cboxtv")

    def testKugou(self):
        execCLI("com.dangbei.dbmusic")

    def testNetease(self):
        execCLI("com.netease.cloudmusic.tv")

    def testMore(self):
        execCLI("com.moretv.android")