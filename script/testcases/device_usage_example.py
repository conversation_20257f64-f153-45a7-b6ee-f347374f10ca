#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
设备参数使用示例
演示如何在多设备环境中使用修改后的 adb_command 函数
"""

from adb_command import *
import time

def example_single_device():
    """单设备使用示例"""
    print("=== 单设备使用示例 ===")
    
    # 方法1: 每次调用都传入设备参数
    device_id = "*************:5555"  # 替换为你的设备IP
    
    print(f"使用设备: {device_id}")
    print(f"设备厂商: {get_tv_manufacturer(device_id)}")
    
    # 遥控器操作
    adb_home(device_id)
    time.sleep(2)
    adb_back(device_id)
    time.sleep(2)
    
    # 检查是否回到主页
    is_home = back2home_page(device_id)
    print(f"是否在主页: {is_home}")
    
    # 截图
    tvqs_screeencap(screenshot_2=5, interation=1, delay_start=0, 
                   interval_time=30, rename_keyword="test", 
                   screen_count=2, device=device_id)

def example_with_default_device():
    """使用默认设备的示例"""
    print("=== 使用默认设备示例 ===")
    
    # 设置默认设备
    device_id = "*************:5555"  # 替换为你的设备IP
    set_default_device(device_id)
    
    print(f"设备厂商: {get_tv_manufacturer()}")  # 自动使用默认设备
    
    # 使用便捷函数（自动使用默认设备）
    adb_home_default()
    time.sleep(2)
    adb_back_default()
    time.sleep(2)
    
    # 或者仍然可以传入None来使用默认设备
    adb_up(None)  # 等同于 adb_up_default()
    time.sleep(1)
    adb_down(None)
    time.sleep(1)
    
    # 截图（使用默认设备）
    tvqs_screeencap(screenshot_2=5, interation=1, delay_start=0, 
                   interval_time=30, rename_keyword="default_test", 
                   screen_count=2)  # device参数为None时自动使用默认设备

def example_multi_device():
    """多设备使用示例"""
    print("=== 多设备使用示例 ===")
    
    # 定义多个设备
    devices = [
        "*************:5555",  # 设备1
        "*************:5555",  # 设备2
        "*************:5555",  # 设备3
    ]
    
    # 对每个设备执行操作
    for i, device in enumerate(devices):
        print(f"\n--- 操作设备 {i+1}: {device} ---")
        
        # 获取设备信息
        manufacturer = get_tv_manufacturer(device)
        print(f"设备厂商: {manufacturer}")
        
        # 回到主页
        adb_home(device)
        time.sleep(2)
        
        # 检查是否在主页
        is_home = back2home_page(device)
        print(f"设备 {i+1} 是否在主页: {is_home}")
        
        # 为每个设备截图
        tvqs_screeencap(screenshot_2=3, interation=1, delay_start=0, 
                       interval_time=10, rename_keyword=f"device_{i+1}", 
                       screen_count=1, device=device)

def example_device_specific_operations():
    """根据设备类型执行特定操作的示例"""
    print("=== 设备特定操作示例 ===")
    
    device_id = "*************:5555"  # 替换为你的设备IP
    
    # 根据设备类型执行不同操作
    if is_xiaomi_tv(device_id):
        print("检测到小米电视，执行小米特定操作...")
        # 小米特定操作
        adb_home(device_id)
        time.sleep(2)
        
    elif is_tcl_tv(device_id):
        print("检测到TCL电视，执行TCL特定操作...")
        # TCL特定操作
        adb_home(device_id)
        time.sleep(2)
        
    elif is_hisense_tv(device_id):
        print("检测到海信电视，执行海信特定操作...")
        # 海信特定操作
        adb_home(device_id)
        time.sleep(2)
        
    elif is_huawei_tv(device_id):
        print("检测到华为电视，执行华为特定操作...")
        # 华为特定操作
        adb_home(device_id)
        time.sleep(2)
        
    else:
        print("未知设备类型，执行通用操作...")
        adb_home(device_id)
        time.sleep(2)

def example_app_operations():
    """应用操作示例"""
    print("=== 应用操作示例 ===")
    
    device_id = "*************:5555"  # 替换为你的设备IP
    
    # 启动应用
    app_package = "com.xiaomi.mitv.tvmanager/.MainTvManagerActivity"
    start_app(app_package, wait_time=5, device=device_id)
    
    # 检查当前应用
    current_package = get_front_package(device_id)
    print(f"当前应用: {current_package}")
    
    # 返回主页
    back_result = back2home_page(device_id)
    print(f"返回主页结果: {back_result}")

if __name__ == '__main__':
    print("设备参数使用示例")
    print("请根据你的实际设备IP修改示例中的设备ID")
    print()
    
    # 运行示例（请根据需要取消注释）
    # example_single_device()
    # example_with_default_device()
    # example_multi_device()
    # example_device_specific_operations()
    # example_app_operations()
    
    print("示例代码准备完成，请修改设备IP后运行相应示例")
