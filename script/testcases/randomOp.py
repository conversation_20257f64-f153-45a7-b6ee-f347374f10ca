# -*- coding:utf-8 -*-
import datetime
import os
from random import choice
import unittest
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time
from script.testcases.adb_command import *
keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']


# THRESHOLD = int(config['play_options']['threshold'])

class RandomTest(unittest.TestCase):

    def setUp(self):
        """
        called before  each test method start.
        """

        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="稍后更新").click(text="稍后更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("EXIT_UPGRADE").when(packageName="com.xiaomi.mitv.upgrade").press("back")
        # d.wakeup()
        # time.sleep(10)
        d.wakeup()
        back2home_page()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d.wait.update(timeout=10000)
        # d.press.home()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        # d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        for _ in range(4):
            d.press.back()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        for _ in range(2): d.press.back()

    def testRandom(self):
        for _ in range(300):
            key = choice(keys)
            getattr(d.press, key)()

    def testRefresh(self):
        for _ in range(2): d.press.back()
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        for _ in range(300):
            d.press.down()
        for _ in range(300):
            d.press.up()
