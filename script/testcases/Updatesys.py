#!/usr/bin/python
# -*- coding:utf-8 -*-
import time
import unittest
from uiautomator import device as d
import time


def testUpdatesys():
    for i in range(10):
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.mitv.upgrade/.UpgradeActivity'), 'fail to launch upgrade'
        if d(text='立即更新').wait.exists(timeout=6000):
            time.sleep(1)
            d.press.enter()
            print('1')
            if d(text='立即更新').wait.gone(timeout=12000):

                if d(text='立即体验').wait.exists(timeout=60000):
                    d.press.enter()
                    # time.sleep(60)
                    return
            else:
                continue
        elif d(text='立即体验').wait.exists(timeout=6000):
            print('2')
            time.sleep(1)
            d.press.enter()
            # time.sleep(60)
            return
        else:
            time.sleep(30)
            for _ in range(2):
                time.sleep(1)
                d.press.home()

        assert i != 9, 'fail to upgrade in 10'
