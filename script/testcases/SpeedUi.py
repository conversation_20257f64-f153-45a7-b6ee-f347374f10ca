# -*- coding:utf-8 -*-
import unittest
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time
from script.testcases.adb_command import *

class SpeedUiTest(unittest.TestCase):
    def setUp(self):
        try:
            """
            called before each test method start.
            """
            # a watcher to aviod dialog block test
            d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
            d.watcher("EXIT_VST_THIRD_APP").when(
                textContains="退出").click(text="退出")
            d.watcher("INSTALL_NEW_VERSION").when(
                textContains="您要安装此应用的新版本吗？").click(text="取消")
            d.watcher("EXIT_SOHU_APP").when(
                textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
            d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
            d.watcher("PASS_VST_UPDATE1").when(
                textContains="稍后更新").click(text="稍后更新")
            d.watcher("PASS_TOGIC_UPDATE").when(
                packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
            d.watcher("PASS_VST_UPDATE2").when(
                textContains='根据国家现行政策规定').press('enter')
            d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
            d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
            d.watcher("SCAN_CHANNEL").when(
                textContains="现在进行频道搜索吗？").click(text="确定")
            d.wakeup()
            back2home_page()
            # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
            # d.wait.update(timeout=10000)
            # d.press.home()
            # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
            # d(text='精选').wait.exists()
            '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
            used = re.search(r'^[0-9]+', data[11])
    
            if used:
                data_used = int(used.group())
                if data_used > THRESHOLD:
                    d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
                    time.sleep(30)
                else:
                    pass
            else:
                assert False, ' data space can not match'''
            # d.server.adb.cmd(
            #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
            # time.sleep(30)
            # d.press.enter()
            for _ in range(4):
                d.press.back()
            time.sleep(1)
            d.press.home()
            time.sleep(2)
        except IOError as exp:
            print(str(exp))
            self.restart_uiautomator()

    def restart_uiautomator(self):
        for i in range(3):
            d.server.stop()
            time.sleep(5)
            d.server.start()
            time.sleep(5)


    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("SCAN_CHANNEL")
        # for _ in range(3):
        #     d.press.back()


    def wake_up_setting(self):
        time.sleep(5)
        print("INSTRUMENTATION_STATUS: title=调出智能助手")
        adb_long_pressmenu()
        adb_back()
        app_name = 'com.xiaomi.mitv.settings/.entry.MainActivity'
        start_app(app_name)
        adb_right()
        [adb_quickdown() for i in range(3)]
        adb_home()
        [adb_up() for i in range(2)]
        app_name = 'com.xiaomi.mitv.settings/.entry.MainActivity'
        start_app(app_name)
        adb_right()
        [adb_quickdown() for i in range(3)]

    def adjustlocaldimming(self,lefttimes):
        # determine tv model
        cmd = 'adb shell getprop ro.product.name'
        tv_model = timeout_command(cmd)
        print("INSTRUMENTATION_STATUS: title=调节图像参数")
        if tv_model == 'freeguy' or 'blueplanet' or 'finch':
            # adjust localdimming
            time.sleep(5)
            imageParameters()
            adb_down()
            [longpress_right() for i in range(15)]
            [longpress_left() for i in range(lefttimes)]
            adb_back()
            back2home_page()
        else:
            app_name = "com.xiaomi.mitv.settings/.entry.MainActivity"
            start_app(app_name)
            package = get_front_package()
            # if "settings_window" not in package:
            #     return False
            time.sleep(3)
            adb_down()
            adb_right()
            adb_down()
            adb_center()
            [longpress_right() for i in range(15)]
            [longpress_left() for i in range(lefttimes)]
            back2home_page()

    def adb_quickright(self,times):
        """模拟遥控器按 向右 键"""
        for i in range(times):
            cmd = "adb shell input keyevent 22"
            timeout_command(cmd)


    def start_yunshiting(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动云视听极光应用")
        app_name = "com.ktcp.video/.activity.MainActivity"
        start_app(app_name)
        package = get_front_package()
        if "com.ktcp.video/.activity.PrivacyAgreementActivity" in package:
            adb_center()
            time.sleep(2)
        time.sleep(10)
        # self.wake_up_setting()

    def start_aiqiyi(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动银河奇异果应用")
        app_name = "com.gitvdemo.video/com.gala.video.app.epg.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意并继续").exists:  # 同意用户协议
            adb_center()
            time.sleep(2)
        # 存储权限
        package = get_front_package()
        if "com.android.permissioncontroller/.permission.ui.GrantPermissionsActivity" not in package:
            adb_center()
            time.sleep(2)
        print("退出广告页推送")
        adb_back()
        time.sleep(5)
        # self.wake_up_setting()

    def start_kumiao(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动酷喵应用")
        app_name = "com.cibn.tv/com.youku.tv.home.activity.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # if d(textContains="同意").exists:
        #     d(textContains="同意").click()
        #     # adb_center()
        #     time.sleep(2)
        # if d(textContains="是否将优酷XL设置为开机自启动？").wait.exists(timeout=6000):
        #     d.press.right()
        #     # time.sleep(2)
        #     d.wait.update(timeout=2000)
        #     d.press.enter()
        # if d(textContains="立即更新").wait.exists(timeout=6000):
        #     d.press.back()
        adb_center()
        time.sleep(3)
        adb_center()
        time.sleep(5)


    def start_xiaodianshi(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动云视听小电视应用")
        back2home_page()
        app_name = "com.xiaodianshi.tv.yst/.ui.main.MainActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # 处理弹窗
        if "com.xiaodianshi.tv.yst/com.xiaodianshi.tv.yst.ui.messagedialog.MessageDialogActivity" in package:
            adb_back()
        # 同意用户协议
        if "com.xiaodianshi.tv.yst/.ui.introduction.IntroductionActivity" in package:
            adb_center()
        d.press.down()
        d.wait.update(timeout=2000)
        # self.wake_up_setting()

    def start_mango(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动芒果TV应用")
        app_name = "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意").exists:
            adb_center()
            time.sleep(2)
        adb_center()  # 跳过开机广告1
        played = 0
        adb_back()  # 跳过广告2
        # self.wake_up_setting()

    def start_wasu(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动华数鲜时光应用")
        app_name = "com.ixigua.android.tv.wasu/com.ixigua.android.business.tvbase.base.app.schema.AdsAppActivity"
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").exists:
            adb_center()
            time.sleep(2)
        # time.sleep(20)
        # self.wake_up_setting()


    def start_kuai(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动云视听快TV应用")
        app_name = "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意并继续").exists:
            adb_center()
        # self.wake_up_setting()


    def start_qqmusic(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动qq音乐应用")
        app_name = "com.tencent.qqmusictv/.examples.NewMainActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        for i in range(4):
            adb_center()
        # self.wake_up_setting()

    def start_cctv(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动CCTV央视频应用")
        app_name = "com.newtv.cboxtv/com.newtv.host.LauncherActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意并继续").exists:
            adb_center()
        if d(textContains="允许").exists:
            adb_center()
        # self.wake_up_setting()

    def start_netease(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动网易云音乐应用")
        app_name = "com.netease.cloudmusic.tv/com.netease.cloudmusic.app.LoadingActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意协议并进入").exists:
            adb_center()
        if d(textContains="允许").exists:
            adb_center()
        # self.wake_up_setting()


    def start_dianshimao(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动云视听电视猫应用")
        app_name = "com.moretv.android/.StartActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        if d(textContains="同意").exists:
            adb_center()
        # self.wake_up_setting()

    def start_child(self):
        adb_home()
        print("INSTRUMENTATION_STATUS: title=启动脑力大冒险")
        app_name = 'com.lutongnet.nldmx/.MainActivity'
        start_app(app_name)
        [adb_center() for i in range(2)]
        package = get_front_package()
        print(package)
        # self.wake_up_setting()


    def start_huya(self):
        adb_home()
        # 云视听虎电竞
        print("INSTRUMENTATION_STATUS: title=启动云视听虎电竞")
        back2home_page()
        app_name = "com.huya.nftv/.startup.StartupActivity"
        start_app(app_name)
        package = get_front_package()
        print(package)
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意并继续").wait.exists(timeout=6000):
            d(textContains="同意并继续").click()
        if d(textContains="我知道了").wait.exists(timeout=6000):
            d(textContains="我知道了").click()
        # self.wake_up_setting()

    def start_miguvideo(self):
        adb_home()
        app_name = 'cn.miguvideo.migutv/.SplashActivity'
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").wait.exists(timeout=6000):
            d(textContains="同意并继续").click()
        # self.wake_up_setting()

    def start_onlinevideo(self):
        """
        随机播放桌面主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        adb_home()
        time.sleep(5)
        adb_center()
        time.sleep(5)
        adb_down()
        time.sleep(2)
        adb_down()
        time.sleep(2)
        adb_center()
        # self.wake_up_setting()

    def start_localvideo(self):
        adb_home()
        app_name = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'
        start_app(app_name)
        time.sleep(5)
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click.wait(timeout=3)
        d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click.wait(timeout=5)
        for i in range(5):
            try:
                assert d(text=u'1A测试片源').wait.exists(
                    timeout=10000), 'can not find folder'
                d(text=u'1A测试片源').click()
                print('find folder')
                break
            except Exception as exp:
                print(str(exp))
                [adb_quickdown() for i in range(9)]
        time.sleep(3)
        adb_center()
        # self.wake_up_setting()

    def test_trigger_scene_setting(self):
        for i in range(1):
            print('this is {} round'.format(i))
            self.start_onlinevideo()
            self.wake_up_setting()
            self.start_localvideo()
            self.wake_up_setting()
            self.start_yunshiting()
            self.wake_up_setting()
            self.start_aiqiyi()
            self.wake_up_setting()
            self.start_kumiao()
            self.wake_up_setting()
            self.start_xiaodianshi()
            self.wake_up_setting()
            self.start_mango()
            self.wake_up_setting()
            self.start_wasu()
            self.wake_up_setting()
            self.start_kuai()
            self.wake_up_setting()
            self.start_qqmusic()
            self.wake_up_setting()
            self.start_cctv()
            self.wake_up_setting()
            self.start_netease()
            self.wake_up_setting()
            self.start_child()
            self.wake_up_setting()
            self.start_dianshimao()
            self.wake_up_setting()
            self.start_huya()
            self.wake_up_setting()
            self.start_miguvideo()
            self.wake_up_setting()


    def test_trigger_scene_localdimming(self):
        # self.start_localvideo()
        self.start_onlinevideo()
        self.adjustlocaldimming(10)
        self.start_localvideo()
        self.adjustlocaldimming(10)
        self.start_yunshiting()
        self.adjustlocaldimming(10)
        self.start_aiqiyi()
        self.adjustlocaldimming(10)
        self.start_kumiao()
        self.adjustlocaldimming(10)
        self.start_xiaodianshi()
        self.adjustlocaldimming(10)
        self.start_mango()
        self.adjustlocaldimming(10)
        self.start_wasu()
        self.adjustlocaldimming(10)
        self.start_kuai()
        self.adjustlocaldimming(10)
        self.start_qqmusic()
        self.adjustlocaldimming(10)
        self.start_qqmusic()
        self.adjustlocaldimming(10)
        self.start_cctv()
        self.adjustlocaldimming(10)
        self.start_netease()
        self.adjustlocaldimming(10)
        self.start_child()
        self.adjustlocaldimming(10)
        self.start_dianshimao()
        self.adjustlocaldimming(10)
        self.start_huya()
        self.adjustlocaldimming(10)
        self.start_miguvideo()
        self.adjustlocaldimming(10)


    def test_trigger_every_patch(self):
        adb_home()
        adb_back()
        for times in range(8):
            [adb_quickup() for i in range(4)]
            self.adb_quickright(times)
            adb_center()
            adb_home()
            adb_back()



