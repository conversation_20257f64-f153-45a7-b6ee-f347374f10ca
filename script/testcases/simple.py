#!/usr/bin/python
# -*- coding:utf-8 -*-
import unittest
import random
from uiautomator import device as d
# from testconfig import config
from subprocess import Popen, PIPE, STDOUT
import sys
import os
import psutil
import subprocess
import re
from random import choice
import time
print(sys.argv)

if len(sys.argv) < 2:
    print("No valuable arg found.")
    sys.exit(-1)
device_id = sys.argv[1].split(":")[0] #如果带端口号，会把端口号去掉
sys.argv[1] = device_id
device_id = os.getenv('ANDROID_SERIAL')
print(device_id)

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
PLAY_TIME_LONG = config_.getint('play_options','play_time_long')
THRESHOLD = config_.getint('play_options','threshold')


# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# THRESHOLD = int(config['play_options']['threshold'])

keys = ['up', 'down', 'left', 'right']


class SimpleModelTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
        d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
        d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
        d.watcher("EXIT_VST_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
            textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(
            textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(
            packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("PASS_STOP_PLAY").when(
            textContains='确认退出').click(text="确认退出")

        d.watcher("PASS_APP_Popup").when(text="按【返回】可以关闭弹窗哦").press('back')
        d.watcher("PASS_APPKEEP_Popup1").when(text="按返回键退出播放").press('back')
        d.watcher("PASS_APPKEEP_Popup2").when(
            textContains="访问您设备上的照片、媒体内容和文件吗？").press('enter')
        d.watcher("PASS_APPAIQIYI_Popup").when(
            textContains="按【返回键】").press('back')
        d.watcher("PASS_APPBilibili_Popup").when(
            text="用户协议及隐私政策 ").press('enter')
        d.watcher("PASS_APPQQmusic_Popup").when(
            textContains="我知道了").press('enter')
        d.watcher("PASS_APPPPsport_Popup").when(
            textContains="同意").press('enter')
        d.watcher("PASS_APPPPsport_Popup2").when(
            textContains="我知道了").press('enter')
        d.watcher("PASS_APPPPsport_Popup2").when(
            textContains="允许CIBN聚体育拨打电话和管理通话吗？").press('enter')
        d.watcher("PASS_APPCloudvideo_popup").when(
            textContains="再看看").press('enter')


        d.watcher(("Install app-uiautomator-test.apk")).when(textContains="app-uiautomator-test.apk").press.up.center.down.center()  # not work

        d.wakeup()
        time.sleep(15)
        d.press.home()
        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        # time.sleep(1)
        #d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")

        d.press.home()

    def __Move_L_R(self, step, direction, sleepTime):
        for i in range(step):
            d.press(direction)
            d.wait.update(timeout=sleepTime * 1000)

    def testPlayLocalVideo(self):
        for _ in range(10):
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(2)
            d.press.enter()
            if d(textContains="不支持该格式视频").exists:
                d.press.back()
                time.sleep(2)
                d.press.down()
            else:
                assert d(className="android.widget.ListView").child(
                    resourceId="com.xiaomi.mitv.mediaexplorer:id/iv_image").wait.gone(
                    timeout=20000), 'start to play video failed!'
                time.sleep(10)
                d.press.back()
                time.sleep(2)

        for _ in range(6):
            d.press.back()
            time.sleep(1)
        d.press.home()

    def testSimsearch(self):
        # 极简模式下浏览导航栏
        d.press.home()
        d.wait.update(timeout=5000, package_name='com.mitv.tvhome')
        assert d(resourceId='android:id/content').wait.exists(), 'not at home'
        self.__Move_L_R(3, 'up', 2)
        for i in range(5):
            self.__Move_L_R(5, 'right', 2)
            self.__Move_L_R(5, 'left', 2)

    def testSimpleHome(self):
        # 极简模式下浏览桌面
        d.press.home()
        d.wait.update(timeout=5000, package_name='com.mitv.tvhome')
        assert d(resourceId='android:id/content').wait.exists(), 'not at home'
        # d(text='精选').click()
        self.__Move_L_R(30, 'down', 2)
        self.__Move_L_R(30, 'up', 2)
        d.press('home')

    def testSwithChannel(self):
        # ATV内切换频道
        """
        lanuch the ATVSource and testSwithChannel
        """
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.tvplayer/.AtvActivity')
        assert d(resourceId='com.xiaomi.mitv.tvplayer:id/video_surface_parent').wait.exists(
            timeout=20000), "launch TV failed!"
        for i in range(10):
            d.press.down()
            time.sleep(10)
            #d.wait.update(timeout=10000) 不生效
        for i in range(10):
            d.press.up()
            time.sleep(10)
            #d.wait.update(timeout=10000) 不生效

    def testplayvideo(self):
        # 高清播放器内播放本地音视频
        assert d.server.adb.raw_cmd(
            'shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity'), 'can not video player'
        time.sleep(3)
        self.testPlayLocalVideo()

    def testLaunchAndExitMediaExplorer(self):
        #反复进退高清播放器

        d.press.home()
        d.wait.update(timeout=20000, package_name='com.mitv.tvhome')
        #d(text='精选').wait.exists()
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity')
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/video', text="视频").wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        time.sleep(5)

    def testLaunchAndExitSetting(self):
        """
        launch  app store and exit
       """
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.settings/.entry.MainActivity')
        d.wait.update(timeout=3000, package_name='com.xiaomi.mitv.settings')

    def testLaunchSmartshare(self):
        # 极简模式下无线投屏
        d.press.home()
        time.sleep(2)
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.smartshare/.MainActivity'),
        d.wait.update(timeout=10000, package_name='com.xiaomi.mitv.smartshare')
        assert d(packageName='com.xiaomi.mitv,smartshare').exist, 'not in smartshare'
        time.sleep(2)
        for i in range(5):
            for i in range(2):
                self.__Move_L_R(2, 'right', 2)
            for i in range(2):
                self.__Move_L_R(2, 'left', 2)


    def testPlayPatchVideo(self):
        for i in range(3):
            d.press.home()
            d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
            self.__Move_L_R(3, 'down', 2)
            #d(text='精选').wait.exists()
            #d.press.back()
            d.wait.update(timeout=1000)
            d.press.right()
            #assert d(text='精选').exists, 'not at home'
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=5000, package_name="com.mitv.mivideoplayer")  # 进入这个包
            # assert d(packageName='com.mitv.mivideoplayer',textContains='收藏').wait.exists(timeout=20000), 'not at mivideoplayer'
            assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'not at mivideoplayer'
            # 进入了packageName的页面之后，还没有加载结束，但是画面已经是符合packageName了，往左一步是到不了左边这个控件的
            d(textContains='收藏').wait.exists()  # check 了几个电视剧（有选集）、电影（没有选集），需要会员，不需要会员的，只有“收藏”控件是统一的,状态可能是“收藏”或者是“已收藏”
            assert d(textContains='收藏').exists, 'loading mivideoplayer timeout'
            d.press.left()  # 左一步是为了让他播放第一集，否则进来默认是“开通会员”控件,如果这个视频不需要会员，则没有开通会员控件
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000)
            assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'video not start!'
            time.sleep(PLAY_TIME_LONG)
            # time.sleep(30)

    def testDemo(self):
        """
        normal switch to simple
        """
        #self.__Move_L_R(1, 'home', 2)
        print(d.info)
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.tvmanager/.MainTvManagerActivity ')
        d.wait.update(timeout=5000, package_name='com.xiaomi.mitv.tvmanager')
        assert d(packageName='com.xiaomi.mitv.tvmanager').wait.exists(timeout=5000), 'tvmanger launch fail'
        d.press.down()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.right()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.enter()
        #time.sleep(30)
        #d.press.home()
        time.sleep(3)
        self.Calculator()


    def testExam(self):
        """
        simple switch to normal
        """
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mitv.tvmanager/.MainTvManagerActivity ')
        d.wait.update(timeout=5000, package_name='com.xiaomi.mitv.tvmanager')
        assert d(packageName='com.xiaomi.mitv.tvmanager').wait.exists(timeout=5000), 'tvmanger launch fail'
        d.press.down()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.left()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        d.press.enter()
        time.sleep(20)
        self.Calculator()
        #print ('success switch to normal')

    def connect(self):
        state = Popen("adb -s %s shell ls -d" % device_id, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        #print time.time()
        #print (out)
        if out != ".":  # 如果状态异常
            Popen("adb disconnect %s" % device_id, shell=True, stdout=PIPE, stderr=PIPE).wait()
            time.sleep(3)
            __connector = Popen("adb connect %s" % device_id, shell=True, stdout=PIPE, stderr=PIPE)  # 则重新连接
            __connector.wait()  # 网上查资料说可以避免僵尸进程
            print (out)
            out, err = __connector.stdout.read(), __connector.stderr.read()
            if not out and err:
                print("err:",err)



    def Calculator(self, timeout=300):
        start_time = time.time()
        while time.time()-start_time < timeout:
            self.connect()

        #print('connection timeout !')

        #print ('switch model run {:.1f}s'.format(time_elapsed // 60, time_elapsed % 60))
        #while time.ti() - start_time < timeout:
            #self.connect()
            #attach = Popen("adb -s %s shell ls -d" % device
        # _id, shell=True, stdout=PIPE, stderr=STDOUT)

    def testSwitchmodel(self):
        for i in range(2):
            print("当前是从标准模式开始切换的")
            self.testDemo()
            print('success switch to simple')
            time.sleep(5)
            self.testExam()
            d.wait.update(timeout=15000, package_name='com.mitv.tvhome')
            assert d(packageName='com.mitv.tvhome').wait.exists(timeout=3000), 'launch home fail'
            d(text='精选').wait.exists()
            assert d(text='精选').exists, 'not at home'
            print('success switch to normal')

    def testSuperpw(self):
        # pw切换至superpw
        self.__Move_L_R(1, 'home', 1)
        self.__Move_L_R(1, 'back', 2)
        assert d(packageName='com.mitv.tvhome').wait.exists(timeout=3000), 'launch home fail'
        d(text='精选').wait.exists()
        assert d(text='精选').exists, 'not at home'
        print("start switch to superpw")
        self.__Move_L_R(3, 'left', 1)
        self.__Move_L_R(7, 'down', 1)
        self.__Move_L_R(4, 'right', 1)
        d.press.enter()
        time.sleep(2)
        d.press.enter()
        time.sleep(2)

    def testSuperBrowser(self):
        # superpw下浏览桌面
        for i in range(3):
            self.__Move_L_R(4, 'right', 1)
            self.__Move_L_R(4, 'left', 1)
            self.__Move_L_R(1, 'down', 1)
            self.__Move_L_R(6, 'right', 1)
            self.__Move_L_R(11, 'left', 1)
        d.press.home()

    def testSupervideo(self):
        # superpw下播放视频
        for i in range(3):
            d.press.back()
            time.sleep(2)
            d.press.enter()
            assert d(packageName='com.mitv.tvhome').wait.exists(timeout=3000), 'launch home fail'
            d(text='精选').wait.exists()
            assert d(text='精选').exists, 'not at home'
            # d.press.home()
            d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
            self.__Move_L_R(3, 'down', 2)
            # d(text='精选').wait.exists()
            # d.press.back()
            d.wait.update(timeout=1000)
            d.press.right()
            # assert d(text='精选').exists, 'not at home'
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=5000, package_name="com.mitv.mivideoplayer")  # 进入这个包
            # assert d(packageName='com.mitv.mivideoplayer',textContains='收藏').wait.exists(timeout=20000), 'not at mivideoplayer'
            assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'not at mivideoplayer'
            # 进入了packageName的页面之后，还没有加载结束，但是画面已经是符合packageName了，往左一步是到不了左边这个控件的
            d(textContains='添加想看').wait.exists()
            # check 了几个电视剧（有选集）、电影（没有选集），需要会员，不需要会员的，只有“添加想看”控件是统一的,状态可能是“收藏”或者是“已收藏”
            assert d(textContains='添加想看').exists, 'loading mivideoplayer timeout'
            d.press.left()  # 左一步是为了让他播放第一集，否则进来默认是“开通会员”控件,如果这个视频不需要会员，则没有开通会员控件
            d.wait.update(timeout=2000)
            d.press.enter()
            d.wait.update(timeout=2000)
            assert d(packageName='com.mitv.mivideoplayer').wait.exists(timeout=20000), 'video not start!'
            time.sleep(60)
            # time.sleep(30)
            d.press.home()

    def testSuperUI(self):
        # 浏览superpw状态栏
        d.press.home()
        time.sleep(2)
        self.__Move_L_R(2, 'up', 1)
        for i in range(2):
            [d.press.right() for i in range(4)]
            [d.press.left() for i in range(4)]

    def testSuperHistory(self):
        # 浏览superpw 历史记录
        self.__Move_L_R(2, 'up', 1)
        self.__Move_L_R(1, 'right', 1)
        d.press.enter()
        time.sleep(2)
        for i in range(2):
            [d.press.right() for i in range(3)]
            [d.press.down() for i in range(3)]

    def testSuperUsercenter(self):
        # 浏览superpw 用户中心
        self.__Move_L_R(2, 'up', 1)
        self.__Move_L_R(2, 'right', 1)
        d.press.enter()
        time.sleep(2)
        [d.press.right() for i in range(7)]
        [d.press.left() for i in range(4)]
        time.sleep(2)
        d.press.back()
        for i in range(2):
            self.__Move_L_R(1, 'right', 1)
            self.__Move_L_R(1, 'enter', 2)
            self.__Move_L_R(1, 'back', 1)

    def testSptopw(self):
        # 切换superpw-pw
        d.press.home()
        time.sleep(2)
        self.__Move_L_R(2, 'up', 1)
        self.__Move_L_R(2, 'right', 1)
        d.press.enter()
        time.sleep(2)
        self.__Move_L_R(1, 'down', 1)
        self.__Move_L_R(1, 'enter', 2)
        self.__Move_L_R(1, 'enter', 2)
        time.sleep(5)
        d.press.back()
        assert d(packageName='com.mitv.tvhome').wait.exists(timeout=3000), 'launch home fail'
        d(text='精选').wait.exists()
        assert d(text='精选').exists, 'not at home'








