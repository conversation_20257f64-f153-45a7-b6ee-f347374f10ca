#!/usr/bin/python
# -*- coding:utf-8 -*-

from subprocess import Popen,PIPE,STDOUT
import time

import subprocess
import configparser
import requests
import re
import os

def timeout_command(command, timeout=60, device=None):
    """
    执行adb命令
    Args:
        command: 要执行的命令
        timeout: 超时时间，默认60秒
        device: 设备序列号，如果提供则会在adb命令中添加-s参数
    """
    # 如果提供了设备参数且命令以adb开头，则插入设备参数
    if device and command.strip().startswith('adb '):
        command = command.replace('adb ', f'adb -s {device} ', 1)
    try:
        result = subprocess.run(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            timeout=timeout,
            text=True
        )
        return result.stdout.strip()
    except subprocess.TimeoutExpired:
        print(f"adb命令执行超时 ({timeout}秒): {command}")
        return ""
    except Exception as e:
        print(f"adb命令执行异常: {e}")
        return ""

def get_tv_manufacturer(device=None):
    """获取电视厂商"""
    try:
        manufacturer = timeout_command('adb shell getprop ro.product.manufacturer', device=device).strip().lower()
        print(f"电视厂商: {manufacturer}")
        return manufacturer
    except:
        return 'unknown'

def is_xiaomi_tv(device=None):
    """判断是否为小米设备"""
    return 'xiaomi' in get_tv_manufacturer(device)

def is_tcl_tv(device=None):
    """判断是否为TCL电视"""
    return 'tcl' in get_tv_manufacturer(device)

def is_hisense_tv(device=None):
    """判断是否为海信电视"""
    return 'hisense' in get_tv_manufacturer(device)

def is_huawei_tv(device=None):
    """判断是否为华为电视"""
    return 'huawei' in get_tv_manufacturer(device)
# 全局设备变量，可以通过 set_default_device() 设置
_default_device = None

def set_default_device(device):
    """设置默认设备，之后所有函数如果不传device参数将使用此设备"""
    global _default_device
    _default_device = device
    print(f"设置默认设备: {device}")

def get_default_device():
    """获取当前默认设备"""
    return _default_device

def clear_default_device():
    """清除默认设备设置"""
    global _default_device
    _default_device = None
    print("已清除默认设备设置")

# 修改 timeout_command 函数，支持使用默认设备
def timeout_command_with_default(command, timeout=60, device=None):
    """
    执行adb命令，如果没有指定device则使用默认设备
    """
    if device is None:
        device = _default_device
    return timeout_command(command, timeout, device)

print("adb_command get_tv_manufacturer: " + get_tv_manufacturer())
def read_config():
    config_dir = "config/config.ini"
    cf = configparser.ConfigParser()
    cf.read(config_dir, encoding="utf-8")  # 读取config.ini
    return cf
config = read_config()

PLAY_TIME = config.getint('play_options', 'play_time')     # int
PLAY_TIME_LONG = config.getint('play_options', 'play_time_long')
THRESHOLD = config.getint('play_options', 'threshold')
MOVIES = config.getint('play_options', 'movie_nums')
TARGET_DIR1 = config.get('play_options', 'target_dir1')   # string
TARGET_DIR2 = config.get('play_options', 'target_dir2')
TARGET_DIR3 = config.get('play_options', 'target_dir3')
TARGET_DIR4 = config.get('play_options', 'target_dir4')





def ensure_screencap_dir(device=None):
    """确保截图目录存在"""
    screencap_dir = "/sdcard/screencaps"
    cmd = f'adb shell "test -d {screencap_dir} || mkdir -p {screencap_dir}"'
    timeout_command(cmd, device=device)
    print(f"确保截图目录存在: {screencap_dir}")
    return screencap_dir

def xiaomi_screencap(screenshot_2, screen_count, capture_path, device=None):
    """小米电视截图实现"""
    print("使用小米截图逻辑...")
    xiaomi_path = "storage/emulated/0/Android/data/com.xiaomi.tvqs/files"
    screen_num = 0

    while screen_num < screen_count:
        timeout_command('adb shell am broadcast -a mitv.action.debug.capture --es cmd "capture"', device=device)
        time.sleep(screenshot_2)
        # 检查小米原始目录中的截图数量
        photo_num = timeout_command('adb shell "ls {} | grep "capture" | wc -l"'.format(xiaomi_path), device=device)
        # 添加错误处理
        try:
            screen_num = int(photo_num) if photo_num else 0
        except ValueError:
            print(f"截图数量检测失败，返回值: '{photo_num}'")
            screen_num = 0
            break  # 避免无限循环

    # 将截图从小米目录移动到统一目录
    move_cmd = f'adb shell "mv {xiaomi_path}/capture*.jpg {capture_path}/ 2>/dev/null || true"'
    move_cmd_png = f'adb shell "mv {xiaomi_path}/capture*.png {capture_path}/ 2>/dev/null || true"'
    timeout_command(move_cmd, device=device)
    timeout_command(move_cmd_png, device=device)

def generic_screencap(screenshot_2, screen_count, capture_path, device=None):
    """通用截图实现"""
    print("使用通用截图逻辑...")
    for i in range(screen_count):
        filename = f"capture_{i+1}.png"
        cmd = f'adb shell "screencap -p {capture_path}/{filename}"'
        timeout_command(cmd, device=device)
        if i < screen_count - 1:  # 不是最后一张时等待
            time.sleep(screenshot_2)

def tcl_keyevent_screencap(screenshot_2, screen_count, capture_path, device=None):
    """
    TCL设备专用截图方法
    通过发送keyevent 4149触发截图，然后从日志中提取API URL，下载图片
    """
    import re
    import requests
    import os

    print("使用TCL专用截图逻辑...")

    for i in range(screen_count):
        print(f"TCL截图第{i+1}张/{screen_count}张")

        # 如果不是第一次截图，增加等待时间让服务恢复
        if i > 0:
            print(f"等待TCL截图服务恢复...")
            time.sleep(1)  # 给TCL设备更多恢复时间
        try:
            # 清理日志
            timeout_command('adb shell logcat -c', device=device)
            time.sleep(1)

            # 发送截图按键
            print("发送截图按键事件 4149")
            timeout_command('adb shell input keyevent 4149', device=device)

            # 监控日志获取API URL
            print("等待截图上传完成...")
            api_url = None

            for wait_time in range(15):
                time.sleep(1)
                result = timeout_command('adb shell "logcat -d | grep GlobalScreenshotUI | tail -1"', device=device)
                if result:
                    # 提取API URL
                    match = re.search(r'https://capture-res-o\.api\.leiniao\.com/capture/api/[a-f0-9\-]+\.html', result)
                    if match:
                        api_url = match.group(0)
                        print(f"捕获到API URL: {api_url}")
                        break

                if wait_time % 3 == 0:
                    print(f"等待中... ({wait_time + 1}/15)")

            if not api_url:
                print("未能捕获到API URL，跳过此次截图")
                continue

            # 下载并保存图片
            print(f"下载截图...")

            # 获取HTML内容
            response = requests.get(api_url, timeout=10)
            response.raise_for_status()
            html_content = response.text

            # 从HTML中提取图片URL
            img_match = re.search(r'src=(["\']?)(http://capture-res-o\.api\.leiniao\.com/capture/api/[a-f0-9\-]+\.jpg)\1', html_content)
            if not img_match:
                print("未能从HTML中提取到图片URL")
                continue

            img_url = img_match.group(2)
            print(f"图片URL: {img_url}")

            # 下载图片
            img_response = requests.get(img_url, timeout=30)
            img_response.raise_for_status()

            # 保存图片到设备
            filename = f"capture_{i+1}.jpg"
            temp_path = f"/tmp/tcl_temp_{i+1}.jpg"

            # 保存到本地临时文件
            with open(temp_path, 'wb') as f:
                f.write(img_response.content)

            # 推送到设备
            push_cmd = f'adb push {temp_path} {capture_path}/{filename}'
            timeout_command(push_cmd, device=device)

            # 删除本地临时文件
            os.remove(temp_path)

            print(f"TCL截图完成: {filename}")

        except Exception as e:
            print(f"TCL截图过程中出错: {e}")
            # 尝试创建一个占位文件，避免后续重命名出错
            placeholder_cmd = f'adb shell "echo TCL_SCREENSHOT_ERROR > {capture_path}/capture_{i+1}.jpg"'
            timeout_command(placeholder_cmd, device=device)

        # 下一张截图前的等待
        if i < screen_count - 1:
            time.sleep(screenshot_2)
        adb_back(device)

def tvqs_screeencap(screenshot_2=10,interation=1,delay_start=3,interval_time=60,rename_keyword="screencap",screen_count=2,device=None):
    """
    截图，每组截图后会根据截图时间戳重命名为，一组截图为2张，原始图像命名为capture_x.jpg & capture_y.jpg,重命名后为screencap_temptime-1.jpg & screencap_temptime-2.jpg
    支持多厂商：小米使用broadcast方式，其他厂商使用screencap命令
    Args:
        screenshot_2: 组内截图的时间间隔时长，默认为10秒
        interation: 截图的组数，默认为1组
        delay_start: 调用本截图方法开始截图前是否需要等待，默认为等待3秒，如果不需要等待，可以写0
        interval_time: 每组截图之间的间隔，默认为60秒。只在每组截图之间等待，最后一组截图后直接返回
        rename_keyword: 重命名的名称，默认为sceencap
        screen_count:一组截图数量，默认为2张
        device: 设备序列号，用于多设备环境
    Returns:
    """
    print("=============多厂商 screencap================")
    time.sleep(delay_start)

    # 确保截图目录存在
    capture_path = ensure_screencap_dir(device)

    # 获取厂商信息
    manufacturer = get_tv_manufacturer(device)

    for i in range(interation):
        print("screencap times:{}".format(i+1))

        # 根据厂商选择截图方式
        if 'xiaomi' in manufacturer:
            xiaomi_screencap(screenshot_2, screen_count, capture_path, device)
        elif is_tcl_tv(device):
            # TCL设备使用专用截图方法
            tcl_keyevent_screencap(screenshot_2, screen_count, capture_path, device)
        else:
            # Hisense, Huawei 等其他厂商使用通用screencap
            generic_screencap(screenshot_2, screen_count, capture_path, device)

        # 根据时间戳重命名
        temp_time = time.strftime("%Y%m%d-%H%M%S")
        index = 1

        tvqs_files = str(timeout_command("adb shell ls {}".format(capture_path), device=device)).splitlines()
        for capture in tvqs_files:
            if capture.startswith("capture") and (capture.endswith(".jpg") or capture.endswith(".png")):
                print("capture:{}，rename to {}_{}-{}.jpg".format(capture,rename_keyword,temp_time,index))
                timeout_command("adb shell mv {}/{} {}/{}_{}-{}.jpg".format(capture_path,capture,capture_path,rename_keyword,temp_time, index), device=device)
                index += 1

        if i+1 == interation:    # 最后一次截图就不等待了直接返回
            return
        else:
            time.sleep(interval_time)

def start_app(package_activity, wait_time=10, device=None):
    """启动一个app,等待app加载时间"""
    cmd = "adb shell am start -n {}".format(package_activity)
    print(timeout_command(cmd, device=device))
    time.sleep(wait_time)


def start_service(package_service, wait_time=10, device=None):
    """启动一个service,等待service加载时间"""
    cmd = "adb shell am startservice -n {}".format(package_service)
    print(timeout_command(cmd, device=device))
    time.sleep(wait_time)


def start_broadcast(package_broadcast, wait_time=10, device=None):
    """发送一个广播,等待加载时间"""
    cmd = "adb shell am broadcast -a {}".format(package_broadcast)
    print(timeout_command(cmd, device=device))
    time.sleep(wait_time)


def get_front_package(device=None):
    """读取当前页面的包名"""
    cmd = "adb shell \"dumpsys window | grep -i mfocused\""
    res = timeout_command(cmd, device=device)
    return res

def back2home_page(device=None):
    """
    通过back3次回到精选home页面
    检查是否在home页面
    """
    adb_home(device)
    time.sleep(5)
    for j in range(3):
        adb_back(device)
    time.sleep(5)
    res = get_front_package(device)
    if "com.mitv.tvhome" in res or "com.tcl.cyberui" in res or "com.jamdeo.tv.vod" in res:
        return True
    elif "com.huawei.homevision" in res:
        adb_center(device)
        time.sleep(1)
        res = get_front_package(device)
        if "com.huawei.himovie.tv" in res:
            return True
        return False
    else:
        return False

def adb_home(device=None):
    """模拟遥控器按home键"""
    cmd = "adb shell input keyevent HOME"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_back(device=None):
    """模拟遥控器按back键"""
    cmd = "adb shell input keyevent BACK"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_up(device=None):
    """模拟遥控器按 向上 键"""
    cmd = "adb shell input keyevent 19"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_down(device=None):
    """模拟遥控器按 向下 键"""
    cmd = "adb shell input keyevent 20"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_left(device=None):
    """模拟遥控器按 向左 键"""
    cmd = "adb shell input keyevent 21"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_right(device=None):
    """模拟遥控器按 向右 键"""
    cmd = "adb shell input keyevent 22"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_center(device=None):
    """模拟遥控器按 确定（center） 键"""
    cmd = "adb shell input keyevent 23"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_volume_up(device=None):
    """模拟遥控器按 音量调大 键"""
    cmd = "adb shell input keyevent 24"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_volume_down(device=None):
    """模拟遥控器按 音量调小 键"""
    cmd = "adb shell input keyevent 25"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_power(device=None):
    """模拟遥控器按 电源 键"""
    cmd = "adb shell input keyevent 26"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_menu(device=None):
    """模拟遥控器按 菜单 键"""
    cmd = "adb shell input keyevent 82"
    timeout_command(cmd, device=device)
    time.sleep(1)

def adb_long_pressmenu():
    time.sleep(5)
    if is_xiaomi_tv():
        cmd = 'adb shell input keyevent --longpress keyevent 82'
        timeout_command(cmd)
        time.sleep(1)
        cmd = "adb shell \"dumpsys window | grep -i mfocusedWindow\""
        res = timeout_command(cmd)
        print(res)
        if 'com.xiaomi.mitv.smartpanel' in res:
            print('call xiaomi IA successfully')
        else:
            print('call xiaomi IA fail')
    if is_hisense_tv():
        cmd = 'adb shell input keyevent 82'
        timeout_command(cmd)
        time.sleep(1)
        cmd = "adb shell \"dumpsys window | grep -i mfocusedWindow\""
        res = timeout_command(cmd)
        print(res)
        if 'com.keylab.speech.view.vidaa' in res:
            print('call hisense IA successfully')
        else:
            print('call hisense IA fail')
    if is_tcl_tv():
        cmd = 'adb shell input keyevent 82'
        timeout_command(cmd)
        time.sleep(1)
        cmd = "adb shell \"dumpsys window | grep -i mfocusedWindow\""
        res = timeout_command(cmd)
        print(res)
        if 'SubPanel' in res:
            print('call tcl IA successfully')
        else:
            print('call tcl IA fail')
    if is_huawei_tv():
        cmd = 'adb shell input keyevent --longpress keyevent 82'
        timeout_command(cmd)
        time.sleep(1)
        cmd = "adb shell \"dumpsys window | grep -i mfocusedWindow\""
        res = timeout_command(cmd)
        print(res)
        if 'com.android.systemui' in res:
            print('call huawei IA successfully')
        else:
            print('call huawei IA fail')
        return
    return

def call_xiaoai():
    # cmd = 'adb shell getprop ro.product.name'
    # tv_model = timeout_command(cmd).lower()
    # if tv_model == 'moderntimes':
    #     print('该机型不支持小爱')
    # elif tv_model == 'maverick':
    #     print('该机型不支持小爱')
    # else:
    #     cmd = "adb shell am startservice -a 'com.xiaomi.mitv.voice.KEYDOWN' -n com.xiaomi.voicecontrol/.VoiceControlService"
    #     timeout_command(cmd)
    if is_xiaomi_tv():
        cmd = "adb shell \"pm list packages | grep com.xiaomi.voicecontrol\""
        res = timeout_command(cmd)
        if 'com.xiaomi.voicecontrol' in res:
            print('callxiaoai')
            cmd = "adb shell am startservice -a 'com.xiaomi.mitv.voice.KEYDOWN' -n com.xiaomi.voicecontrol/.VoiceControlService"
            timeout_command(cmd)
            res = get_front_package()
            if "voice-control-root" in res:
                print("call xiaoai successfully")
        else:
            print('no voicecontrol package')
    if is_hisense_tv():
        cmd = 'adb shell input keyevent 4379'
        timeout_command(cmd)
        time.sleep(1)
        cmd = "adb shell \"dumpsys window | grep -i mfocusedWindow\""
        res = timeout_command(cmd)
        print(res)
        if 'com.keylab.speech.view.vidaa' in res:
            print("call xiaoai successfully")
        else:
            print('no voicecontrol package')
    if is_huawei_tv():
        cmd = 'adb shell input keyevent --longpress keyevent 231'
        timeout_command(cmd)
        time.sleep(1)
        cmd = "adb shell \"dumpsys window | grep -i mfocusedWindow\""
        res = timeout_command(cmd)
        print(res)
        if 'FloatVoiceContentView' in res:
            print("call xiaoai successfully")
        else:
            print('no voicecontrol package')
        return
    if is_tcl_tv:
        # todo tcl暂无语音按键映射
        return
    return

def adb_quickdown(device=None):
    cmd = "adb shell input keyevent 20"
    timeout_command(cmd, device=device)


def adb_quickup(device=None):
    """模拟遥控器按 向上 键"""
    cmd = "adb shell input keyevent 19"
    timeout_command(cmd, device=device)


def adb_quickright(device=None):
    """模拟遥控器按 向右 键"""
    cmd = "adb shell input keyevent 22"
    timeout_command(cmd, device=device)


def adb_quickleft(device=None):
    """模拟遥控器按 向左 键"""
    cmd = "adb shell input keyevent 21"
    timeout_command(cmd, device=device)


# def adb_long_pressmenu():
#     cmd = "adb shell input keyevent --longpress keyevent 82"
#     timeout_command(cmd)

def breathing_screen(times, device=None):
    cmd = 'adb shell input keyevent 26'
    timeout_command(cmd, device=device)
    time.sleep(times)
    timeout_command(cmd, device=device)
    check_screen = timeout_command('adb shell getprop sys.screen.turn_on', device=device)
    if check_screen =='true':
        return True
    else:
        time.sleep(5)
        timeout_command(cmd, device=device)

def longpress_left(device=None):
    cmd = 'adb shell input keyevent --longpress keyevent 21'
    out = timeout_command(cmd, device=device)

def longpress_right(device=None):
    cmd = 'adb shell input keyevent --longpress keyevent 22'
    out = timeout_command(cmd, device=device)

def longpress_power(device=None):
    cmd = 'adb shell input keyevent --longpress 26'
    timeout_command(cmd, device=device)

def press_power(device=None):
    cmd = 'adb shell input keyevent 26'
    timeout_command(cmd, device=device)

# def app_forcestop(package_name):
#     cmd = "adb shell am force-stop {}".format(package_name)
#     timeout_command(cmd)
#
# def kill_monkey():
#     cmd = "adb shell pkill monkey"
#     timeout_command(cmd)

def imageParameters():
    cmd = 'adb shell am broadcast -a "com.xiaomi.mitv.settings.DISPLAY_SETTINGS_POPUP"'
    out=timeout_command(cmd)

def frozenxiaoai():
    back2home_page()
    cmd = "adb shell getprop ro.product.name"
    tv_id = timeout_command(cmd).lower()
    print("电视型号是%s" %tv_id)
    if tv_id == 'freeguy':
        print("小爱进程冷冻")
        adb_center()
        time.sleep(3)
        call_xiaoai()
        adb_back()
    elif tv_id == 'mulan':
        print("小爱进程冷冻")
        adb_center()
        time.sleep(3)
        call_xiaoai()
        adb_back()
    else:
        return True

# 便捷的包装函数，使用默认设备
def adb_home_default():
    """使用默认设备模拟遥控器按home键"""
    return adb_home(_default_device)

def adb_back_default():
    """使用默认设备模拟遥控器按back键"""
    return adb_back(_default_device)

def adb_up_default():
    """使用默认设备模拟遥控器按向上键"""
    return adb_up(_default_device)

def adb_down_default():
    """使用默认设备模拟遥控器按向下键"""
    return adb_down(_default_device)

def adb_left_default():
    """使用默认设备模拟遥控器按向左键"""
    return adb_left(_default_device)

def adb_right_default():
    """使用默认设备模拟遥控器按向右键"""
    return adb_right(_default_device)

def adb_center_default():
    """使用默认设备模拟遥控器按确定键"""
    return adb_center(_default_device)

def adb_volume_up_default():
    """使用默认设备模拟遥控器按音量调大键"""
    return adb_volume_up(_default_device)

def adb_volume_down_default():
    """使用默认设备模拟遥控器按音量调小键"""
    return adb_volume_down(_default_device)

def adb_power_default():
    """使用默认设备模拟遥控器按电源键"""
    return adb_power(_default_device)

def adb_menu_default():
    """使用默认设备模拟遥控器按菜单键"""
    return adb_menu(_default_device)

if __name__ == '__main__':
    # 示例：设置默认设备
    # set_default_device("192.168.1.100:5555")  # 设置默认设备

    # 使用默认设备的示例
    # adb_home_default()
    # adb_back_default()

    # 或者直接传入设备参数的示例
    # device_id = "192.168.1.100:5555"
    # adb_home(device_id)
    # adb_back(device_id)
    # y = back2home_page(device_id)
    # print(y)

    # 截图示例
    tvqs_screeencap(screenshot_2=5,interation=3,delay_start=0,interval_time=30,rename_keyword="mitv")