
def testPlayVideoFromYou<PERSON>(self):
    assert d.server.adb.raw_cmd(
        'shell am start -n com.cibn.tv/com.youku.tv.home.activity.HomeActivity'), 'can not launch Youku'
    '''
    assert d.server.adb.raw_cmd('shell am start -n com.cibn.tv/com.yunos.tv.yingshi.home.HomeActivity'), 'can not launch Youku'
    '''

    # time.sleep(2)
    d.wait.update(timeout=2000, package_name='com.cibn.tv')
    if d(textContains="是否将优酷XL设置为开机自启动？").wait.exists(timeout=6000):
        d.press.right()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        d.press.enter()
    if d(textContains="立即更新").wait.exists(timeout=6000):
        d.press.back()
    # time.sleep(4)
    d.wait.update(timeout=4000)
    # assert d(text='电视剧').wait.exists(timeout=10000), "优酷-电视剧菜单 not found on screen"

    played = 0
    # assert d(text = '电影').exists(), '非电影界面'

    while played < MOVIES:
        d.press.enter()
        if d(textContains="全屏").wait.exists(timeout=2000):
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.left()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
        else:
            d.press.back()
            # time.sleep(1)
            d.wait.update(timeout=1000)
            d.press.down()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            played += 1
            continue
        time.sleep(PLAY_TIME)

        # assert d(className='android.view.View').wait.exists(timeout=20000), 'Youku-播放视频 未开始！'

        if d(textContains="非常抱歉，该节目暂时无法在TV端播放").wait.exists(timeout=6000):
            d.press.right()
            # time.sleep(2)
            d.wait.update(timeout=2000)
            d.press.enter()
        played += 1
        # time.sleep(2)
        d.wait.update(timeout=2000)
        d.press.back()
        # time.sleep(3)
        d.wait.update(timeout=3000)
        d.press.back()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        d.press.down()

    for _ in range(5):
        d.press.back()
        # time.sleep(1)
        d.wait.update(timeout=1000)
    d.press.home()