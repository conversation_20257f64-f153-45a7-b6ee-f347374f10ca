#!/usr/bin/python
# -*- coding:utf-8 -*-
import unittest
import random
import subprocess
import time
import sys
import pexpect
import re
from uiautomator import device as d
from testconfig import config
import time

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
PLAY_TIME_LONG = config_.getint('play_options','play_time_long')
THRESHOLD = config_.getint('play_options','threshold')

# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# THRESHOLD = int(config['play_options']['threshold'])


class AutoRunTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="稍后更新").click(text="稍后更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("CLEAR_STORAGE").when(textContains='本地存储空间不足，建议进行清理').click(text="确认")
        d.wakeup()
        d.wait.update(timeout=10000)
        d.press.home()
        d.wait.update(timeout=10000, package_name='com.mitv.tvhome')
        d(text='精选').wait.exists()
        data = subprocess.Popen('adb shell busybox df /data', shell=True, stdout=subprocess.PIPE,
                                stderr=subprocess.STDOUT).communicate()[0].split()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        # d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        for _ in range(4):
            d.press.back()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        for i in range(4):
            d.press.back()
        d.press.home()

    def exec_cmd(self, cmd_line, shell=True):
        cmd_exec = subprocess.Popen(cmd_line, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        output, _ = cmd_exec.communicate()
        cmd_exec.wait()
        if cmd_exec.returncode == 0:
            return True, output
        else:
            return (False,)

    def check_expect(self, child, cmd, expect_str):
        child.sendline(cmd)
        try:
            child.expect(expect_str, timeout=300)
        except pexpect.EOF:
            assert False, '%s expect failed' % cmd
        except pexpect.TIMEOUT:
            assert False, '%s expect timeout' % cmd

    def testPushAutoRun(self):
        if not self.exec_cmd('adb push AutoRun /sdcard/')[0]:
            assert False, 'push AutoRun failed'

    def testrefresh(self):
        try:
            child = pexpect.spawn('adb shell')
            child.expect(['shell@.*', 'root@.*'])
            self.check_expect(child, 'su', 'root@.*')
            self.check_expect(child, 'sh ./sdcard/AutoRun/auto_run.sh -c testrefresh -l 1', 'had run 2 times')

        except:
            assert False, 'run autorun fail'
        finally:
            child.close(force=True)

    def testonlinevideo(self):
        try:
            child = pexpect.spawn('adb shell')
            child.expect(['shell@.*', 'root@.*'])
            self.check_expect(child, 'su', 'root@.*')
            self.check_expect(child, 'sh ./sdcard/AutoRun/auto_run.sh -c testonlinevideo -l 1', 'had run 2 times')

        except:
            assert False, 'run autorun fail'
        finally:
            child.close(force=True)

    def testappstore(self):
        try:
            child = pexpect.spawn('adb shell')
            child.expect(['shell@.*', 'root@.*'])
            self.check_expect(child, 'su', 'root@.*')
            self.check_expect(child, 'sh ./sdcard/AutoRun/auto_run.sh -c testappstore -l 1', 'had run 2 times')

        except:
            assert False, 'run autorun fail'
        finally:
            child.close(force=True)

    def testgamecenter(self):
        try:
            child = pexpect.spawn('adb shell')
            child.expect(['shell@.*', 'root@.*'])
            self.check_expect(child, 'su', 'root@.*')
            self.check_expect(child, 'sh ./sdcard/AutoRun/auto_run.sh -c testgamecenter -l 1', 'had run 2 times')

        except:
            assert False, 'run autorun fail'
        finally:
            child.close(force=True)

    def testlaunchsystemapp(self):
        try:
            child = pexpect.spawn('adb shell')
            child.expect(['shell@.*', 'root@.*'])
            self.check_expect(child, 'su', 'root@.*')
            self.check_expect(child, 'sh ./sdcard/AutoRun/auto_run.sh -c testlaunchsystemapp -l 1', 'had run 2 times')

        except:
            assert False, 'run autorun fail'
        finally:
            child.close(force=True)

    def testsearch(self):
        try:
            child = pexpect.spawn('adb shell')
            child.expect(['shell@.*', 'root@.*'])
            self.check_expect(child, 'su', 'root@.*')
            self.check_expect(child, 'sh ./sdcard/AutoRun/auto_run.sh -c testsearch -l 1', 'had run 2 times')

        except:
            assert False, 'run autorun fail'
        finally:
            child.close(force=True)
