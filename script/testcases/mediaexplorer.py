#!/usr/bin/python
# -*- coding:utf-8 -*- 
import unittest
from uiautomator import device as d
import random
import time


def fc_close(device):
    device.press.left()
    device.press.left()
    device.press.left()
    return False


class MediaExplorerTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """

        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="稍后更新").click(text="稍后更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        # super(MediaExplorerTest, self).setUp()
        # d.watcher("AUTO_FC_WHEN_ANR")#.when(text="ANR").when(text="Wait") .press.back.home()
        # d.watcher("AUTO_FC_WHEN_ANR").when(text="ANR").when(text="强行关闭") .click(text="确定")
        # d.watcher("AUTO_FC_WHEN_ANR").press.left.left.left.home()
        # d.handlers.on(fc_close)
        d.wakeup()
        time.sleep(10)
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
        used = re.search(r'^[0-9]+', data[11])

        if used:
        	data_used = int(used.group())
        	if data_used > THRESHOLD:
        		d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        		time.sleep(30)
        	else:
        		pass
        else:
        	assert False, ' data space can not match'''
        d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        time.sleep(30)
        d.press.enter()
        for _ in range(4):
            d.press.back()
        for i in range(8): d.press.left()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        for i in range(8): d.press.back()
        d.press.home()
        d.press.home()

    def testPlayLocalVideo(self):
        """
        launch  app store and exit
        """
        for i in range(5):
            d.press.right()
        assert d(text="高清播放器").exists, 'Media Explorer icon not found!'
        d(text="高清播放器").click.wait()
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/video', text="视频").wait.exists(
            timeout=20000), 'launch Media Explorer failed!'
        d.press.down()
        time.sleep(2)
        d.press.enter()
        #         assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/device_label', text="移动存储设备").wait.exists(timeout=20000), 'enter Device list screen failed!'
        #         d.press.enter()
        #         assert d(className="android.widget.ListView").child(text="视频").wait.exists(timeout=20000), 'enter USB device list failed!'
        #         d.press.enter()
        #         assert d(className="android.widget.ListView").child(resourceId="com.xiaomi.mitv.mediaexplorer:id/iv_image").wait.exists(timeout=20000), 'enter USB device video list failed!'
        #         for i in xrange(16):
        #             for j in xrange(random.randint(0, 16)):
        #                 d.press.down()
        #                 time.sleep(1)
        #             for k in xrange(random.randint(0, 8)):
        #                 d.press.up()
        #                 time.sleep(1)
        #             d.press.enter()
        assert d(className="android.widget.ListView").child(
            resourceId="com.xiaomi.mitv.mediaexplorer:id/iv_image").wait.gone(
            timeout=20000), 'start to play video failed!'
        time.sleep(60)
        for i in range(8):
            d.press.back()
            time.sleep(1)
        # exit to home screen
        assert d(packageName="com.xiaomi.tv.desktop").exists, "fail to exit from media Explorer failed!"
#         assert d(className="android.widget.ListView").child(resourceId="com.xiaomi.mitv.mediaexplorer:id/iv_image").wait.exists(timeout=20000), 'enter USB device video list failed!'
