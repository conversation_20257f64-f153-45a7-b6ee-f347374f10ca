#!/usr/bin/python
# -*- coding:utf-8 -*-

import time
import unittest
import random
from uiautomator import device as d
from script.testcases.adb_command import *

keys = ['up', 'down', 'left', 'right']

PLAY_time = 300
PLAY_TIME = 60


class VideoPlayerTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="升级").click(text="升级")
        d.watcher("CLICK_FINISH").when(text="完成").click(text="完成")
        d.watcher("CLICK_INSTALL").when(text="安装").click(text="安装")
        d.watcher("EXIT_VST_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(
            textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(
            textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(
            packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.watcher("PASS_STOP_PLAY").when(
            textContains='确认退出').click(text="确认退出")

        d.watcher("PASS_APP_Popup").when(text="按【返回】可以关闭弹窗哦").press('back')
        d.watcher("PASS_APPKEEP_Popup1").when(text="按返回键退出播放").press('back')
        d.watcher("PASS_APPKEEP_Popup2").when(
            textContains="访问您设备上的照片、媒体内容和文件吗？").press('enter')
        d.watcher("PASS_APPAIQIYI_Popup").when(
            textContains="按【返回键】").press('back')
        d.watcher("PASS_APPBilibili_Popup").when(
            text="用户协议及隐私政策 ").press('enter')
        d.watcher("PASS_APPQQmusic_Popup").when(
            textContains="我知道了").press('enter')
        d.watcher("PASS_APPPPsport_Popup").when(
            textContains="同意").press('enter')
        d.watcher("PASS_APPPPsport_Popup2").when(
            textContains="我知道了").press('enter')
        d.watcher("PASS_APPPPsport_Popup2").when(
            textContains="允许CIBN聚体育拨打电话和管理通话吗？").press('enter')
        d.watcher("PASS_APPCloudvideo_popup").when(
            textContains="再看看").press('enter')

        d.watcher(("Install app-uiautomator-test.apk")).when(
            textContains="app-uiautomator-test.apk").press.up.center.down.center()  # not work

        # d.wakeup()
        # time.sleep(10)

        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        # time.sleep(1)
        # d.press.home()
        # back2home_page()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.watchers.remove("PASS_STOP_PLAY")

        # d.press.home()
        # back2home_page()

    def testsleep_time(self):
        t=3600
        print('倒计时1小时')
        while t > 1:
            time.sleep(1)
            t-=1
        else:
            print('倒计时完成')
            # adb_home()




