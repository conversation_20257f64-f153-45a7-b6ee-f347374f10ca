#!/usr/bin/python
# -*- coding:utf-8 -*-
import unittest
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
THRESHOLD = config_.getint('play_options','threshold')

THIRD_APK = ('com.togic.livevideo', 'net.myvst.v2')
# THRESHOLD = int(config['play_options']['threshold'])


class GameCenterTest(unittest.TestCase):
    def setUp(self):
        """
called before  each test method start.
"""

        before_install = d.server.adb.cmd(
            'shell pm list package -3').communicate()[0].split()
        self.before_install = d.server.adb.cmd(
            'shell pm list package -3').communicate()[0].split()
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(
            textContains="退出").click(text="退出")
        d.watcher("INSTALL_NEW_VERSION").when(
            textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(
            textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(
            textContains="稍后更新").click(text="稍后更新")
        d.watcher("PASS_TOGIC_UPDATE").when(
            packageName='com.togic.livevideo', textContains="已阅读").press('enter', 'enter')
        d.watcher("PASS_VST_UPDATE2").when(
            textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")
        d.wakeup()
        # d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        d.wait.update(timeout=10000)
        d.press.home()
        d.wait.update(timeout=10000, package_name="com.mitv.tvhome")
        d(text='精选').wait.exists()
        '''data = d.server.adb.cmd('shell busybox df /data').communicate()[0].split()
		used = re.search(r'^[0-9]+', data[11])

		if used:
			data_used = int(used.group())
			if data_used > THRESHOLD:
				d.server.adb.cmd('shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
				time.sleep(30)
			else:
				pass
		else:
			assert False, ' data space can not match'''
        # d.server.adb.cmd(
        #     'shell am start -n com.xiaomi.mitv.tvmanager/.CleanDiskActivity --ei directlaunchid 2')
        # time.sleep(30)
        # d.press.enter()
        time.sleep(2)
        d.press.home()
        d.wait.update(timeout=2000,package_name='com.mitv.tvhome')
        for _ in range(4):
            time.sleep(1)
            d.press.back()

    def tearDown(self):
        """
called after each test method end or exception occur.
"""
        self.after_install = d.server.adb.cmd(
            'shell pm list package -3').communicate()[0].split()
        del_apk = [i.split(':')[1]
                   for i in self.after_install if i not in self.before_install]
        for apk in del_apk:
            d.server.adb.cmd('shell pm uninstall %s' % apk)
            time.sleep(3)
        for i in range(5):
            d.press.back()
            time.sleep(1)
        d.press.home()

    def testLaunchAndExitAppStore(self):
        """
        launch  app store and exit
        """
        d.server.adb.raw_cmd('shell am start -n com.mitv.tvhome/.AppStoreActivity')
        d.wait.update(timeout=60000,package_name='com.mitv.tvhome')
        assert d(text='推荐').wait.exists(timeout=20000), 'launch App Store failed!'
        d(text='推荐').click()
        d.wait.update(timeout=3000)
        for i in range(3):
            d.press.down()
            d.wait.update(timeout=2000)
        for i in range(4):
            d.press.right()
            d.wait.update(timeout=2000)
        d.press.enter()
        d.wait.update(timeout=2000)
        assert d(text="应用信息").wait.exists(timeout=5000), "loading app failed"
        # assert d(text='下载').wait.exists(timeout=50000), 'install button not exists'   #同一个app，之前已经下载过了有缓存了，显示的是“打开”控件
        if d(text='下载').wait.exists(timeout=50000):
            d(text='下载').click()
            d.wait.update(timeout=50000)
        assert d(text='打开').wait.exists(timeout=60000), 'fail to install app'
        if d(text='打开').wait.exists(timeout=60000):
            d(text='打开').click()
            d.press.enter()
            d.wait.update(timeout=10000)
        for i in range(2):
            d.press.down()
        d.press.right()
        d.press.enter()
        d.wait.update(timeout=10000)
        for i in range(3):
            d.press.back()
            d.wait.update(timeout=2000)
        '''
        if d(resourceId='com.xiaomi.mitv.appstore:id/status_text', text='下载').wait.exists(timeout=20000):
            time.sleep(2)
            d.press.enter()
            time.sleep(60)
            assert d(resourceId='com.xiaomi.mitv.appstore:id/status_text',
                     text='打开').wait.exists(timeout=60000), 'fail to install app'
            time.sleep(2)
            d(resourceId='com.xiaomi.mitv.appstore:id/status_text', text='打开').click()
            d.press.enter()
        else:
            assert d(resourceId='com.xiaomi.mitv.appstore:id/status_text',
                     text='打开').wait.exists(timeout=60000), 'not in appstore'
            time.sleep(2)
            d.press.enter()
        '''


    def testInstallAndUninstallGame(self):
        """
        launch  app store and exit
        """
        d.press.home()
        d.wait.update(timeout=5000)
        d.server.adb.raw_cmd('shell am start -n com.xiaomi.mibox.gamecenter/.MainActivity')
        d.wait.update(timeout=5000)
        assert d(text="榜单").wait.exists(timeout=2000), 'Game Center icon not found!'   # 有可能打开是一个维护公告的界面
        d(text='榜单').click()
        d.wait.update(timeout=2000)
        for i in range(2):
            d.press.down()
        # time.sleep(1)
        d.wait.update(timeout=1000)
        d.press.enter()
        # time.sleep(1)
        d.wait.update(timeout=1000)
        d.press.enter()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        if d(className='android.widget.TextView', textContains='遥控器操控').wait.exists(timeout=2000):
            d(className='android.widget.Button', text='安 装').click()
            time.sleep(60)
            assert d(className='android.widget.Button', text='启 动').wait.exists(
                timeout=600000), 'install game failed in 5 minutes!'
            d.press.back()
            # d.press.right()
        else:
            d.press.back()
        d.press.enter()
        if d(className='android.widget.Button', textContains='安 装').wait.exists(timeout=2000):
            d(className='android.widget.Button', text='安 装').click()
        time.sleep(60)
        assert d(className='android.widget.Button', text='启 动').wait.exists(
            timeout=600000), 'install game failed in 5 minutes!'
        d(className='android.widget.Button', text='启 动').click()
        time.sleep(20)
        d.press.enter()
        # time.sleep(2)
        d.wait.update(timeout=2000)
        d.press.enter()
        for i in range(2):
            d.press.back()
