#!/usr/bin/python
# -*- coding:utf-8 -*- 

import unittest
from uiautomator import device as d
from testconfig import config
from random import choice
import time

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases_m19/config.ini',encoding='utf-8')
PLAY_TIME = config_.get('play_options','play_time')
PLAY_TIME_LONG = config_.get('play_options','play_time_long')
MOVIES = config_.get('play_options','movie_nums')
TARGET_DIR = config_.get('play_options','target_dir')
keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']
AP1 = config_.get('play_options','ap1')
AP2 = 'ASUS_5G'
AP3 = config_.get('play_options','ap3')

# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR = str(config['play_options']['target_dir'])
# keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']
# AP1 = str(config['play_options']['ap1'])
# AP2 = 'ASUS_5G'
# AP3 = str(config['play_options']['ap3'])
num = 5  # num of download apps from GooglePlayStore
num1 = 5  # num of download games from GooglePlayGames


class test1(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")

        d.wakeup()
        time.sleep(10)
        d.press.back()
        time.sleep(1)
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        for _ in range(3):
            d.press.back()
            time.sleep(1)
        time.sleep(1)
        d.press.home()

    def playnetflix(self):  # play netflix
        assert d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity'), 'NETFLIX failed'
        #	time.sleep(10)
        #	d.press.enter()
        #        time.sleep(1)
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        for i in range(5):
            d.press.enter()
            time.sleep(10)
            time.sleep(PLAY_TIME)
            d.press.back()
            d.press.down()
        d.press.back()
        for i in range(2):
            d.press.right()
        d.press.enter()
        d.press.home()

    def playsling(self):  # play sling
        assert d.server.adb.raw_cmd('shell am start -n com.sling/com.movenetworks.StartupActivity'), 'sling failed'
        time.sleep(30)
        d.press.down()
        d.press.down()
        for i in range(5):
            d.press.enter()
            time.sleep(10)
            time.sleep(PLAY_TIME)
            d.press.back()
            d.press.down()
        d.press("right")
        d.press.back()
        d.press.home()

    def playrbtv(self):  # play redbullTV
        assert d.server.adb.raw_cmd(
            'shell am start -n com.nousguide.android.rbtv/com.redbull.view.launch.SplashActivity'), 'redbullTV failed'
        #	d.press.enter()
        time.sleep(5)
        for i in range(5):
            time.sleep(PLAY_TIME)
            d.press.down()
            time.sleep(2)
            d.press.down()
            time.sleep(2)
            d.press.enter()
        d.press.back()
        d.press.back()
        time.sleep(2)
        d.press.home()

    def playespn(self):  # play ESPN
        assert d.server.adb.raw_cmd(
            'shell am start -n com.espn.score_center/com.espn.androidtv.ui.LoadingActivity'), 'ESPN failed'
        time.sleep(10)
        d.press.down()
        d.press.down()
        d.press.down()
        d.press.enter()
        time.sleep(2)
        d.press.right()
        for i in range(5):
            d.press.enter()
            time.sleep(5)
            time.sleep(PLAY_TIME)
            d.press.back()
            d.press.right()
        d.press.back()
        d.press.back()

    def testPluto(self):  # pluto
        self.current_app = 'pluto'
        assert d.server.adb.raw_cmd(
            'shell am start -n tv.pluto.android/.leanback.controller.MainActivity'), 'can not start pluto'
        time.sleep(PLAY_TIME)
        played = 0
        while played < MOVIES:
            d.press.back()
            time.sleep(2)
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(PLAY_TIME)
            played += 1

    def playcdbsports(self):  # play CBS SPORTS
        assert d.server.adb.raw_cmd(
            'shell am start -n com.handmark.sportcaster/com.onelouder.cbssportstv.MainActivity'), 'CBS SPORTS failed'
        time.sleep(5)
        d(text='SHOWS').click()
        time.sleep(2)
        for _ in range(3):
            d.press.enter()
            time.sleep(1)
        time.sleep(PLAY_TIME)
        for _ in range(3):
            d.press.back()
        time.sleep(10)

    def playvudu(self):  # play vudu
        assert d.server.adb.raw_cmd(
            'shell am start -n air.com.vudu.air.DownloaderTablet/com.vudu.android.app.activities.account.WelcomeActivity'), 'vudu failed'
        time.sleep(5)
        for _ in range(4):
            d.press.enter()
            time.sleep(1)
        time.sleep(PLAY_TIME)
        for _ in range(5):
            d.press.back()
        time.sleep(5)

    def playhulu(self):  # play hulu
        assert d.server.adb.raw_cmd('shell am start -n com.hulu.livingroomplus/.MainActivity'), 'hulu failed'
        time.sleep(8)
        d.press.enter()
        time.sleep(PLAY_TIME)
        for _ in range(2):
            d.press.back()

    def playpandora(self):  # play pandora
        assert d.server.adb.raw_cmd(
            'shell am start -n com.pandora.android.atv/com.pandora.android.activity.NowPlaying'), 'pandora failed'
        time.sleep(5)
        #	d.press.left()
        #	d.press.enter()
        #	time.sleep(5)
        time.sleep(PLAY_TIME)
        d.press.up()
        for _ in range(2):
            d.press.right()
            time.sleep(1)
        d.press.enter()
        d.press.down()
        d.press.enter()
        d.press.right()
        d.press.enter()

    def playcbsnews(self):  # play cbsnews
        assert d.server.adb.raw_cmd('shell am start -n com.cbsnews.ott/.activities.StartActivity'), 'cbsnews failed'
        time.sleep(5)
        d.press.right()
        d.press.enter()
        time.sleep(10)
        time.sleep(PLAY_TIME)
        d.press.back()
        d.press.back()
        d.press.right()
        d.press.enter()

    def playcbsallaccess(self):  # play cbsallaccess
        assert d.server.adb.raw_cmd('shell am start -n com.cbs.ott/.ui.activity.SplashActivity'), 'cbsallaccess failed'
        time.sleep(10)
        d.press.down()
        d.press.down()
        d.press.enter()
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.back()
        d.press.back()

    def switchWifi(self, ap):  # switch WIFI, then play youtube
        '''	
        assert d.server.adb.raw_cmd('shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'),'can not start youtube'
            time.sleep(2)
            d.press.up()
            time.sleep(2)
            d.press.enter()       
            d.press.right()
            d.press.right()
            d.press.right()
            # input:escape plan
            d.press(0x00000021)
            d.press(0x0000002f)
            d.press(0x0000001f)
            d.press(0x0000001d)
            d.press(0x0000002c)
            d.press(0x00000021)
            d.press(0x0000003e)
            d.press(0x0000002c)
            d.press(0x00000028)
            d.press(0x0000001d)
            d.press(0x0000002a)
            time.sleep(5)
            d.press.back()
            #d(text='SEARCH').click() 
            d.press.enter()
            time.sleep(PLAY_TIME)
            d.press.home()  
        '''
        assert d.server.adb.raw_cmd(
            'shell am start -n com.android.tv.settings/.connectivity.NetworkActivity'), 'launch wifi failed'
        time.sleep(2)
        d.press.enter()
        time.sleep(2)
        if d(textContains='On').wait.exists(timeout=6000):
            d(textContains='On').click()
            time.sleep(1)
            d.press.left()
            time.sleep(1)
            d.press.right()
            time.sleep(5)
        flag = True
        for i in range(5):
            if d(textContains='See all').exists:
                d(textContains='See all').click()
                time.sleep(1)
                d.press.left()
                time.sleep(1)
                d.press.right()
                time.sleep(10)
                flag = False
                break
            else:
                d.press.down()
                time.sleep(2)
        time.sleep(10)
        if flag:
            assert False, 'can not find see all'
        time.sleep(5)
        flag1 = True
        for i in range(40):
            if d(textContains=ap).exists:
                d(textContains=ap).click()
                flag1 = False
                if d(text="CONNECT").exists:
                    d(text="CONNECT").click()
                    break
            else:
                d.press.down()
                time.sleep(2)
        if flag1:
            assert False, 'can not find AP'
        time.sleep(5)
        d.press.home()
        time.sleep(10)

        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'), 'can not start youtube'
        time.sleep(5)
        d.press.up()
        time.sleep(2)
        d.press.enter()
        assert d(textContains='Speak to search YouTube').wait.exists(timeout=6000), 'not at search interface'
        for i in range(3):
            d.press.right()
            time.sleep(2)
        # input:escape plan
        d.press(0x00000021)
        d.press(0x0000002f)
        d.press(0x0000001f)
        d.press(0x0000001d)
        d.press(0x0000002c)
        d.press(0x00000021)
        d.press(0x0000003e)
        d.press(0x0000002c)
        d.press(0x00000028)
        d.press(0x0000001d)
        d.press(0x0000002a)
        time.sleep(5)
        d.press.back()
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.home()

    def testupdate(self):  # version update
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings'), 'Settings failed'
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.about.AboutActivity'), 'About failed'
        d(textContains="system update").click()
        if (d(text="Check for update").exists):
            d(text="Check for update").click()
            time.sleep(300)
        if (d(textContains="Restart").exists):
            d(textContains="Restart").click()
            time.sleep(300)
        else:
            d.press.home()
        time.sleep(3600)

    def testYoutube(self):
        self.current_app = "youtube"
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'), 'can not start youtube'
        time.sleep(8)
        d.press.right()

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(PLAY_TIME)
            d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(2)
            played += 1

    def testgooglemovies(self):  # test google play movies & TV
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.videos/.tv.usecase.home.TvHomeActivity'), 'can not start google play movies and TV'
        time.sleep(10)
        d.press.enter()
        time.sleep(1)
        d.press.enter()
        time.sleep(1)
        time.sleep(PLAY_TIME)
        d.press.back()
        d.press.back()

    def connectWifi(self):  # same AP: 5g->2.4g->5g
        self.switchWifi(AP1)

    def connectWifi_AP(self):  # different AP: AP1_5g ->AP2_5g 
        self.switchWifi(AP3)

    def testgooglemusic(self):  # test google play music
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.music/com.android.music.activitymanagement.TopLevelActivity'), 'can not start google play music'
        d.press.enter()
        d.press.down()
        d.press.down()
        d.press.back()
        d.press.back()

    def testgooglestore(self):  # test google play store
        assert d.server.adb.raw_cmd(
            'shell am start -n com.android.vending/com.google.android.finsky.activities.TvMainActivity'), 'can not start google play store'
        d.press.enter()
        played1 = 0
        while played1 < num:
            d.press.enter()
            time.sleep(2)
            if d(text='Install').exists:
                d.press.enter()
                time.sleep(30)
                #	        assert d(text='Open').wait.exists(timeout=5000),'open failed'
                #	        if d(text='Open').exists:
                #		     assert d(text='Open'),'open failed'
                #		     time.sleep(20)
                #		     d.press.back()
                d.press.back()
            #               time.sleep(60) #download new app from store,60 is download_time
            #	        d.press.enter() #open this app
            #	        time.sleep(PLAY_TIME) #play this app
            else:
                d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(2)
            played1 += 1
        d.press.back()
        d.press.back()
        time.sleep(600)

    def testgooglegames(self):  # test google play games
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.play.games/com.google.android.gms.games.pano.activity.MainPanoActivity'), 'can not start google play games'
        d.press.enter()
        time.sleep(5)
        d.press.right()
        played2 = 0
        while played2 < num1:
            d.press.enter()
            time.sleep(2)
            if d(text='Install').exists:
                d.press.enter()
                time.sleep(3)
                d(text='Install').click()
                time.sleep(30)
                d.press.back()
                d.press.back()
            else:
                d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(2)
            played2 += 1
        d.press.back()
        d.press.back()
        time.sleep(600)

    def playgames(self):  # play google games_REDBULL
        assert d.server.adb.raw_cmd(
            'shell am start -n com.FDGEntertainment.redball4.gp/.RedBall4'), 'can not start REDBULL'
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.back()
        d.press.left()
        d.press.enter()

    def BT(self):  # play google games_REDBULL
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings'), 'can not start setting'
        d.press.down()
        if d(text='PLT_M165').exists:
            assert d(text='PLT_M165').click(), 'BT failed'
            d(text='PLT_M165').click()
            d(text='Unpair').click()
            d(text='OK').click()
            time.sleep(10)
            d.press.home()
        else:
            assert d(text='Add accessory').click(), 'BT failed'
            d(text='Add accessory').click()
            time.sleep(5)
            #	       assert d(text='PLT_M165').click(),'not find BT:PLT_M165 '	
            if d(text='PLT_M165').exists:
                d(text='PLT_M165').click()
                time.sleep(20)
                assert d(text='PLT_M165').exists, 'BT failed '
                d.press.home()
            else:
                d.press.home()
        time.sleep(PLAY_TIME)

    def Play(self):  # 定义文件名
        d.press.home()  # 进入主界面
        time.sleep(2)  # 等待2秒
        d.press.up()
        d.press.right()
        d.press.enter()
        time.sleep(2)
        d.press(
            0x0b)  # 以下0x00的是键入文字搜索的keycode（4k for demo和Taylor），参考链接：https://developer.android.com/reference/android/view/KeyEvent.html                      
        d.press(0x27)
        d.press(0x3e)
        d.press(0x22)
        d.press(0x2b)
        d.press(0x2e)
        d.press(0x3e)
        d.press(0x20)
        d.press(0x21)
        d.press(0x29)
        d.press(0x2b)
        d.press(0x42)
        time.sleep(2)
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.home()
        time.sleep(2)
        d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'), 'can not start youtube'  # 打开youtube
        time.sleep(2)
        d.press.up()
        d.press.enter()
        d.press.right()
        d.press.right()
        d.press(0x30)
        d.press(0x1d)
        d.press(0x35)
        d.press(0x28)
        d.press(0x2b)
        d.press(0x2e)
        d.press(0x42)
        time.sleep(2)
        d.press.right()
        d.press(0x42)
        time.sleep(2)
        time.sleep(PLAY_TIME)
        d.press.back()
        d.press.home()

    def testhbogo(self):
        assert d.server.adb.raw_cmd('shell am start -n com.hbo.go/.LaunchActivity'), 'can not start hbogo'
        time.sleep(5)
        d.press.enter()
        d.press.enter()
        d.press.enter()
        if d(textContains='Retry').exists:
            d(text='Retry').click()
        time.sleep(PLAY_TIME)
        d.press.back()
        time.sleep(2)
        d.press.back()
        time.sleep(2)
        d.press.back()
        time.sleep(2)
        d.press.back()
        time.sleep(2)

    def testhbonow(self):
        assert d.server.adb.raw_cmd('shell am start -n com.hbo.hbonow/.splash.SplashActivity'), 'can not start hbogo'
        time.sleep(5)
        d.press.enter()
        d.press.enter()
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.back()
        time.sleep(2)
        d.press.back()
        time.sleep(2)
        d.press.back()
        time.sleep(2)
        d.press.back()
        time.sleep(2)
