#!/usr/bin/python
# -*- coding:utf-8 -*- 

import unittest
from uiautomator import device as d
from testconfig import config
from random import choice
import time

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases_m19/config.ini',encoding='utf-8')
PLAY_TIME = config_.get('play_options','play_time')
PLAY_TIME_LONG = config_.get('play_options','play_time_long')
MOVIES = config_.get('play_options','movie_nums')
TARGET_DIR = config_.get('play_options','target_dir')
keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']

# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR = str(config['play_options']['target_dir'])
# keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']


class OnlineVideoTest(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter','enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")

        d.wakeup()
        time.sleep(10)
        d.press.back()
        time.sleep(1)
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        for _ in range(3):
            d.press.back()
            time.sleep(1)
        time.sleep(1)
        d.press.home()

    def testPlayVideoFromVSTClient(self):
        self.current_app = "VST"
        assert d.server.adb.raw_cmd(
            'shell am start -n net.myvst.v2/com.vst.itv52.v1.LancherActivity'), 'can not launch VST'
        if d(textContains="下次更新").wait.exists(timeout=15000):
            d.press.right()
            time.sleep(2)
            d.press.enter()

        if d(textContains="解码器补丁").wait.exists(timeout=15000):
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(5)
            d.press.down()
            time.sleep(1)
            d.press.enter()

        assert d(text="搜索").wait.exists(timeout=20000), "VST-搜索 not found on screen"
        d.click(948, 1021)
        time.sleep(8)
        assert d(textContains="本周热门").wait.exists(timeout=10000), "进入 vst-电影 失败！"
        time.sleep(5)
        for _ in range(2):
            d.press.left()
            time.sleep(1)
        time.sleep(2)
        d(text='免费首播').click()
        time.sleep(3)
        d.press.right()
        time.sleep(1)
        d.press.right()
        time.sleep(1)

        played = 0
        #         d.press.enter()
        while played < MOVIES:
            time.sleep(1)
            d.press.enter()
            if not d(text="播放").wait.exists(timeout=10000) and not d(text="续播").wait.exists(timeout=10000):
                assert d(text="影片已下线").wait.exists(timeout=10000), "非播放或视频已下线界面"
                d.press.back()
                time.sleep(1)
                d.press.down()
                time.sleep(2)
                played += 1
                continue
            else:
                d.press.enter()
                assert d(className='android.view.View').wait.exists(timeout=20000), 'VST-播放视频 未开始！'
                time.sleep(PLAY_TIME)
                if not d(text="播放").wait.exists(timeout=5000):
                    for _ in range(2):
                        d.press.back()
                        time.sleep(0.2)
                played += 1
            time.sleep(1)
            d.press.back()
            time.sleep(1)
            d.press.down()
            time.sleep(2)

        d.press.back()
        time.sleep(2)
        d.press.enter()

    def testPlayVideoFromTogicClient(self):
        self.current_app = "Togic"
        assert d.server.adb.raw_cmd(
            'shell am start -n com.togic.livevideo/com.togic.launcher.SplashActivity'), 'can not launch Togic'
        if d(textContains="手机/平板继续使用").wait.exists(timeout=3000):
            d.press.enter()
        if d(textContains="立即更新").wait.exists(timeout=8000):
            d.press.back()
        assert d(packageName='com.togic.livevideo', text='影视').wait.exists(timeout=10000), "泰捷-影视菜单 not found on screen"

        def locate():
            for _ in range(2):
                d.press.up()
                time.sleep(1)
            d.press.down()
            time.sleep(1)
            d.press.right()
            time.sleep(1)
            d.press.right()
            time.sleep(1)
            d.press.down()
            time.sleep(1)
            d.press.enter()

        if d(text='影视').selected:
            locate()
            assert d(text="电影").wait.exists(timeout=10000), "进入 泰捷-电影 失败！"
            d.press.right()
            time.sleep(5)
        else:
            for _ in range(6):
                d.press.left()
                time.sleep(1)
            if not d(text='影视').selected:
                assert False, "焦点没有在影视上"
            else:
                locate()
                assert d(text="电影").wait.exists(timeout=10000), "进入 泰捷-电影 失败！"
                d.press.right()
                time.sleep(5)

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(2)
            if not d(resourceId='com.togic.livevideo:id/play', text="播放").wait.exists(timeout=10000):
                assert d(textContains="节目已失效").wait.exists(timeout=10000), "非播放或节目已失效界面"
                d.press.back()
                time.sleep(1)
                d.press.down()
                time.sleep(2)
                played += 1
                continue
            else:
                d.press.enter()
                time.sleep(5)
                if not d(resourceId='com.togic.livevideo:id/video_layout').wait.exists(timeout=20000):
                    d.press.back()
                    time.sleep(1)
                    d.press.back()
                    time.sleep(1)
                    d.press.down()
                    time.sleep(2)
                    played += 1
                    continue
                time.sleep(PLAY_TIME)
                if d(resourceId='com.togic.livevideo:id/play', text="播放").wait.exists(timeout=3000):
                    d.press.back()
                    time.sleep(1)
                else:
                    for _ in range(2):
                        d.press.back()
                        time.sleep(1)
                played += 1

            d.press.down()
            time.sleep(2)

        d.press.back()
        time.sleep(2)
        d.press.back()

    def testPlayVideoFromMolitv(self):
        self.current_app = "Moli"
        assert d.server.adb.raw_cmd(
            'shell am start -n com.molitv.android/.activity.LauncherActivity'), 'can not launch Moli'
        time.sleep(12)
        d.press.left()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(3)
        d.press.right()
        time.sleep(1)
        d.press.down()
        time.sleep(3)

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(3)
            d.press.enter()
            assert d(className='android.view.View').wait.exists(timeout=20000), 'Moli-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            if not d(text="开始播放").wait.exists(timeout=10000):
                for _ in range(2):
                    d.press.back()
                    time.sleep(0.5)
            played += 1
            time.sleep(1)
            d.press.back()
            time.sleep(1)
            d.press.down()
            time.sleep(3)

        for _ in range(5):
            d.press.back()
            time.sleep(1)
        d.press.enter()

    def testPlayVideoFromBuding(self):
        self.current_app = "Buding"
        assert d.server.adb.raw_cmd(
            'shell am start -n com.dianlv.tv/com.verycd.tv.VeryCDHomeAct'), 'can not launch Buding'
        if d(textContains="应用更新").wait.exists(timeout=6000):
            d(text="下次再说").click()
            time.sleep(1)
        if d(textContains="手机/平板继续使用").wait.exists(timeout=6000):
            d.press.enter()
        d.press.right()
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.enter()
        time.sleep(2)

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(3)
            d.press.enter()
            time.sleep(3)
            if d(textContains="继续播放").wait.exists(timeout=2000):
                d.press.enter()
            assert d(className='android.view.View').wait.exists(timeout=20000), 'Buding-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            d.press.back()
            played += 1
            time.sleep(1)
            d.press.back()
            time.sleep(1)
            d.press.down()
            time.sleep(3)

        for _ in range(6):
            d.press.back()
            time.sleep(0.8)

    def testPlayVideoFromDianshimao(self):
        self.current_app = "dian shi mao"
        assert d.server.adb.raw_cmd(
            'shell am start -n com.moretv.android/.StartActivity'), 'can not launch Dian shi mao'
        time.sleep(10)
        d.press.down()
        time.sleep(1)
        d.press.down()
        time.sleep(1)
        d.press.right()
        time.sleep(1)
        d.press.enter()
        time.sleep(3)
        d.press.right()
        time.sleep(3)

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(3)
            if d(text="播放影片").wait.exists(timeout=2000):
                d.press.enter()
            else:
                d.press.back()
                time.sleep(1)
                d.press.down()
                time.sleep(2)
                played += 1
                continue
            assert d(className='android.view.View').wait.exists(timeout=20000), 'Dianshimao-播放视频 未开始！'
            time.sleep(PLAY_TIME)
            d.press.back()
            time.sleep(2)
            d.press.enter()
            played += 1
            time.sleep(2)
            d.press.back()
            time.sleep(1)
            d.press.down()
            time.sleep(3)

        for _ in range(3):
            d.press.back()
            time.sleep(1)
        d.press.enter()

    def testYoutube(self):
        self.current_app = "youtube"
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'), 'can not start youtube'
        time.sleep(8)
        d.press.right()

        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(PLAY_TIME)
            d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(2)
            played += 1

    def testPluto(self):
        self.current_app = 'pluto'
        assert d.server.adb.raw_cmd(
            'shell am start -n tv.pluto.android/.leanback.controller.MainActivity'), 'can not start pluto'
        time.sleep(PLAY_TIME)
        played = 0
        while played < MOVIES:
            d.press.back()
            time.sleep(2)
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(PLAY_TIME)
            played += 1

    def testBloomberg(self):
        self.current_app = 'bloomberg'
        assert d.server.adb.raw_cmd(
            'shell am start -n com.bloomberg.btva/.mvc.ControllerActivity_'), 'can not start Bloomberg'
        time.sleep(10)
        d.press.right()
        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(PLAY_TIME)
            d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(1)
            played += 1

    def testTed(self):
        self.current_app = 'ted'
        assert d.server.adb.raw_cmd(
            'shell am start -n com.ted.android.tv/.ui.activity.SplashScreenActivity'), 'can not start ted'
        time.sleep(10)
        d.press.right()
        time.sleep(2)
        played = 0
        while played < MOVIES:
            d.press.enter()
            time.sleep(2)
            d.press.enter()
            time.sleep(PLAY_TIME)
            d.press.back()
            time.sleep(2)
            d.press.right()
            time.sleep(1)
            played += 1

    def testHuffpost(self):
        self.current_app = 'huffpost'
        assert d.server.adb.raw_cmd(
            'shell am start -n com.aol.on.androidtv.huffpostlive/com.aol.on.googletv.GTVNativeWebActivity'), 'can not start huffpost'
        time.sleep(15)
        played = 0
        d.press.right()
        time.sleep(2)
        while played < MOVIES:
            d.press.enter()
            time.sleep(PLAY_TIME)
            d.press.back()
            time.sleep(2)
            d.press.down()
            time.sleep(1)
            played += 1

    def testRandom(self):
        for _ in range(1000):
            key = choice(keys)
            getattr(d.press, key)()
