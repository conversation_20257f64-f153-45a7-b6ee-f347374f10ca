#!/usr/bin/python
# -*- coding:utf-8 -*-

import unittest
from uiautomator import device as d
from testconfig import config
from random import choice
import os
import time
from time import sleep

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases_m19/config.ini',encoding='utf-8')
WIFI_SSID = config_.get('account','wifi_ssid')
WIFI_PWD = config_.get('account','wifi_pwd')
NETFLIX = config_.get('account','netflix')
NETFLIX_PWD = config_.get('account','netflix_pwd')
SLING = config_.get('account','sling')
SLING_PWD = config_.get('account','sling_pwd')
GOOGLE = config_.get('account','google')
GOOGLE_PWD = config_.get('account','google_pwd')
VUDU = config_.get('account','vudu')
VUDU_PWD = config_.get('account','vudu_pwd')
PANDORA = config_.get('account','pandora')
PANDORA_PWD = config_.get('account','pandora_pwd')
SHOWTIME = config_.get('account','showtime')
SHOWTIME_PWD = config_.get('account','showtime_pwd')
CBS = config_.get('account','cbs')
CBS_PWD = config_.get('account','cbs_pwd')
HULU = config_.get('account','hulu')
HULU_PWD = config_.get('account','hulu_pwd')

PLAY_TIME = config_.get('play_options','play_time')
PLAY_TIME_LONG = config_.get('play_options','play_time_long')
MOVIES = config_.get('play_options','movie_nums')
TARGET_DIR = config_.get('play_options','target_dir')
keys = ['up', 'down', 'left', 'right', 'enter']
AP1 = config_.get('play_options','ap1')
AP2 = 'ASUS_5G'
AP3 = config_.get('play_options','ap3')

# WIFI_SSID = str(config['account']['wifi_ssid'])
# WIFI_PWD = str(config['account']['wifi_pwd'])
# NETFLIX = str(config['account']['netflix'])
# NETFLIX_PWD = str(config['account']['netflix_pwd'])
# SLING = str(config['account']['sling'])
# SLING_PWD = str(config['account']['sling_pwd'])
# GOOGLE = str(config['account']['google'])
# GOOGLE_PWD = str(config['account']['google_pwd'])
# VUDU = str(config['account']['vudu'])
# VUDU_PWD = str(config['account']['vudu_pwd'])
# PANDORA = str(config['account']['pandora'])
# PANDORA_PWD = str(config['account']['pandora_pwd'])
# SHOWTIME = str(config['account']['showtime'])
# SHOWTIME_PWD = str(config['account']['showtime_pwd'])
# CBS = str(config['account']['cbs'])
# CBS_PWD = str(config['account']['cbs_pwd'])
# HULU = str(config['account']['hulu'])
# HULU_PWD = str(config['account']['hulu_pwd'])
#
# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR = str(config['play_options']['target_dir'])
# keys = ['up', 'down', 'left', 'right', 'enter']
# AP1 = str(config['play_options']['ap1'])
# AP2 = 'ASUS_5G'
# AP3 = str(config['play_options']['ap3'])
num = 5  # num of download apps from GooglePlayStore
num1 = 5  # num of download games from GooglePlayGames


class test1(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")

        d.wakeup()
        time.sleep(10)
        d.press.home()
        time.sleep(2)

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.server.adb.raw_cmd("pkill uiautomator")
        for _ in range(5):
            d.press.back()
        sleep(3)
        d.press.home()
        time.sleep(2)

    def presskey(self, key, num):
        for i in range(num):
            getattr(d.press, key)()

    def testRename(self):
        d.server.adb.raw_cmd('shell am start -S com.android.tv.settings/.MainSettings')
        sleep(3)

        # d(text='About').click()  # this button on GTV clickable is false
        # sleep(1)
        if d(resourceId="android:id/title").exists and d(text="System").exists:
            for _ in range(6):
                d.press.up()
                sleep(1)
            for _ in range(5):
                d.press.down()
                sleep(2)
            d.press.enter()  # 进入System
            sleep(3)

        if d(resourceId="android:id/title").exists and d(text="System").exists:
            for _ in range(5):
                d.press.up()
            sleep(1)
            d.press.down()
            sleep(2)
            d.press.enter()  #进入About
            sleep(2)

        d.press.down()
        sleep(2)
        for _ in range(3):
            d.press.enter()
            sleep(2)
        for _ in range(8):
            d.press.enter()
            sleep(2)
            d.press.enter()
            sleep(2)
            d.press.down()
            sleep(2)
            d.press.enter()
            sleep(2)
        for _ in range(2):
            d.press.enter()
            sleep(2)
        for _ in range(10):
            d.press.up()
            sleep(1)
        for _ in range(10):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)

        d.server.adb.raw_cmd('shell input text mtbfjaws')
        sleep(2)
        d.press.enter()
        sleep(2)
        d.press.back()
        sleep(5)
        d.press.back()
        sleep(5)
        d.press.back()
        sleep(5)


    def testDisplaySound(self):
        d.server.adb.raw_cmd('shell am start -S com.android.tv.settings/.MainSettings')
        sleep(3)
        d.press.enter()
        sleep(3)

        for i in range(5):
            sleep(2)
            if i == 0:  # check resolution one by one
                d.press.down()
                sleep(2)
                for _ in range(4):
                    d.press.enter()
                    sleep(3)

                d.press.down()
                for _ in range(10):
                    self.presskey("down", 1)
                    sleep(3)
                    d.press.enter()
                    sleep(10)
                    d.press.right()
                    sleep(2)
                    d.press.enter()
                    sleep(3)
                else:
                    for _ in range(15):
                        d.press.up()
                        sleep(1)
                d.press.down()
                sleep(2)
                d.press.enter()
                sleep(10)
                d.press.right()
                sleep(2)
                d.press.enter()
                sleep(3)
                d.press.back()
                sleep(2)

            if i == 1:
                d.press.down()
                sleep(2)
                d.press.down()
                sleep(2)
                d.press.enter()
                sleep(2)
                d.press.enter()
                sleep(2)

                for j in range(10):
                    if j == 0:
                        d.press.enter()
                        sleep(10)
                        d.press.right()
                        sleep(2)
                        d.press.enter()
                        sleep(3)
                    else:
                        self.presskey("down", 1)
                        sleep(3)
                        d.press.enter()
                        sleep(10)
                        d.press.right()
                        sleep(2)
                        d.press.enter()
                        sleep(3)
                else:
                    for _ in range(15):
                        d.press.up()
                        sleep(1)
                    d.press.enter()
                    sleep(10)
                    d.press.right()
                    sleep(2)
                    d.press.enter()
                    sleep(3)
                    d.press.back()
                    sleep(2)
                    d.press.back()
                    sleep(2)
            if i == 2:
                d.press.down()
                sleep(2)
                d.press.enter()
                sleep(2)
                for _ in range(10):
                    d.press.enter()
                    sleep(5)
                d.press.back()
                sleep(4)
                d.press.back()
                sleep(2)

            if i == 3:
                for _ in range(2):
                    d.press.down()
                    sleep(2)
                    d.press.enter()
                    sleep(2)
                for _ in range(20):
                    d.press.enter()
                    sleep(2)
                d.press.up()
                sleep(2)
                d.press.enter()
                sleep(2)
                for _ in range(20):
                    d.press.enter()
                    sleep(2)
                d.press.back()
                sleep(2)

            if i == 4:
                d.press.down()
                sleep(2)
                d.press.enter()
                sleep(2)
                d.press.enter()
                sleep(2)
                for _ in range(2):
                    d.press.down()
                    sleep(2)
                    d.press.enter()
                    sleep(2)
                for _ in range(2):
                    d.press.up()
                    sleep(2)
                    d.press.enter()
                    sleep(2)
                d.press.back()
                sleep(2)

                d.press.down()
                sleep(2)
                d.press.enter()
                sleep(2)
                for _ in range(3):
                    d.press.down()
                    sleep(2)
                    d.press.enter()
                    sleep(2)
                for _ in range(3):
                    d.press.up()
                    sleep(2)
                    d.press.enter()
                    sleep(2)
                d.press.back()
                sleep(2)

    def testnetflixSignin(self):
        for i in range(10):
            d.server.adb.raw_cmd("shell pm clear com.netflix.ninja")
            time.sleep(5)
            assert d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity'), 'launch NETFLIX failed'
            time.sleep(30)
            d.press.back()
            sleep(3)
            d.press.down()
            sleep(1)
            d.press.left()
            sleep(1)
            d.press.enter()
            time.sleep(5)
            d.server.adb.raw_cmd("shell input text '%s'" % NETFLIX)
            time.sleep(5)
            for _ in range(3):
                d.press.down()
            d.press.enter()
            time.sleep(3)
            d.server.adb.raw_cmd("shell input text '%s'" % NETFLIX_PWD)
            time.sleep(5)
            for _ in range(5):
                d.press.down()
            d.press.enter()
            time.sleep(5)
            d.press.enter()
            time.sleep(20)
            d.press.enter()
            sleep(5)
            d.press.enter()
            time.sleep(120)

    def testGooglesignin(self):
        d.server.adb.raw_cmd("shell pm clear com.google.android.gms")
        time.sleep(5)
        d.server.adb.raw_cmd("shell am start -n com.android.tv.settings/com.android.tv.settings.MainSettings")
        time.sleep(3)
        d.press.down()
        sleep(3)
        d.press.enter()
        sleep(5)
        d.server.adb.raw_cmd("shell input text xm11111111111")
        sleep(5)
        d.press.back()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.right()
        sleep(2)
        d.press.enter()
        sleep(8)

        d.server.adb.raw_cmd("shell input text Xiaomi@123")
        sleep(5)
        d.press.back()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.right()
        sleep(2)
        d.press.enter()
        sleep(8)
        d.press.enter()
        sleep(3)

    def testSlingdownload(self):
        d.server.adb.raw_cmd(
            "shell am start -n com.android.vending/com.google.android.finsky.tvmainactivity.TvMainActivity")
        time.sleep(5)
        d.press.up()
        time.sleep(5)
        d.press.left()
        time.sleep(5)
        d.press.enter()
        time.sleep(5)
        d.press.right()
        time.sleep(5)
        d.server.adb.raw_cmd("shell input text sling")
        time.sleep(5)
        d.press.back()
        time.sleep(5)
        # d.press.enter()
        # time.sleep(5)
        d.press.enter()
        time.sleep(40)

    def testprimevideo(self):
        d.server.adb.raw_cmd('shell am start -n com.amazon.amazonvideo.livingroom/com.amazon.ignition.IgnitionActivity')
        time.sleep(10)
        d.press.enter()
        sleep(3)
        d.press.down()
        time.sleep(3)
        d.press.down()
        time.sleep(3)
        d.press.enter()
        time.sleep(3)
        d.press.enter()
        time.sleep(60)
        d.press.back()
        time.sleep(2)
        for _ in range(10):
            d.press.down()
            time.sleep(1)
            d.press.enter()
            time.sleep(5)
            d.press.enter()
            time.sleep(8)
            d.press.back()
            time.sleep(3)
            d.press.back()
            time.sleep(3)
        for _ in range(100):
            key = choice(keys)
            getattr(d.press, key)()
            time.sleep(1)

    def testnetflix(self):
        d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity')
        time.sleep(10)
        d.press.enter()
        time.sleep(60)
        d.press.back()
        time.sleep(2)
        for _ in range(10):
            d.press.down()
            time.sleep(1)
            d.press.enter()
            time.sleep(5)
            d.press.enter()
            time.sleep(8)
            d.press.back()
            time.sleep(3)
            d.press.back()
            time.sleep(3)
        for _ in range(100):
            key = choice(keys)
            getattr(d.press, key)()
            time.sleep(1)

    def appsleep(self):
        time.sleep(20)
        d.press.home()
        time.sleep(5)

    def testlaunchapp(self):
        assert d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity'), 'launch NETFLIX failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.amazon.amazonvideo.livingroom/com.amazon.ignition.IgnitionActivity'), 'launch primevideo failed'
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.music/.tv.HomeActivity'), 'launch google music failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.videos/com.google.android.apps.play.movies.tv.usecase.home.TvHomeActivity'), 'launch google video failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.cobalt.activity.MainActivity'), 'launch youtube failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.droidlogic.videoplayer/.FileList'), 'launch videoplayer failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.play.games/com.google.android.gms.games.pano.ui.profile.PanoGamesSignUpActivity'), 'launch google games failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.android.vending/com.google.android.finsky.activities.TvMainActivity'), 'launch google store failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.tv/com.android.tv.TvActivity'), 'launch livetv failed'
        self.appsleep()
        time.sleep(20)
        d.press.back()
        time.sleep(3)
        d.press.home()
        time.sleep(3)

    def presshome(self):
        d.press.home()
        time.sleep(2)
        self.presskey("back", 2)
        self.presskey("up", 3)
        self.presskey("left", 3)

    def testhome(self):
        for j in range(4):
            d.press.home()
            time.sleep(2)
            self.presskey("right", j)
            d.press.down()
            time.sleep(2)
            d.press.down()
            time.sleep(2)
            for i in range(3):
                self.presskey("down", i)
                for _ in range(10):
                    time.sleep(2)
                    d.press.enter()
                    time.sleep(10)
                    d.press.home()
                    time.sleep(2)
                    d.press.right()
                    time.sleep(2)
        for _ in range(100):
            key = choice(keys)
            getattr(d.press, key)()
            time.sleep(1)

    # 桌面上第一行favorite apps 9个应用进入退出
    def testfavoriteapps(self):
        for i in range(9):
            d.press.home()
            time.sleep(2)
            self.presskey("down", 1)
            time.sleep(1)
            self.presskey("left", i)
            time.sleep(2)
            self.presskey("right", i)
            time.sleep(1)
            d.press.enter()
            time.sleep(10)
            d.press.enter()
            time.sleep(10)
            d.press.enter()
            time.sleep(10)
            d.press.home()
            time.sleep(1)

    def testYoutubePlay(self):
        self.presshome()
        time.sleep(3)
        for i in range(20):
            d.server.adb.raw_cmd(
                'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.ShellActivity')
            time.sleep(20)
            self.presskey("right", i + 2)
            time.sleep(3)
            self.presskey("down", i)
            d.press.enter()
            time.sleep(10)
            # 快进
            d.press.enter()
            time.sleep(1)
            d.press.up()
            self.presskey("right", 3)
            time.sleep(2)
            d.press.down()
            time.sleep(5)
            # 快退
            d.press.enter()
            time.sleep(1)
            d.press.up()
            self.presskey("left", 3)
            time.sleep(2)
            d.press.down()
            time.sleep(5)
            # 暂停
            self.presskey("enter", 2)
            time.sleep(10)
            d.press.enter()
            # 下一个
            time.sleep(1)
            d.press.right()
            d.press.enter()
            time.sleep(10)
            # 上一个
            d.press.enter()
            d.press.left()
            d.press.enter()
            time.sleep(10)
            # 音量调节
            d.server.adb.raw_cmd("input KEYCODE_VOLUME_UP")
            time.sleep(2)
            d.server.adb.raw_cmd("input KEYCODE_VOLUME_DOWN")
            time.sleep(2)
            # 播放3分钟
            time.sleep(180)
            d.press.back()
            time.sleep(3)
            # random test
            for _ in range(100):
                key = choice(keys)
                getattr(d.press, key)()
                time.sleep(1)

    def testGooglePlayMovies(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.google.android.videos/com.google.android.apps.play.movies.tv.usecase.home.TvHomeActivity')
        time.sleep(10)
        self.presskey("right", 2)
        self.presskey("down", 4)
        d.press.enter()
        for _ in range(10):
            if d(text="PLAY TRAILER").wait.exists(timeout=10000):
                d.press.enter()
                time.sleep(20)
                self.presskey("right", 3)
                time.sleep(20)
                self.presskey("left", 3)
                time.sleep(20)
                d.press.back()
                time.sleep(5)
                d.press.down()
                time.sleep(1)
                d.press.enter()
            else:
                d.press.back()
                time.sleep(5)
                d.press.down()
                time.sleep(1)
                d.press.enter()
                time.sleep(1)

    def testGoogleSearch(self):
        self.presshome()
        time.sleep(1)
        d.press.left()
        time.sleep(2)
        d.press.enter()
        time.sleep(3)
        self.presshome()
        time.sleep(1)
        d.press.left()
        time.sleep(3)
        d.press.enter()
        time.sleep(3)
        d.server.adb.raw_cmd("shell input text video")
        time.sleep(5)
        d.press.enter()
        time.sleep(5)
        d.press.enter()
        time.sleep(30)

    def testGooglePlayStore(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.android.vending/com.google.android.finsky.tvmainactivity.TvMainActivity')
        time.sleep(10)
        for _ in range(5):
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(2)
            d.press.enter()
            time.sleep(30)
            d.press.back()
            time.sleep(3)

    def testGooglePlayGames(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.google.android.play.games/com.google.android.apps.play.games.app.atv.features.home.HomeActivity')
        time.sleep(8)
        d.press.enter()
        time.sleep(3)
        d.press.enter()
        time.sleep(3)
        d.press.enter()
        time.sleep(10)
        for _ in range(3):
            d.press.right()
            time.sleep(3)
            d.press.enter()
            time.sleep(3)
            d.press.enter()
            time.sleep(3)
            d.press.enter()
            time.sleep(30)
            d.press.back()
            time.sleep(3)
            d.press.back()
            time.sleep(3)

    def testSettingsNetwork(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        if d(resourceId="android:id/title", text="Network & Internet").exists:
            d.press.down()
            time.sleep(3)
            d.press.enter()
            time.sleep(3)
            for _ in range(10):
                d.press.enter()
                time.sleep(5)
            else:
                d.press.back()
                time.sleep(2)

    def testSettingsgooglecast(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(6):
            d.press.down()
            time.sleep(2)
        d.press.enter()
        time.sleep(2)
        for _ in range(9):
            d.press.down()
            time.sleep(2)
        d.press.enter()
        time.sleep(2)
        for _ in range(2):
            d.press.down()
            time.sleep(2)
            d.press.enter()
            time.sleep(2)
        for _ in range(2):
            d.press.up()
            time.sleep(2)
            d.press.enter()
            time.sleep(2)

    def testSettingsDisplay(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        time.sleep(3)
        d.press.enter()
        time.sleep(3)
        d.press.back()
        time.sleep(3)

    def testSwitchDisplayMode(self): # check resolution one by one
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        d.press.enter()
        sleep(3)
        d.press.down()
        sleep(3)
        for _ in range(4):
            d.press.enter()
            sleep(3)

        d.press.down()
        sleep(2)
        for _ in range(10):
            self.presskey("down", 1)
            sleep(2)
            d.press.enter()
            sleep(5)
            d.press.right()
            sleep(2)
            d.press.enter()
            sleep(3)
        else:
            for _ in range(15):
                d.press.up()
                sleep(1)
        d.press.down()
        sleep(2)
        d.press.enter()
        sleep(5)
        d.press.right()
        sleep(2)
        d.press.enter()
        sleep(3)
        d.press.back()
        sleep(2)

    def testSwitchColorSpace(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        d.press.enter()
        sleep(3)

        d.press.down()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.enter()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.enter()
        sleep(2)

        for j in range(10):
            if j == 0:
                d.press.enter()
                sleep(5)
                d.press.right()
                sleep(2)
                d.press.enter()
                sleep(3)
            else:
                self.presskey("down", 1)
                sleep(3)
                d.press.enter()
                sleep(5)
                d.press.right()
                sleep(2)
                d.press.enter()
                sleep(3)
        else:
            for _ in range(15):
                d.press.up()
                sleep(1)
            d.press.enter()
            sleep(5)
            d.press.right()
            sleep(2)
            d.press.enter()
            sleep(3)
            d.press.back()
            sleep(2)
            d.press.back()
            sleep(2)

    def testScreenPosition(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)
        d(text='Display & Sound').click()
        sleep(3)
        for _ in range(4):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        d.press.down()
        sleep(2)

        for _ in range(20):
            d.press.enter()
            sleep(3)
        d.press.up()
        sleep(2)
        for _ in range(20):
            d.press.enter()
            sleep(3)

        d.press.back()
        sleep(4)
        d.press.back()
        sleep(2)

    def testSettingsSound(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)
        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(10):
            d.press.down()
            sleep(2)
        for _ in range(10):
            d.press.enter()
            sleep(2)
        d.press.back()
        sleep(2)

    def testSettingsSwitchSound(self):# Select Formats & AC4 Dialog Enhancer
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)
        d.press.enter()
        sleep(3)
        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)

        d.press.enter()
        sleep(2)
        for _ in range(2):
            d.press.down()
            sleep(2)
            d.press.enter()
            sleep(2)
        for _ in range(2):
            d.press.up()
            sleep(2)
            d.press.enter()
            sleep(2)
        d.press.back()
        sleep(2)

        d.press.down()
        sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(3):
            d.press.down()
            sleep(2)
            d.press.enter()
            sleep(2)
        for _ in range(3):
            d.press.up()
            sleep(2)
            d.press.enter()
            sleep(2)
        d.press.back()
        sleep(2)

    def testSettingsApps(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)
        for _ in range(4):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(5):
            d.press.enter()
            sleep(5)
            d.press.back()
            sleep(3)
            d.press.down()
            sleep(3)

    def testSettingsScreensaver(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(6):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)

        for _ in range(4):
            d.press.enter()
            sleep(5)
        sleep(5)

        for _ in range(10):
            d.press.down()
            sleep(2)
            d.press.enter()
            sleep(7)
            d.press.back()
            sleep(3)


    def testSettingsStorageandreset(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(5):
            d.press.down()
            sleep(2)
        for _ in range(2):
            d.press.enter()
            sleep(2)
        for _ in range(4):
            d.press.down()
            sleep(2)
        for _ in range(2):
            d.press.enter()
            sleep(2)

    def testSettingsAbout(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        d.press.down()
        sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(10):
            d.press.down()
            sleep(3)
        for _ in range(10):
            d.press.up()
            sleep(3)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)

    def testSettingsDateandtime(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(2):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(5)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)

    def testSettingsLanguage(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(3):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(5)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)

    def testSettingsKeyboard(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(4):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(5)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)


    def testSettingsPowerEnergy(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(2)
        for _ in range(7):
            d.press.down()
            sleep(2)
        d.press.enter()
        sleep(5)

        for i in range(9):
            d.press.enter()
            sleep(1)
            for j in range(1):
                d.press.down()
                sleep(1)
            d.press.enter()
            sleep(1)

        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)

    def testSettingsAccessibility(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(5):
            d.press.down()
            sleep(2)
        for _ in range(2):
            d.press.enter()
            sleep(5)
        d.press.back()
        sleep(3)
        d.press.back()
        sleep(3)

    def testSettingsHDMICEC(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)
        d.press.enter()
        sleep(3)
        d.press.enter()
        sleep(3)
        for _ in range(3):
            d.press.down()
            sleep(3)
            d.press.enter()
            sleep(3)
            d.press.enter()
            sleep(3)
        for _ in range(3):
            d.press.up()
            sleep(3)
            d.press.enter()
            sleep(3)
            d.press.enter()
            sleep(3)
        d.press.back()
        time.sleep(5)
        d.press.back()
        time.sleep(5)
        d.press.back()
        time.sleep(5)

    def testSettingsAddaccessory(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)

        for _ in range(6):
            d.press.down()
            time.sleep(3)
        d.press.enter()
        time.sleep(5)
        d.press.enter()
        time.sleep(15)
        d.press.back()
        time.sleep(3)
        d.press.back()
        time.sleep(3)

    def testSettingsHelpFeedback(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(7):
            d.press.down()
            time.sleep(2)
        d.press.enter()
        time.sleep(5)
        d.press.back()
        time.sleep(5)

    def testSettingsAddAccount(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(3)
        for _ in range(2):
            d.press.down()
            time.sleep(3)

        d.press.enter()
        time.sleep(3)
        d.press.down()
        time.sleep(3)
        d.press.enter()
        time.sleep(10)

        d.press.back()
        time.sleep(5)

    def testMediaPlayer(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        sleep(5)
        for _ in range(4):
            d.press.right()
            time.sleep(3)
        for _ in range(100):
            key = choice(keys)
            getattr(d.press, key)()
            time.sleep(1)