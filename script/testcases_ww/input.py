import unittest
import os
from copy import deepcopy
from random import choice, randint
from time import sleep, time

from script.testcases_ww.common import get_dict_value_in_key
from script.testcases_ww.media_player import MediaPlayer
from script.testcases_ww.params import box_device, mtbf_loop_times, rerun_times, operate_play_time, all_time, \
    not_support_4k_device, youtube_4k_video, live_tv_page, tv_input_id, netflix, video_folder, volume_step_max, \
    live_tv_ok_resource_id, gtv_picture_settings_header_resource_id, radio_button_class, settings_progress_resource_id, \
    not_support_netflix_device
from script.testcases_ww.ui_adb import *

gtv_input_page = 'com.mitv.livetv/.input.SelectInputActivity'
input_list_resource_id = 'com.mitv.livetv:id/scene_transition_common'
input_title_resource_id = 'com.mitv.livetv:id/input_label'
live_tv_list_resource_id = 'com.mitv.livetv:id/shortcut_fragment_item_list'
live_tv_title_resource_id = 'com.mitv.livetv:id/shortcut_list_item_detail_title'

to_input_cmd_prefix = 'am start -a android.intent.action.VIEW -d content://android.media.tv/passthrough/'
tv_input_list = ['TV', 'AV', 'HDMI 1', 'HDMI 2', 'HDMI 3']


class Input(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def open_input(self):
        self.device.open_app(gtv_input_page)

    def get_input_list(self):
        self.device.open_app(gtv_input_page, 'S')
        input_list = self.device.get_list_items(input_list_resource_id, input_title_resource_id)
        self.device.back(3)
        self.assertTrue(input_list != [], 'Input列表为空')
        return input_list

    def enter_input_item(self, item, fuzzy=True):
        self.device.logger.info(f'进入输入源{item}')
        self.open_input()
        to_list_top = self.device.to_list_top(input_list_resource_id)
        self.assertTrue(to_list_top, '没有将光标置于列表最上方')
        find_res = self.device.find_item(input_list_resource_id, input_title_resource_id, item, fuzzy)
        self.assertTrue(find_res, f'没有找到{item}项')
        self.device.center(5)
        if self.device.d(resourceId=live_tv_ok_resource_id, text='OK').exists:
            self.device.center(5)

    def input_enter_picture(self):
        self.device.open_live_tv_settings()
        find_res = self.device.find_item(live_tv_list_resource_id, live_tv_title_resource_id, 'Picture', sub_class=relative_class)
        self.assertTrue(find_res, '未在LiveTV菜单中找到Picture')
        self.device.center(3)

    def test_input_change(self):
        """
        桌面切源
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            for item in self.get_input_list():
                self.enter_input_item(item)
                self.device.home(3)

    def test_input_random_change(self):
        """
        桌面随机切源
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            input_list = self.get_input_list()
            for i in range(10):
                self.enter_input_item(choice(input_list))
                self.device.home(3)

    def test_input_random_change_uat(self):
        """
        随机切源，UAT用例
        """
        input_list = self.get_input_list()
        for i in range(60):
            self.enter_input_item(choice(input_list))
            sleep(30)

    def check_item_exist(self, text, match_type):
        self.open_input()
        if match_type == 'text':
            ele = self.device.d(resourceId=input_title_resource_id, text=text)
        elif match_type == 'match':
            ele = self.device.d(resourceId=input_title_resource_id, textMatches=text)
        elif match_type == 'start':
            ele = self.device.d(resourceId=input_title_resource_id, textStartsWith=text)
        elif match_type == 'contain':
            ele = self.device.d(resourceId=input_title_resource_id, textContains=text)
        else:
            raise TypeError('match_type错误')
        res = True if ele.exists else False
        self.device.back(3)
        return res

    def test_input_item(self, text, match_type, enter_times=3):
        ele_exists = self.check_item_exist(text, match_type)
        if not ele_exists:
            self.skipTest(f'当前设备不支持{text}')
        self.device.center(5)
        for i in range(enter_times):
            self.enter_input_item(text)

    def test_home(self):
        """
        访问Home
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('Home', 'contain')

    def test_tv(self):
        """
        访问TV
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('TV', 'text')

    def test_airplay(self):
        """
        访问AirPlay
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('AirPlay', 'text')

    def test_hdmi_1(self):
        """
        访问HDMI 1
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('HDMI 1', 'contain')

    def test_hdmi_2(self):
        """
        访问HDMI 2
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('HDMI 2', 'contain')

    def test_hdmi_3(self):
        """
        访问HDMI 3
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('HDMI 3', 'contain')

    def test_av(self):
        """
        访问 AV
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('AV', 'text')

    def test_usb(self):
        """
        访问 USB
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_input_item('USB', 'text')

    def test_hdmi_picture(self):
        """
        HDMI源内切换图像模式
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.enter_input_item('HDMI 1')
            self.input_enter_picture()
            # 确定键进入Picture mode
            self.device.center(3)
            picture_mode_len = len(self.device.d(resourceId=settings_title_resource_id))
            self.device.back(3)
            # 遍历图像模式，除Game之外
            for step in range(picture_mode_len):
                self.device.center(3)
                self.device.down(1, times=step)
                focus_item = self.device.get_item_focus(settings_list_1_resource_id, settings_title_resource_id)
                self.device.logger.debug(f'当前光标在： {focus_item}')
                if focus_item.lower() == 'game':
                    self.device.back(3)
                else:
                    self.device.center(5)

            # 最后再遍历Game
            self.device.center()
            find_res = self.device.find_item(settings_list_1_resource_id, settings_title_resource_id, 'Game', True)
            self.assertTrue(find_res, '未找到Game')
            self.device.center(5)
            self.device.down()
            self.device.center()
            self.device.up()
            self.device.center(5)
            self.device.home(3)

    def test_hdmi_backlight(self):
        """
        hdmi源内调节Backlight
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Input列表，故跳过')
        for _ in range(mtbf_loop_times):
            self.enter_input_item('HDMI 2')
            self.input_enter_picture()
            # 下键 -> 确定键进入Backlight
            self.device.down(1)
            self.device.center(1)
            self.device.left(0.3, 30)
            self.device.right(0.3, 30)
            self.device.back(1, 2)
            self.device.home(3)

    def test_global_local_dimming_hdmi(self):
        # 开始播放时间
        start_time = time()
        times = 0

        while True:
            times += 1
            try:
                if times > 1:
                    self.device.logger.info(f'发生异常，再次尝试 {times - 1} times')
                self.device.logger.info(f'{times}.1. 进入HDMI2')
                self.enter_input_item('HDMI 2')
                while time() - start_time < all_time * 60:
                    item_play_time = randint(10, operate_play_time * 60)
                    self.device.global_dimming_play(item_play_time)
            except Exception as e:
                if times - 1 == rerun_times:
                    raise Exception(str(e))
                self.device.logger.exception(e)
            else:
                break

    def test_input_switch_uat_item(self, input_item):
        self.device.logger.info(f'播放{input_item}')
        if input_item in tv_input_list:
            self.enter_input_item(input_item, fuzzy=False)
            sleep(10)
            self.device.google_voice_up_down()
            sleep(10)
            up_step = randint(1, volume_step_max)
            self.device.logger.info(f'调高音量{up_step}')
            self.device.volume_up(up_step)
            sleep(10)
            self.device.logger.info(f'调低音量{volume_step_max}')
            self.device.volume_down(volume_step_max)
        elif input_item == 'Netflix':
            self.device.open_app(netflix, 'S', 20)
            self.device.center(50, 2)
        elif input_item == 'YouTube':
            self.device.open_app(youtube_4k_video, sleep_time=30)
        elif input_item == 'Local':
            mp = MediaPlayer()
            mp.setUpClass()
            mp.setUp()
            mp.enter_folder(video_folder)
            mp.find_media_item('720P_Dolby_AC3', True)
            mp.play_content_pre()

    def test_input_switch_uat(self, input_1, input_2):
        if input_1 in tv_input_list and not self.check_item_exist(input_1, 'text'):
            self.skipTest(f'当前设备不支持{input_1}')
        if input_2 in tv_input_list and not self.check_item_exist(input_2, 'text'):
            self.skipTest(f'当前设备不支持{input_2}')
        for i in range(3):
            self.device.logger.info(f'循环---{i}')
            for input_item in [input_1, input_2]:
                if input_item == 'Netflix' and self.device.build_name in not_support_netflix_device:
                    self.device.logger.info('设备不支持Netflix，故跳过Netflix播放')
                    continue
                self.test_input_switch_uat_item(input_item)
                sleep(60)

    # 同一通路信号源切换 4条
    def test_hdmi_1_to_hdmi_2(self):
        """
        同一通路 HDMI 1 to HDMI 2
        """
        self.test_input_switch_uat('HDMI 1', 'HDMI 2')

    def test_tv_to_av(self):
        """
        同一通路 TV to AV
        """
        self.test_input_switch_uat('TV', 'AV')

    def test_tv_to_local(self):
        """
        同一通路 TV to Local
        """
        self.test_input_switch_uat('TV', 'Local')

    def test_netflix_to_youtube(self):
        """
        同一通路 Netflix to YouTube
        """
        self.test_input_switch_uat('Netflix', 'YouTube')

    # 不同通路信号源切换 7条
    def test_hdmi_1_to_tv(self):
        """
        不同通路 HDMI 1 to TV
        """
        self.test_input_switch_uat('HDMI 1', 'TV')

    def test_hdmi_2_to_local(self):
        """
        不同通路 HDMI 2 to Local
        """
        self.test_input_switch_uat('HDMI 2', 'Local')

    def test_hdmi_1_to_av(self):
        """
        不同通路 HDMI 1 to AV
        """
        self.test_input_switch_uat('HDMI 1', 'AV')

    def test_hdmi_2_to_youtube(self):
        """
        不同通路 HDMI 2 to YouTube
        """
        self.test_input_switch_uat('HDMI 2', 'YouTube')

    def test_tv_to_youtube(self):
        """
        不同通路 TV to YouTube
        """
        self.test_input_switch_uat('TV', 'YouTube')

    def test_av_to_local(self):
        """
        不同通路 HDMI 1 to TV
        """
        self.test_input_switch_uat('AV', 'Local')

    def test_av_to_youtube(self):
        """
        不同通路 AV to YouTube
        """
        self.test_input_switch_uat('AV', 'YouTube')

    def test_picture_settings_traversal_item(self, input_text, match_type):
        self.test_input_item(input_text, match_type, enter_times=1)
        for mode in tv_picture_modes:
            # 切换Picture mode
            i = 0
            while i < rerun_times:
                if not self.device.gtv_tv_change_picture_mode(mode, back=False):
                    self.device.logger.warning(f'未查找到图像设置{mode}')
                    i += 1
                else:
                    break
            self.last_item_list = {'Picture mode'}
            self.last_header = None
            self.picture_settings_traversal()

    # 这个地方本意是想用递归来遍历Picture设置，但是写的有些乱，并且有些问题，后续再改吧
    def picture_settings_traversal(self):
        # 递归终止条件
        #   1 单选按钮
        if self.device.d(className=radio_button_class).exists:
            focus_res = self.device.get_focus_index(settings_list_1_resource_id, radio_button_class, 'checked', True)
            if focus_res != -1:
                cur_item_index, all_index = focus_res
                rand_step = choice([x for x in range(all_index) if x != cur_item_index])
                self.device.down(1, rand_step)
                item_focus = self.device.get_item_focus(settings_list_1_resource_id, settings_title_resource_id)
                self.device.logger.info(f'选择[{item_focus}]')
            self.device.center(3)
            return
        #   2 设置进度条
        elif self.device.d(resourceId=settings_progress_resource_id).exists:
            left_step = randint(1, 10)
            right_step = randint(1, 10)
            self.device.logger.info(f'左{left_step}步，右{right_step}步')
            self.device.left(0.5, left_step)
            self.device.right(0.5, right_step)
            self.device.center(3)
            return
        #   3 切换按钮
        elif self.device.d(className=switch_class).exists:
            self.device.center(3)
            return

        # 如果没有判断到Picture mode设置头 说明菜单已关闭 返回
        if not self.device.d(resourceId=gtv_picture_settings_header_resource_id).exists:
            self.device.logger.info('Picture设置头不存在，说明菜单已关闭，直接return')
            return

        last_item = None
        current_item = self.device.get_item_focus(settings_list_resource_id, settings_title_resource_id, linear_class)
        while last_item != current_item:
            last_item = current_item
            self.device.logger.debug(f'{self.last_item_list=}')
            if current_item is None:
                self.device.logger.info('获取当前聚焦项为None，返回后继续执行')
                self.device.back()
            elif current_item in self.last_item_list:
                self.device.logger.info(f'已设置过[{current_item}]，故跳过')
            elif current_item == 'Reset picture settings':
                self.device.logger.info(f'Reset Picture设置【{current_item}】')
                self.device.center(3)
                self.device.center(15)
                return True
            elif current_item == 'Reset':
                # Reset在列表最下方，可继续往下执行
                self.device.logger.info(f'Reset子设置【{current_item}】')
                self.device.center(3)
                self.device.center(15)
                if self.device.d(resourceId=gtv_picture_settings_header_resource_id).exists:
                    last_item = self.device.d(resourceId=gtv_picture_settings_header_resource_id).get_text()
                self.device.back(3)
            else:
                self.device.logger.info(f'设置【{current_item}】')
                if self.device.d(resourceId=gtv_picture_settings_header_resource_id).exists:
                    self.last_header = self.device.d(resourceId=gtv_picture_settings_header_resource_id).get_text()
                    if self.last_header == 'Picture':
                        self.last_item_list.add(last_item)
                self.device.center(3)
                self.picture_settings_traversal()
            # 如果没有判断到Picture mode设置头 说明菜单已关闭，仅在第一层时才打开继续，其余情况直接返回
            if not self.device.d(resourceId=gtv_picture_settings_header_resource_id).exists and self.last_header == 'Picture':
                self.device.open_app(gtv_picture_setting, 'S', 5)
                last_item = None
            self.device.down()
            current_item = self.device.get_item_focus(settings_list_resource_id, settings_title_resource_id, linear_class)
            self.device.logger.info(f'{last_item=}|{current_item=}')
        if self.device.d(resourceId=gtv_picture_settings_header_resource_id).exists and self.device.d(resourceId=gtv_picture_settings_header_resource_id).get_text() != 'Picture':
            # 仅Picture头不是Picture时才返回
            self.device.back(3)

    # 图像设置遍历测试 4条
    def test_picture_settings_traversal_home(self):
        """
        图像设置遍历测试 桌面
        """
        if self.device.build_name in box_device:
            self.skipTest('仅TV支持此用例')
        self.test_picture_settings_traversal_item('Home', 'contain')

    def test_picture_settings_traversal_tv(self):
        """
        图像设置遍历测试 TV源
        """
        if self.device.build_name in box_device:
            self.skipTest('仅TV支持此用例')
        self.test_picture_settings_traversal_item('TV', 'text')

    def test_picture_settings_traversal_hdmi(self):
        """
        图像设置遍历测试 HDMI源
        """
        if self.device.build_name in box_device:
            self.skipTest('仅TV支持此用例')
        self.test_picture_settings_traversal_item('HDMI 2', 'contain')

    def test_picture_settings_traversal_av(self):
        """
        图像设置遍历测试 AV源
        """
        if self.device.build_name in box_device:
            self.skipTest('仅TV支持此用例')
        self.test_picture_settings_traversal_item('AV', 'text')

class InputWithoutSetup(Input):
    def setUp(self):
        pass

    def test_hdmi_long_play_uat(self):
        """
        HDMI源长播
        """
        if self.device.build_name not in box_device:
            # 先判断是否在HDMI2源中，若不在则进入，否则直接播放
            cur_page = self.device.get_page()
            cur_input = self.device.get_input_id()
            if cur_page in live_tv_page and cur_input == get_dict_value_in_key(tv_input_id['hdmi_2'], self.device.build_name):
                self.device.logger.info('当前在HDMI 2源中')
            else:
                self.enter_input_item('HDMI 2')
            self.device.long_play_uat('HDMI')
        else:
            resolution_steps = 6 if self.device.build_name in not_support_4k_device else 8
            self.device.logger.info(f'设备{"不" if self.device.build_name in not_support_4k_device else ""}支持4k，故分辨率菜单有{resolution_steps}项')
            for i in range(resolution_steps):
                self.device.logger.info('播放YouTube 4k视频')
                self.device.open_app(youtube_4k_video, sleep_time=30)
                self.device.logger.info('恢复分辨率为Auto')
                self.device.center(3)
                self.device.up(2)
                self.device.right(1, 8)
                self.device.center(3, 2)
                self.device.up(1, resolution_steps)
                self.device.center(5)
                self.device.back(10)
                # 等待YouTube菜单消失后继续
                self.device.logger.info('修改分辨率')
                self.device.center(3)
                self.device.up(2)
                self.device.right(1, 8)
                self.device.center(3, 2)
                self.device.down(2, i + 1)
                self.device.center(5)
                self.device.back(3)
                self.device.logger.info('修改分辨率后播放10分钟')
                sleep(600)

    def test_av_long_play_uat(self):
        """
        AV源长播
        """
        # 先判断是否在AV源中，若不在则进入，否则直接播放
        cur_page = self.device.get_page()
        cur_input = self.device.get_input_id()
        if cur_page in live_tv_page and cur_input == get_dict_value_in_key(tv_input_id['av'], self.device.build_name):
            self.device.logger.info('当前在AV源中')
        else:
            self.enter_input_item('AV')
        self.device.long_play_uat('AV')

    def test_hdmi_refresh_in_out(self):
        """
        最高/次高刷新率 HDMI源进出与系统交互压力测试
        """
        # 开始播放时间
        start_time = time()
        times = 0

        while True:
            times += 1
            try:
                if times > 1:
                    self.device.logger.info(f'发生异常，再次尝试 {times - 1} times')
                while time() - start_time < all_time * 60:
                    item_play_time = randint(10, operate_play_time * 60)
                    self.device.home(10)
                    self.enter_input_item('HDMI 2')
                    sleep(10)
                    self.device.logger.info(f'阶段播放{item_play_time}秒')
                    self.device.random_picture_format()
                    sleep(item_play_time // 3)
                    up_step = randint(1, volume_step_max)
                    self.device.logger.info(f'调高音量{up_step}')
                    self.device.volume_up(up_step)
                    sleep(item_play_time // 3)
                    self.device.logger.info(f'调低音量{volume_step_max}')
                    self.device.volume_down(volume_step_max)
                    sleep(item_play_time // 3)
            except Exception as e:
                if times - 1 == rerun_times:
                    raise Exception(str(e))
                self.device.logger.exception(e)
            else:
                break
