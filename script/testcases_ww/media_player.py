from time import sleep, time
import os
import unittest

from script.testcases_ww.params import *
from script.testcases_ww.ui_adb import *
from script.testcases_ww.common import keyword_match
from script.testcases_ww.ui_adb import UiADB


# Media Player主界面上第一个U盘图标的resource_id
device_logo_0 = 'com.xiaomi.mitv.mediaexplorer:id/devices_logo_root'
# u盘图标的resource_id
device_name = 'com.xiaomi.mitv.mediaexplorer:id/device_name'
# 视频不支持时提示语所在的resource_id
video_not_support = 'com.mitv.videoplayer:id/error_tips'
relative_class = 'android.widget.RelativeLayout'
# 文件列表中文件名称的resource_id
title_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/item_title'
# 文件列表中光标当前所在的resource_id
focus_item_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/item_root'
empty_tip = 'com.xiaomi.mitv.mediaexplorer:id/no_file_layout'
video = 'com.mitv.videoplayer/com.mitv.videoplayer.VideoPlayerActivity'
music = 'com.xiaomi.mimusic2/com.xiaomi.mimusic2.localplayer.LocalPlayerActivity'
photo = 'com.mitv.gallery/com.mitv.gallery.activity.PhotoActivity'
media_player = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'
explorer = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.explorer.ExplorerActivity'
permission_page = 'com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity'
media_tab_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/tab_title'
media_empty_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/empty_content'
music_settings_list_resource_id = 'com.xiaomi.mimusic2:id/menu_items_container'
music_title_list_resource_id = 'com.xiaomi.mimusic2:id/menu_item_name'
video_menu_list_resource_id = 'com.mitv.videoplayer:id/menu_ct'
video_menu_title_resource_id = 'com.mitv.videoplayer:id/menu_txt'
media_play_menu_list_resource_id = 'com.mitv.videoplayer:id/menu_ct'
media_play_menu_title_resource_id = 'com.mitv.videoplayer:id/menu_txt'

# media_device_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/dev'
# media_video_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/video'
# media_image_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/image'
# media_music_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/music'
# main_video_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/video_icon'
# main_image_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/album_item_img'
# main_music_list_resource_id = 'com.xiaomi.mitv.mediaexplorer:id/all_music_view'


class MediaPlayer(unittest.TestCase):
    # 设置循环标志
    set_repeat_flag = False

    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def launch_media_player(self):
        self.device.logger.info('打开Media player')
        self.device.open_app(media_player, 'S', 5)
        self.assertTrue(self.device.d(resourceId=media_tab_resource_id).exists, '打开Media player失败')

    def enter_media_root(self, index):
        self.launch_media_player()
        self.device.up(3)
        self.device.left(3, 3)
        self.device.right(2, index)

    def enter_media_tab(self, index):
        self.enter_media_root(index)
        sleep(10)
        if self.device.d(resourceId=media_empty_resource_id).exists:
            self.device.logger.info('Tab下没有资源')
            return False
        else:
            return True

    def test_launch_media_player(self):
        """
        打开退出Media player
        """
        for _ in range(mtbf_loop_times):
            self.launch_media_player()
            self.device.logger.info('退出Media player')
            self.device.back(2, 2)
            self.device.home(3)

    def test_switch_video_player_tab(self):
        """
        Media player tab切换
        """
        for _ in range(mtbf_loop_times):
            self.launch_media_player()
            self.device.up(3)
            self.device.right(5, 3)
            self.device.left(5, 3)

    def test_play_video(self):
        """
        播放视频文件
        """
        for _ in range(mtbf_loop_times):
            if self.enter_media_tab(1):
                for i in range(5):
                    step = i // 2 + 1
                    self.device.down(2, step)
                    if i % 2:
                        self.device.left(2, step)
                    else:
                        self.device.right(2, step)
                    self.device.center(3)
                    self.device.before_play_video_check()
                    if self.device.get_page() == video_play_page:
                        self.device.logger.info('正在播放视频')
                        sleep(60)
                        # 仅仅在播放视频时才返回，否则就说明已经播放完毕了 无需返回
                        if self.device.get_page() == video_play_page:
                            self.device.logger.info('退出播放')
                            self.device.back(0.3, 2)

    def test_play_picture(self):
        """
        浏览图片
        """
        for _ in range(mtbf_loop_times):
            if self.enter_media_tab(2):
                self.device.down(3)
                for i in range(3):
                    if i != 0:
                        self.device.right(2)
                    self.device.center(3, 3)
                    self.device.right(3, 10)
                    self.device.back(2, 2)

    def test_play_music(self):
        """
        播放音乐-切换声音模式
        """
        for _ in range(mtbf_loop_times):
            if self.enter_media_tab(3):
                self.device.down(3)
                self.device.center(5)
                self.device.left(3)
                self.assertTrue(self.device.find_item(music_settings_list_resource_id, music_title_list_resource_id, 'Sound', True, relative_class), '未找到Sound settings')
                self.device.change_sound_mode()

    def test_media_player_picture_mode(self):
        """
        本地视频-切换图像模式
        """
        for _ in range(mtbf_loop_times):
            if self.enter_media_tab(1):
                self.device.down(2, 3)
                self.device.center(3)
                self.device.before_play_video_check()
                if self.device.get_page() == video_play_page:
                    self.device.logger.info('正在播放视频')
                    self.device.down(1, 3)
                    self.assertTrue(
                        self.device.find_item(video_menu_list_resource_id, video_menu_title_resource_id, 'picture', True, ori='h'),
                        '未找到Picture')
                    # 第一个center用于点击Picture，第二个用于进入Picture菜单内
                    self.device.center(3, 2)
                    picture_mode_len = len(self.device.d(resourceId=settings_title_resource_id))
                    self.device.back(3)
                    # 遍历图像模式，除Game之外
                    for step in range(picture_mode_len):
                        self.device.center(3)
                        self.device.down(1, times=step)
                        self.device.center(3)
                    self.device.back(2, 3)

    def test_media_player_sound_mode(self):
        """
        本地视频-切换声音模式
        """
        for _ in range(mtbf_loop_times):
            if self.enter_media_tab(1):
                self.device.down(2, 3)
                self.device.center(3)
                self.device.before_play_video_check()
                if self.device.get_page() == video_play_page:
                    self.device.logger.info('正在播放视频')
                    self.device.down(2)
                    self.assertTrue(
                        self.device.find_item(video_menu_list_resource_id, video_menu_title_resource_id, 'sound', True, ori='h'),
                        '未找到Sound')
                    self.device.center(3)
                    self.device.change_sound_mode()

    def test_audio_aac(self):
        """
        Audio稳定性 - AAC
        """
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.video_play('1080P_AAC')

    def test_audio_dolby_ac3(self):
        """
        Audio稳定性 - Dolby AC3
        """
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.video_play('720P_Dolby_AC3')

    def test_audio_dolby_eac3(self):
        """
        Audio稳定性 - Dolby AC3
        """
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.video_play('1080P_Dolby_EAC3')

    def test_audio_dolby_atmos(self):
        """
        Audio稳定性 - Dolby Atmos
        """
        # 需要先切换音效模式
        if self.device.build_name in not_support_dolby_vision_device:
            self.skipTest('此设备不支持Dolby Vision，故跳过')
        if self.device.build_name in not_support_4k_device:
            self.skipTest('此设备不支持4k，故跳过')
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.video_play('DolbyVision_4K_EAC3_Atmos')

    def test_audio_dts(self):
        """
        Video稳定性 - DTS
        """
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.video_play('1080P_DTS_Dolby_AC3')

    def test_video_4k(self):
        """
        Video稳定性 - 4K
        """
        if self.device.build_name in not_support_4k_device:
            self.skipTest('此设备不支持4K，故跳过')
        self.video_play('4K_AAC')

    def test_video_1080p(self):
        """
        Video稳定性 - 1080P
        """
        self.video_play('1080P_Dolby_AC3')

    def test_video_720p(self):
        """
        Video稳定性 - 720P
        """
        self.video_play('720P_Dolby_AC3')

    def test_video_dolby_vision(self):
        """
        Video稳定性 - Dolby Vision 4k
        """
        if self.device.build_name in not_support_dolby_vision_device:
            self.skipTest('此设备不支持Dolby Vision，故跳过')
        if self.device.build_name in not_support_4k_device:
            self.skipTest('此设备不支持4k，故跳过')
        self.video_play('DolbyVision_4K_EAC3_Atmos')

    def test_video_hdr_1080p(self):
        """
        Video稳定性 - HDR_1080p
        """
        if self.device.build_name in not_support_hdr_device:
            self.skipTest('此设备不支持HDR，故跳过')
        self.video_play('HDR_1080P_AAC')

    def test_video_hdr_4k(self):
        """
        Video稳定性 - HDR_4k
        """
        if self.device.build_name in not_support_hdr_device:
            self.skipTest('此设备不支持HDR，故跳过')
        if self.device.build_name in not_support_4k_device:
            self.skipTest('此设备不支持4k，故跳过')
        self.video_play('HDR_4K_AAC')

    def test_video_hdr10_plus_4k(self):
        """
        Video稳定性 - HDR10+
        """
        if self.device.build_name in not_support_hdr10_plus_device:
            self.skipTest('此设备不支持HDR10+，故跳过')
        if self.device.build_name in not_support_4k_device:
            self.skipTest('此设备不支持4k，故跳过')
        self.video_play('HDR10+_4K_AAC')

    def test_global_local_dimming_50(self):
        """
        Global/Local Dimming稳定性测试
        """
        self.video_play('720P_50FPS', 'global_dimming')

    def test_global_local_dimming_60(self):
        """
        Global/Local Dimming稳定性测试
        """
        self.video_play('720P_Dolby_AC3', 'global_dimming')

    def get_now_dir(self):
        """
        通过在文件夹列表中返回的方式获取当前目录
        @return: 当前所在文件夹
        """
        self.back_to_explorer()
        self.device.back(3)
        cur_page = self.get_media_page()
        if cur_page == 'explorer':
            root_dir = self.get_focused_text()
        elif cur_page == 'media_player':
            root_dir = 'u_disk_root'
        else:
            return False
        self.device.center(3)
        return root_dir

    def enter_folder(self, root):
        """
        当处于MediaPlayer主页面时，进入指定U盘的指定目录
        @return:
        """
        if self.get_now_dir() == root:
            return True
        self.device.home(3)
        self.enter_media_root(0)
        if not self.device.d(resourceId=device_name).exists:
            raise Exception('请检查U盘是否插入')
        self.device.down(3)
        self.device.center(5)
        find_res = self.find_folder_item('\\', root)
        if find_res:
            self.device.logger.info(f'当前光标已在 [{root}] 上')
            self.device.center(3)
            self.device.logger.info(f'进入 [{root}] 中')
            return True
        else:
            raise Exception(f'该U盘中无此文件夹: {root}')

    def find_folder_item(self, root, key, layer=0):
        """
        广度优先查找文件
        @param root:当前所在目录
        @param key: 要查找的文件
        @param layer: 层级，用于规范格式
        @return: 返回查找结果，若查找到返回True，没查找到返回False
        """
        layer += 1
        self.device.logger.info(f'{"--" * layer}进入到目录 [{root}] 中查找')
        root_is_empty = self.check_dir_empty()
        if root_is_empty:
            self.device.logger.info(f'{"--" * layer}该目录为空: {root}')
            self.device.back()
            return False
        else:
            root_first_file = None
            item_folder_num = 0
            while True:
                item_type = self.check_item_type()
                if item_type == 'dir':
                    item_folder_num += 1
                item_text = self.get_focused_text()
                self.device.logger.info(f'{"--" * layer}当前光标所在: {item_text}')
                if item_text == key:
                    self.device.logger.info(f'{"--" * layer}已找到该文件')
                    return True
                if item_text == root_first_file:
                    self.device.logger.info(f'{"--" * layer}目录 [{root}] 中没有要查找的文件: {key}, 接下来查找子文件夹')
                    break
                if not root_first_file:
                    root_first_file = item_text
                self.device.down()
            if item_folder_num != 0:
                root_first_file = None
                while True:
                    item_type = self.check_item_type()
                    item_text = self.get_focused_text()
                    self.device.logger.info(f'{"--" * layer}当前光标所在: {item_text}')
                    if item_text == root_first_file:
                        self.device.logger.info(f'{"--" * layer}目录 [{root}] 中没有要查找的文件: {key}, 返回到上一层目录')
                        self.device.back()
                        return False
                    if not root_first_file:
                        root_first_file = item_text
                    if item_type == 'dir':
                        self.device.center(3)
                        res = self.find_folder_item(item_text, key, layer)
                        if res:
                            return True
                        else:
                            self.device.down()
                    else:
                        self.device.down()
            else:
                self.device.logger.info(f'当前目录中无文件夹，直接返回')
                self.device.back()
                return False

    def check_support(self):
        """
        检查正在播放的视频是否支持，通过判断提示不支持的字段是否存在得到结果
        @return: 若提示视频不支持返回False，若正常播放，返回True
        """
        page = self.get_media_page()
        self.device.logger.debug(f'当前页面为: {page}')
        if page == 'video':
            sleep(5)
            res = not self.device.d(resourceId=video_not_support).exists
            self.device.logger.debug(f'{page}是否支持: {res}')
        elif page in ['music', 'photo']:
            toast_content = self.device.d.toast.get_message(2, 2, None)
            res = False if toast_content else True
            self.device.logger.debug(f'{page}是否支持: {res}')
        elif page == 'permission':
            self.device.center()
            self.device.logger.debug(f'在权限申请页面按下确认键，即点击Allow')
            # 权限申请页面后再次调用判断是否支持
            res = self.check_support()
        else:
            res = True
        return res

    def get_media_page(self):
        """
        获取当前页面包名/活动，并判断类型
        @return: video、photo、music、media_player、explorer、其它
        """
        curr_page = self.device.get_page()
        page_type = curr_page
        if curr_page == video:
            page_type = 'video'
        elif curr_page == photo:
            page_type = 'photo'
        elif curr_page == music:
            page_type = 'music'
        elif curr_page == media_player:
            page_type = 'media_player'
        elif curr_page == explorer:
            page_type = 'explorer'
        elif curr_page == permission_page:
            page_type = 'permission'
        return page_type

    def get_list_text(self):
        """
        获取MediaPlayer中当前页面上的文件及文件夹列表，依赖于self.get_focused_text()
        @return: 文件夹列表
        """
        is_empty = self.check_dir_empty()
        if not is_empty:
            text_list = []
            while True:
                item_text = self.get_focused_text()
                if item_text in text_list:
                    break
                text_list.append(item_text)
                self.device.down()
            return text_list

    def check_dir_empty(self):
        """
        检查当前文件夹是否为空，通过判断有无resourceId 'com.xiaomi.mitv.mediaexplorer:id/no_file_layout'
        @return: 文件夹为空返回True，不为空返回False
        """
        is_empty = self.device.d(resourceId=empty_tip).exists
        return is_empty

    def find_focused_item(self):
        """
        在当前文件列表中查找焦点所在
        @return: 返回焦点所在的列表
        """
        item_root = self.device.d.xpath(f'@{focus_item_resource_id}').all()
        for ii, item in enumerate(item_root):
            focused = item.info.get('focused')
            if focused == 'true' or focused is True:
                focus_item = self.device.d(resourceId=focus_item_resource_id)[ii]
                return focus_item

    def get_focused_item(self):
        """
        在当前文件列表中查找焦点所在行
        若无焦点，则按下Center键后再查找，若还是无，则报错
        """
        focus_item = self.find_focused_item()
        if focus_item:
            return focus_item
        else:
            self.device.logger.debug('当前无焦点，按下Center键后再查找')
            self.device.center()
            item_focus = self.find_focused_item()
            if item_focus:
                return item_focus
            else:
                raise Exception('当前未在文件列表中')

    def get_focused_text(self):
        """
        获取在文件列表中当前光标所在文件的名称
        @return: 若光标不在文件列表中，则返回None，否则返回当前高亮的文件名称
        """
        focused_item = self.get_focused_item()
        return focused_item.sibling().child(resourceId=title_resource_id).get_text()

    def check_item_type(self):
        """
        获取文件列表中光标所在文件的类型，通过界面右侧文件信息是否含有Size去判断
        @return:文件夹、文件
        """
        focused_item = self.get_focused_item()
        img_num = focused_item.child(className=relative_class).info.get('childCount')
        return 'dir' if img_num == 2 else 'file'

    def find_media_item(self, keyword, fuzzy=False):
        self.device.logger.debug(f'查找关键字：{keyword}')
        find_res = False
        item_now = self.get_focused_text()
        if keyword_match(keyword, item_now, fuzzy):
            find_res = True

        if not find_res:
            item_last = None
            while item_last != item_now:
                item_last = item_now
                if keyword_match(keyword, item_now, fuzzy):
                    find_res = True
                    break
                self.device.up()
                item_now = self.get_focused_text()
            else:
                self.device.logger.debug('当前在列表最上方，开始下翻')

            if not find_res:
                item_last = None
                while item_last != item_now:
                    item_last = item_now
                    if keyword_match(keyword, item_now, fuzzy):
                        find_res = True
                        break
                    self.device.down()
                    item_now = self.get_focused_text()
        if find_res:
            self.device.logger.info(f'查找到关键字：{keyword}')
            self.device.center(5)
        return find_res

    def play_list_content(self, now_dir, result_path, loop_time, play_time, layer=0):
        """
        播放该目录下的所有内容，并遍历子文件夹
        @param now_dir: 当前目录
        @param result_path: 当前路径
        @param layer: 目录深度，用于格式化log
        @param loop_time: 循环次数
        @param play_time: 播放时间
        @return:
        """
        self.device.logger.info('=' * 50)
        result_path = result_path + '\\' + now_dir
        layer += 1
        self.device.logger.info(f'{"--" * layer}进入文件夹 [{now_dir}]')
        is_empty = self.check_dir_empty()
        if is_empty:
            self.device.logger.info(f'{"--" * layer}当前目录为空')
            self.device.back()
        else:
            item_first_file = None
            while True:
                item_page = self.get_media_page()
                if item_page != 'explorer':
                    raise Exception('当前页面不是文件列表')
                item_txt = self.get_focused_text()
                if item_txt == item_first_file:
                    self.device.logger.info(f'{"--" * layer}当前文件夹 [{now_dir}] 已遍历一遍结束')
                    return
                if not item_first_file:
                    item_first_file = item_txt

                self.device.logger.info(f'{"--" * layer}当前光标所在 [{item_txt}]')
                item_type = self.check_item_type()
                if item_type == 'dir':
                    item_folder = self.get_focused_text()
                    self.device.center(3)
                    self.play_list_content(item_folder, result_path, loop_time, layer)
                    self.device.back()
                    self.device.down()
                else:
                    if item_txt.split('.')[-1] == 'apk':
                        self.device.logger.debug(f'{"--" * layer}当前文件 [{item_txt}] 为安装文件，跳过')
                    else:
                        self.device.center()
                        support = self.check_support()
                        if support:
                            sleep(play_time)
                        self.play_next()

    def back_to_explorer(self):
        curr_page = self.get_media_page()
        if curr_page in ['video', 'music']:  # 当前页面为视频播放或音乐时，需要两次返回
            self.device.back(0.3)
            self.device.back(3)
        elif curr_page == 'photo':
            self.device.back(3)

    def set_repeat_one(self, turn):
        self.device.down(2, 2)
        if self.device.find_item(media_play_menu_list_resource_id, media_play_menu_title_resource_id, 'Repeat', False, ori='h'):
            self.device.center(3)
            if turn:
                self.device.right(2)
            else:
                self.device.left(2)
            self.device.center(10)
            self.set_repeat_flag = True

    def play_next(self):
        """
        在播放状态返回到文件列表后光标下移，并判断当前页面
        @return:
        """
        self.back_to_explorer()
        curr_page = self.get_media_page()

        if curr_page == 'explorer':
            self.device.down()

    def seek(self, direction='back', step=5):
        """
        快进快退
        @params direction: 快进还是快退
        @params step: 快进或快退的步长
        @params sleep_time:
        """
        if direction == 'back':
            self.device.left(0, step)
        elif direction == 'forward':
            self.device.right(0, step)

    def play_content_pre(self):
        """
        每一次播放的前置操作
        """
        self.device.before_play_video_check()
        if self.get_media_page() != 'video':
            raise Exception('当前页面不是视频播放')
        if not self.check_support():
            self.fail('视频播放失败：显示不支持')
        if not self.set_repeat_flag:
            self.set_repeat_one(True)

    def video_play(self, item_video, play_type='stability'):
        """
        Video播放
        """
        # 开始播放时间
        start_time = time()
        times = 0

        while True:
            times += 1
            try:
                if times > 1:
                    self.device.logger.info(f'发生异常，再次尝试 {times - 1} times')
                self.device.logger.info(f'{times}.1. 进入目录:{video_folder}')
                self.enter_folder(video_folder)
                self.find_media_item(item_video, True)

                self.device.logger.info(f'{times}.2. 播放视频: {item_video} 并执行')
                while time() - start_time < all_time * 60:
                    self.play_content_pre()
                    item_play_time = randint(10, operate_play_time * 60)
                    if play_type == 'stability':
                        self.stability_play(item_play_time)
                    elif play_type == 'global_dimming':
                        self.device.global_dimming_play(item_play_time)
                    else:
                        raise Exception('播放类型错误')
                self.device.logger.info(f'{times}.3. 播放成功')
            except Exception as e:
                if times - 1 == rerun_times:
                    raise Exception(str(e))
                self.device.logger.exception(e)
            else:
                break
    
    def stability_play(self, item_play_time):
        """
        视频稳定性播放
        """
        item_operate = choice(play_operates)
        self.device.logger.info(
            f'阶段播放时间{item_play_time // 2}s后执行操作{item_operate}后再播放{item_play_time // 2}s')
        sleep(item_play_time // 2)
        if item_operate == 'pause-resume':
            self.device.center(3)
            pause_time = randint(1, 20)
            self.device.logger.info(f'暂停播放{pause_time}s')
            sleep(pause_time)
            self.device.center(3)
        elif item_operate == 'seek-back':
            back_step = randint(1, 20)
            self.device.logger.info(f'跳转至前{back_step}秒')
            self.seek('back', back_step)
        elif item_operate == 'seek-forward':
            forward_step = randint(1, 20)
            self.device.logger.info(f'跳转至后{forward_step}秒')
            self.seek('forward', forward_step)
        elif item_operate == 'volume-up_down':
            # 这里调高音量弄少一些，防止声音太大了
            up_step = randint(1, volume_step_max)
            self.device.logger.info(f'调高音量{up_step}')
            self.device.volume_up(up_step)
            sleep(item_play_time // 2)
            self.device.logger.info(f'调低音量{volume_step_max}')
            self.device.volume_down(volume_step_max)
        elif item_operate == 'ffga':
            self.device.google_voice_up_down(True)
        sleep(item_play_time // 2)
