import os
import unittest

from script.testcases_ww.params import mtbf_loop_times
from script.testcases_ww.ui_adb import *

gallery_activity = 'com.mitv.gallery/.activity.HomePageActivity'
no_data_resource_id = 'com.mitv.gallery:id/no_data_view'
img_play_btn_resource_id = 'com.mitv.gallery:id/album_slide_show_btn'
theme_list_resource_id = 'com.mitv.gallery:id/slide_show_theme_list'
video_icon_resource_id = 'com.mitv.gallery:id/album_indicator'
gallery_main_item_resource_id = 'com.mitv.gallery:id/album_item_img'
gallery_sub_item_resource_id = 'com.mitv.gallery:id/photo_item_img'


class Gallery(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def launch_gallery(self):
        self.device.logger.info('打开Gallery')
        self.device.open_app(gallery_activity, 'S')
        self.assertTrue(self.device.d(text='All').exists, 'TAB栏中All不存在')

    def test_launch_gallery(self):
        """
        打开退出Gallery
        """
        for _ in range(mtbf_loop_times):
            self.launch_gallery()
            self.device.logger.info('退出Gallery')
            self.device.back(2, 2)

    def test_scan_picture(self):
        """
        浏览图片
        """
        for _ in range(mtbf_loop_times):
            self.launch_gallery()
            if self.device.d(resourceId=no_data_resource_id).exists:
                self.device.logger.info('No data here')
            else:
                self.device.logger.info('进入U盘')
                self.device.down(2)
                self.device.center(2)
                if self.device.d(resourceId=img_play_btn_resource_id).exists:
                    self.device.up(2, 2)
                    self.device.center(2)
                    self.assertTrue(self.device.d(resourceId=theme_list_resource_id).exists, 'theme effect 不存在')
                    self.device.right(2, 4)
                    self.device.center(60 * 5)
                    self.device.back(2, 2)
                else:
                    self.device.logger.info('播放按钮不存在')

    def test_scan_video(self):
        """
        浏览视频
        """
        for _ in range(mtbf_loop_times):
            self.launch_gallery()
            if self.device.d(resourceId=no_data_resource_id).exists:
                self.device.logger.info('No data here')
            else:
                self.device.logger.info('进入U盘')
                self.device.down(2)
                main_items = self.device.d(resourceId=gallery_main_item_resource_id)
                self.device.down(2, len(main_items))
                self.device.right(2, len(main_items))
                self.device.center(2)
                sub_items = self.device.d(resourceId=gallery_sub_item_resource_id)
                if sub_items.exists:
                    self.device.down(3)
                    for i in range(len(sub_items)):
                        self.device.center(3)
                        self.device.before_play_video_check()
                        if self.device.get_page() == video_play_page:
                            self.device.logger.info('正在播放视频')
                            sleep(60)
                            break
                        else:
                            self.device.back(2)
                            self.device.right(2)
                else:
                    self.device.logger.info('未进入Folder')
