# 不支持4K的设备
not_support_4k_device = ['arctictale', 'furiosa', 'watchmen', 'coco']
# 不支持Dolby Vision的设备
not_support_dolby_vision_device = ['arctictale', 'furiosa', 'watchmen', 'coco']
# 不支持HDR的设备
not_support_hdr_device = []
# 不支持HDR10+的设备
not_support_hdr10_plus_device = ['arctictale', 'furiosa', 'watchmen', 'coco']
# 不支持Netflix
not_support_netflix_device = ['stepup', 'lalaland', 'furiosa']
# 盒子设备
box_device = ['twilight', 'jaws', 'adastra', 'coco']
# 播放操作：反复起播、暂停、恢复、seek、调音量
play_operates = ['pause-resume', 'seek-back', 'seek-forward', 'volume-up_down', 'ffga']
# 播放目录
video_folder = 'OVERSEASUAT'
# 单个视频播放时间/分钟
all_time = 15
# 执行操作时每次播放的最大时间 分钟，取随机值/分钟
operate_play_time = 3
# 重新执行次数
rerun_times = 3
# MTBF case循环次数
mtbf_loop_times = 2
# 音量调大上限（及调小次数）
volume_step_max = 5

# ob1音效模式列表，后续如果有多个项目时可以改为项目字典格式处理
box_sound_format_list = ['Automatic', 'PCM', 'Passthrough']
# TV声音模式列表
tv_sound_modes = ['Standard', 'Movie', 'News', 'Game']
# TV图像模式列表
tv_picture_modes = ['Standard', 'Vivid', 'Movie', 'Sport', 'Game', 'Monitor']
# 盒子分辨率列表，音效模式列表
box_resolution_list = [
    'Automatic', '4k (60 Hz)', '4k (59.94 Hz)', '4k (50 Hz)', '4k (30 Hz)', '4k (29.97 Hz)', '4k (25 Hz)', '4k (24 Hz)',
    '4k (23.98 Hz)', '1080p (60 Hz)', '1080p (59.94 Hz)', '1080p (50 Hz)', '1080p (30 Hz)', '1080p (29.97 Hz)',
    '1080p (25 Hz)', '1080p (24 Hz)', '1080p (23.98 Hz)', '720p (60 Hz)', '720p (59.94 Hz)', '720p (50 Hz)'
]

# 应用
youtube = 'com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.ShellActivity'
# Netflix
netflix = 'com.netflix.ninja/.MainActivity'
# YouTube播放4k视频
youtube_4k_video = 'am start -W -a android.intent.action.VIEW -d ""https://www.youtube.com/watch?v=K1QICrgxTjA"" -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.ShellActivity'

# Sound Mode字典
sound_settings_dict = {
    'watchmen': 'com.android.tv.settings/com.xiaomi.gtv.settings.sound.MiSoundActivity',
    'default': 'com.android.tv.settings/com.xiaomi.gtv.settings.display.sound.MiSoundActivity'
}

# Picture Mode字典
picture_setting_dict = {
    'watchmen': 'com.android.tv.settings/com.xiaomi.gtv.settings.picture.MiPictureActivity',
    'default': 'com.android.tv.settings/com.xiaomi.gtv.settings.display.picture.MiPictureActivity'
}
# 截图默认目录
screen_cap_folder = '/sdcard/screencaps'
live_tv_ok_resource_id = 'com.mitv.livetv:id/ok'
# 设置的进度条
settings_progress_resource_id = 'com.android.tv.settings:id/dialog_progress_view'
# GTV Picture设置头
gtv_picture_settings_header_resource_id = 'com.android.tv.settings:id/decor_title'
# TV Input ID
tv_input_id = {
    'hdmi_2': {
        'arctictale': 'com.droidlogic.tvinput/.services.Hdmi2InputService/HW6'
    },
    'av': {
        'arctictale': 'com.droidlogic.tvinput/.services.AV1InputService/HW1'
    }
}
# 查看当前源的Input id
check_input_id_cmd = "dumpsys tv_input | grep -A 10 sessionStateMap | grep inputId | awk '{print $2}'"
# LiveTV包名/活动名
live_tv_page = [
    'com.mitv.livetv/com.mitv.livetv.tv.MainActivity'
]
# Radio Button class
radio_button_class = 'android.widget.RadioButton'
