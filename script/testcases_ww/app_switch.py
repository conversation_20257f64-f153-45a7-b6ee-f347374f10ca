import unittest
import os
from time import sleep

from script.testcases_ww.params import box_device, mtbf_loop_times
from script.testcases_ww.simple_adb import google_voice_page
from script.testcases_ww.ui_adb import UiADB

youtube_page = 'com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.MainActivity'
media_player = 'com.xiaomi.mitv.mediaexplorer/.NewScraperMainEntryActivity'
play_games = 'com.google.android.play.games/com.google.android.apps.play.games.app.atv.features.home.HomeActivity'
netflix = 'com.netflix.ninja/.MainActivity'
patch_wall = 'com.mitv.tvhome.atv/.app.PatchWallActivity'
playstore = 'com.android.vending/com.google.android.finsky.unauthenticated.activity.UnauthenticatedMainActivity'
to_tv = 'am start -a android.intent.action.VIEW -d content://android.media.tv/channel'
tv_manager = 'com.xiaomi.mitv.tvmanager/com.xiaomi.mitv.tvmanager.MainTvManagerActivity'

dashboard_list_resource_id = 'com.google.android.apps.tv.launcherx:id/dashboard_general_recycler_view'
dashboard_title_resource_id = 'com.google.android.apps.tv.launcherx:id/dashboard_tile_text'


class AppSwitch(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def test_app_switch(self):
        """
        应用切换
        """
        for _ in range(mtbf_loop_times):
            self.device.logger.info(f'打开退出YouTube')
            self.device.open_app(youtube_page, 'S', 10)
            self.device.center(10)
            self.device.home(3)

            if self.device.build_name not in box_device:
                self.device.logger.info(f'打开退出LiveTV')
                self.device.open_app(to_tv, sleep_time=10)
                self.device.home(3)

                self.device.logger.info(f'打开退出TV Manager')
                self.device.open_app(tv_manager, 'S',10)
                self.device.center(10)
                self.device.home(3)

            self.device.logger.info(f'打开退出PlayGames')
            self.device.open_app(play_games, 'S', 10)
            self.device.center(5)
            self.device.home(3)

            self.device.logger.info(f'打开退出PlayStore')
            self.device.open_app(playstore, 'S', 10)
            self.device.center(5)
            self.device.home(3)

            self.device.logger.info(f'打开退出PatchWall')
            self.device.open_app(patch_wall, 'S', 10)
            self.device.center(10)
            self.device.home(3)

            self.device.logger.info(f'打开退出NetFlix')
            self.device.open_app(netflix, 'S', 10)
            self.device.center(10)
            self.device.home(3)

            self.device.logger.info(f'打开退出MediaPlayer')
            self.device.open_app(media_player, 'S')
            self.device.home(3)

    def test_dash_switch(self):
        """
        DashBoard切换
        """
        for _ in range(mtbf_loop_times):
            self.device.logger.info(f'打开DashBoard菜单')
            self.device.dashboard()
            self.device.logger.info(f'打开账号预览页面')
            self.device.right(3)
            self.device.center(3)
            self.device.home(3)

            self.device.logger.info(f'遍历Dashboard菜单')
            self.device.dashboard()
            self.assertTrue(self.device.d(resourceId=dashboard_title_resource_id).exists, msg='dashboard title not found')
            dashboard_title_list = [element.get_text() for element in self.device.d(resourceId=dashboard_list_resource_id).child(resourceId=dashboard_title_resource_id) if element.get_text()]
            self.device.logger.info(f'Dashboard列表：{dashboard_title_list}')
            self.assertNotEqual(dashboard_title_list, list(), msg='dashboard title not found')
            self.device.home(3)
            for element in dashboard_title_list:
                self.device.dashboard()
                self.device.d(text=element).click()
                sleep(5)
                self.device.home(3)

    def test_power_menu(self):
        """
        Power Menu遍历
        """
        for _ in range(mtbf_loop_times):
            self.device.logger.info(f'调出Power Menu')
            self.device.power_menu()
            self.device.down(1, 3)
            self.device.back()

    def test_voice_search(self):
        """
        语音助手搜索
        """
        # 同意协议
        for _ in range(mtbf_loop_times):
            for i in range(3):
                self.device.open_app(google_voice_page, sleep_time=2)
            self.device.center(5, 3)
            self.device.back(2, 3)
            self.device.home(3)

            self.device.logger.info(f'语音播放YouTube 音乐')
            self.device.google_search_text('playbackflowersMileyCyrus')
            # 播放180秒
            self.device.center(180)
            self.device.home(3)

            self.device.logger.info(f'语音打开Netflix')
            self.device.google_search_text('Netflix')
            self.device.home(3)

            self.device.logger.info(f'语音播放YouTube 4K视频')
            self.device.google_search_text('playbackEurope4K')
            # 播放180秒
            self.device.center(180)
            self.device.home(3)

            self.device.logger.info(f'语音播放YouTube Dolby视频')
            self.device.google_search_text('playbackDoblyVideo')
            # 播放180秒
            self.device.center(180)
            self.device.home(3)

            self.device.logger.info(f'语音播放YouTube HDR视频')
            self.device.google_search_text('playback4KHDRIMAXTeaser')
            # 播放180秒
            self.device.center(180)
            self.device.home(3)

            self.device.logger.info(f'语音播放视频BloombergGlobalFinancialNewsLIVE')
            self.device.google_search_text('playbackBloombergGlobalFinancialNewsLIVE')
            # 播放180秒
            self.device.center(180)
            self.device.home(3)

            self.device.logger.info(f'语音播放视频ApexlegendsLIVE')
            self.device.google_search_text('playbackBloombergGlobalFinancialNewsLIVE')
            # 播放180秒
            self.device.center(180)
            self.device.home(3)
