from random import choice, randint
from time import sleep

import uiautomator2 as u2

from script.testcases_ww.common import keyword_match, get_dict_value_in_key
from script.testcases_ww.params import box_sound_format_list, box_device, tv_sound_modes, \
    rerun_times, picture_setting_dict, tv_picture_modes, sound_settings_dict
from script.testcases_ww.simple_adb import SimapleADB


# 默认检查Wi-Fi列表的次数
check_times = 3
# 打开设置-Wi-Fi页面
to_wifi_page = 'am start -a android.intent.action.MAIN -n com.android.tv.settings/.connectivity.NetworkActivity'
# 一些ResourceId
wifi_title_resource_id = 'android:id/icon'
atv_favourite_row_resource_id = 'com.google.android.tvlauncher:id/items_list'
atv_favourite_row_icon_resource_id = 'com.google.android.tvlauncher:id/banner_image'
keyboard_resource_id = 'android:id/inputArea'
# 键盘的确定键
keyboard_done_resource_id = 'com.google.android.inputmethod.latin:id/key_pos_ime_action'
settings_list_resource_id = 'com.android.tv.settings:id/list'
settings_title_resource_id = 'android:id/title'
settings_list_1_resource_id = 'android:id/list'
google_list_resource_id = 'com.google.android.tungsten.setupwraith:id/guidedactions_list'
google_item_resource_id = 'com.google.android.tungsten.setupwraith:id/guidedactions_item_title'
keyboard_area_resource_id = 'com.google.android.inputmethod.latin:id/tv_focus_area_input'
google_desc_resource_id = 'com.google.android.tungsten.setupwraith:id/guidance_description'
message_resource_id = 'android:id/message'
install_apps_settings_page = 'com.android.tv.settings/com.android.tv.settings.device.apps.specialaccess.ExternalSourcesActivity'


# 类
linear_class = 'android.widget.LinearLayout'
frame_class = 'android.widget.FrameLayout'
relative_class = 'android.widget.RelativeLayout'
switch_class = 'android.widget.Switch'


# 蓝牙配对页
bt_pair_page = [
    'com.xiaomi.android.tvsetup.partnercustomizer/com.xiaomi.tvsetup.partnercustomizer.bluetooth.BleRcActivity',
    'com.xiaomi.android.tvsetup.partnercustomizer/com.xiaomi.android.tvsetup.partnercustomizer.bluetooth.BleRcActivity',
    'com.xiaomi.android.tvsetup.partnercustomizer/com.xiaomi.android.tvsetup.partnercustomizer.BleRcGuide'
]
# 系统主页
main_activity_page = [
    'com.google.android.tvlauncher/com.google.android.tvlauncher.MainActivity',
    'com.google.android.apps.tv.launcherx/com.google.android.apps.tv.launcherx.home.VanillaModeHomeActivity',
    'com.google.android.apps.tv.launcherx/com.google.android.apps.tv.launcherx.home.HomeActivity'
]
# Settings页
settings_page = 'com.android.tv.settings/com.android.tv.settings.MainSettings'
# martian选择region页
region_pages = [
    'com.mediatek.wwtv.setupwizard/com.mediatek.wwtv.setupwizard.mitv.region.ServiceRegionActivity',  # martian
    'com.xiaomi.android.tvsetup.partnercustomizer/com.xiaomi.android.tvsetup.partnercustomizer.ChooseServiceRegionActivity'  # aquaman, rango
]

# 权限页
permission_page = 'com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity'
# android sdk版本提示页
sdk_version_page = 'DeprecatedTargetSdkVersionDialog'
# 视频播放页
video_play_page = 'com.mitv.videoplayer/com.mitv.videoplayer.VideoPlayerActivity'

# 找键盘上键的最大次数
press_keyboard_times_max = 30
# 限制开机向导的最大尝试次数
setup_wraith_retry_max_times = 50

# 开机向导中选择的语言
setup_language = 'English (United States)'


class UiADB(SimapleADB):
    def __init__(self, sn, log_name=None, console_level='DEBUG', file_level='DEBUG'):
        """
        初始化Uiautomator环境，一般需在刷机、恢复出厂后进行操作
        :return:
        """
        super().__init__(sn, log_name, console_level, file_level)
        self.logger.info('初始化uiautomator')
        self.d = None
        self.reset_uiautomator()

    def remove_uiautomator(self):
        self.send_adb('root')
        if self.d:
            self.d.stop_uiautomator()
        self.send_adb(f'pm uninstall com.github.uiautomator')

    def reset_uiautomator(self):
        self.remove_uiautomator()
        sleep(5)
        self.d = u2.connect(self.sn)
        self.logger.info(f'uiautomator2读取的设备参数{self.d.info}')

    def check_wifi_list(self):
        for _ in range(check_times):
            self.send_adb(to_wifi_page)
            sleep(20)
            wifi_item_num = len(self.d(resourceId=wifi_title_resource_id))
            if wifi_item_num > 1:
                self.logger.info(f'Wi-Fi列表检查正常, 有{wifi_item_num}个')
                return True
            else:
                self.logger.info('Wi-Fi列表消失，3秒后再次尝试')
                self.home(3)
        else:
            return False

    def check_atv_favourite_row(self):
        if self.is_gtv:
            self.logger.debug('GTV跳过Favourite Row图标检查')
            return True
        for _ in range(check_times):
            self.home(3, 2)
            if self.d(resourceId=atv_favourite_row_resource_id).exists and self.d(
                    resourceId=atv_favourite_row_icon_resource_id).exists:
                favourite_row_num = len(self.d(resourceId=atv_favourite_row_icon_resource_id))
                if favourite_row_num > 1:
                    self.logger.info(f'ATV Favourite Row 列表正常，有{favourite_row_num}个')
                    return True
                else:
                    self.logger.debug('ATV Favourite Row列表异常，3秒后再次尝试')
                    sleep(3)
        else:
            self.logger.info('ATV Favourite Row列表异常')
            return False

    def setup_wraith_bt_pair(self):
        cur_page = self.get_page()
        if cur_page in bt_pair_page:
            self.logger.info('开机向导-蓝牙配对页')
            self.center(5)

    def press_keyboard_done(self):
        if self.d(resourceId=keyboard_resource_id).exists:
            self.down(times=5)
            pos_now = self.get_keyboard_focus_info()
            times = press_keyboard_times_max
            while times >= 0:
                self.logger.debug(f'键盘当前光标：{pos_now}')
                if pos_now == keyboard_done_resource_id:
                    self.logger.info(f'键盘光标已在{keyboard_done_resource_id}上')
                    break
                self.left()
                times -= 1
                pos_now = self.get_keyboard_focus_info()
            else:
                raise Exception('未找到键盘上对应的值')
            self.center(5)

    def input_google_account(self, text):
        sleep(5)
        self.setup_input_text(text)

    def setup_input_text(self, text):
        self.down(2)
        self.del_key(20)
        self.input_text(text)
        self.press_keyboard_done()

    def select_language_region(self):
        # dangal开机向导先选国家，再选择语言
        if 'dangal' in self.product_name:
            self.select_region()
        self.select_language()  # 选择语言
        sleep(10)
        # 其他设备，先选择语言，再选择国家
        if 'dangal' not in self.product_name:
            self.select_region()

    # def setup_wraith(self, wifi_info):
    #     """
    #     进行开机向导流程
    #     :return:
    #     """
    #     gtv_model = 'Full' if self.build_name in self.keywords['setup_gtv_full_mode'] else 'Basic'
    #     setup_connect_wifi = self.build_name in self.keywords['setup_connect_wifi'] or self.build_name in self.keywords['setup_gtv_full_mode']
    #     limit_times = setup_wraith_retry_max_times
    #     self.setup_wraith_bt_pair()
    #     self.logger.info('开机向导-静音')
    #     self.mute()  # 禁音
    #     pai_page_times = 0
    #     input_wifi = input_account = input_pwd = False
    #     sleep(5)
    #     self.set_time_now()
    #     self.select_language_region()
    #
    #     cur_page = self.get_page()
    #     while cur_page not in main_activity_page:
    #         limit_times -= 1
    #         if limit_times == 0:
    #             raise Exception('开机向导未正常通过, 再次尝试')
    #         if self.d(resourceId=self.keywords['wait_resource_id']).exists:
    #             self.logger.info('开机向导-页面加载中')
    #             sleep(5)
    #         elif self.d(resourceId=self.keywords['wifi_icon_resource_id']).exists:  # Wi-Fi连接页选择最下方的Skip
    #             self.logger.info('当前在连接Wi-Fi页')
    #             item_wifi_title_resource_id = self.keywords['wifi_list_resource_id'] if self.d(
    #                 resourceId=self.keywords['wifi_list_resource_id']).exists else None
    #             wifi_now = self.get_item_focus(title_resource_id=item_wifi_title_resource_id)
    #             wifi_last = None
    #             find_res = False
    #             while wifi_last != wifi_now:
    #                 wifi_last = wifi_now
    #                 if setup_connect_wifi and wifi_last == wifi_info['wifi_ssid']:
    #                     find_res = True
    #                     break
    #                 self.down()
    #                 wifi_now = self.get_item_focus(title_resource_id=item_wifi_title_resource_id)
    #             if setup_connect_wifi:
    #                 if find_res:
    #                     self.center(2)
    #                     self.logger.info('找到目标Wi-Fi')
    #                 else:
    #                     self.logger.debug('未找到Wi-Fi')
    #                     self.back()
    #             else:
    #                 self.logger.info(f'Wi-Fi选择为{wifi_now}')
    #                 self.center()
    #         elif self.d(resourceId=self.keywords[
    #             'google_page_title']).exists and f'Enter password for {wifi_info["wifi_ssid"]}' in self.d(
    #                 resourceId=self.keywords['google_page_title']).get_text():
    #             if not input_wifi:
    #                 self.logger.info('开机向导-输入Wi-Fi密码')
    #                 input_wifi = True
    #                 self.setup_input_text(wifi_info['wifi_pwd'])
    #             else:
    #                 self.logger.info('已输入过Wi-Fi，加载中')
    #                 sleep(5)
    #         elif self.d(resourceId=self.keywords['google_page_title']).exists and self.keywords['connected_name'] in self.d(resourceId=self.keywords['google_page_title']).get_text():
    #             self.logger.info('开机向导-网络连接确认页')
    #             self.center(5)
    #         elif self.d(text='Set up Google TV').exists:
    #             self.logger.info(f'开机向导-Google TV页，选择{gtv_model}模式')
    #             if gtv_model == 'Basic':
    #                 self.down()
    #             self.center(5)
    #         elif (len(self.d(resourceId=google_item_resource_id)) == 2) or any(self.d(
    #                 resourceId=qr_code).exists for qr_code in self.keywords['qr_code_resource_id']):
    #             self.logger.info('开机向导-Quickly Setup页')
    #             self.down(2)
    #             self.center(5)
    #         elif cur_page == self.keywords['dangaluhd_quickly_setup_page']:
    #             self.logger.info('开机向导-DangalUHD-Quickly Setup页')
    #             self.up()
    #             self.center(5)
    #         elif self.d(text='Sign in').exists and self.d(text='Use your Google Account').exists:
    #             if not input_account:
    #                 self.logger.info('开机向导-登录-输入账号')
    #                 self.input_google_account(self.keywords['google_email'])
    #                 sleep(20)
    #             else:
    #                 self.logger.info('已输入过账号，加载中')
    #                 sleep(5)
    #         elif self.d(text='Show password').exists:
    #             if not input_pwd:
    #                 self.logger.info('开机向导-登录-输入密码')
    #                 self.input_google_account(self.keywords['google_pwd'])
    #                 sleep(20)
    #             else:
    #                 self.logger.info('已输入过密码，加载中')
    #                 sleep(5)
    #         elif self.d(resourceId=self.keywords['setup_google_title_resource_id']).exists and \
    #                 'google services' == self.d(resourceId=self.keywords['setup_google_title_resource_id']).get_text().lower():
    #             self.logger.info('开机向导-2.0-Google协议页')
    #             self.right(2, 2)
    #             self.down(2, 2)
    #             self.center(5)
    #         elif self.d(resourceId=self.keywords['google_icon_resource_id']).exists or self.d(
    #                 resourceId=self.keywords['google_voice_icon_resource_id']).exists or cur_page == self.keywords[
    #             'google_voice_setup_page']:
    #             self.logger.info('开机向导-Google协议页')
    #             if any(self.d(text=item).exists for item in self.keywords['google_page_select']):
    #                 list_resource_id = self.keywords['google_voice_list_resource_id'] if self.d(
    #                     resourceId=self.keywords['google_voice_list_resource_id']).exists else None
    #                 item_resource_id = self.keywords['google_voice_item_resource_ie'] if self.d(
    #                     resourceId=self.keywords['google_voice_item_resource_ie']).exists else None
    #                 focus_now = self.get_item_focus(list_resource_id, item_resource_id)
    #                 focus_last = None
    #                 while focus_last != focus_now:
    #                     focus_last = focus_now
    #                     if focus_now in self.keywords['google_page_select']:
    #                         self.logger.debug(f'当前光标在{focus_now}')
    #                         break
    #                     self.down()
    #                     focus_now = self.get_item_focus(list_resource_id, item_resource_id)
    #                 else:
    #                     self.logger.debug('未查找到关键字')
    #             self.center()
    #         elif self.d(text=self.keywords['ir_page_title']).exists:
    #             self.logger.info('开机向导-红外对码页')
    #             self.down(2)
    #             self.center(2, 2)
    #         elif self.d(resourceId=self.keywords['image_resource_id']).exists or self.d(
    #                 resourceId=self.keywords['image_resource_id_gtv']).exists:
    #             self.logger.info('开机向导-功能介绍页')
    #             self.center()
    #         elif self.d(resourceId=self.keywords['number_pad_resource_id']).exists:
    #             self.logger.info('开机向导-区域码页')
    #             self.center()
    #         elif self.d(resourceId=self.keywords['mediatek_item_resource_id']).exists or cur_page in self.keywords['channel_scan_page']:
    #             self.logger.info('开机向导-系统设置页')
    #             if cur_page == 'com.mitv.setup/com.mitv.setup.auto.ScanActivity':
    #                 item_resource_id = self.keywords['setup_list_resource_id']
    #             else:
    #                 item_resource_id = self.keywords['mediatek_list_resource_id']
    #             index_is_last = self.focus_compare_len(item_resource_id)
    #             while not index_is_last:
    #                 self.down(1)
    #                 index_is_last = self.focus_compare_len(item_resource_id)
    #             self.center()
    #         elif (self.d(resourceId=self.keywords['agreement_resource_id']).exists and self.d(
    #                 resourceId=self.keywords['privacy_resource_id']).exists) or cur_page in self.keywords['mi_terms_of_service_page'] :
    #             self.logger.info('开机向导-用户隐私页')
    #             self.center()
    #         elif cur_page in self.keywords['pre_install_page']:
    #             self.logger.info('开机向导-更新应用页')
    #             sleep(10)
    #         elif self.d(resourceId=self.keywords['install_app_bar_resource_id']).exists:
    #             self.logger.info('开机向导-安装应用页')
    #             sleep(60)
    #         elif self.d(resourceId=self.keywords['wifi_animation_title_resource_id']).exists:
    #             self.logger.info('开机向导-Wi-Fi连接动画页')
    #             sleep(10)
    #         elif self.d(resourceId=self.keywords['pai_title_resource_id']).exists():
    #             if pai_page_times < 5:
    #                 self.logger.info(f'开机向导-PAI页')
    #                 self.right(times=3)
    #                 self.center()
    #                 pai_page_times += 1
    #             else:
    #                 self.logger.info(f'开机向导-PAI页，确认键在左侧')
    #                 self.left(times=5)
    #                 self.center()
    #         elif cur_page == self.keywords['adding_finishing_touches_page']:
    #             self.logger.info('开机向导-Adding finishing touches页')
    #             sleep(5)
    #         elif cur_page == self.keywords['setup_home_page']:
    #             self.logger.info('开机向导-设置Home页')
    #             self.center(5)
    #         elif cur_page in self.keywords['google_auth_page']:
    #             # 目前存在在Google页面而没有焦点，导致识别不到界面元素的情况，故按Center键激活一下
    #             self.logger.info('开机向导-Google验证页')
    #             self.center()
    #         elif cur_page in self.keywords['other_pages']:
    #             self.logger.info('开机向导-多媒体相关页')
    #             self.home(3)
    #         elif not any(pack in cur_page for pack in self.keywords['setupwraith_packages']):
    #             self.logger.info('开机向导-非开机向导页')
    #             self.home(3)
    #         else:
    #             self.logger.debug(f'当前页面{cur_page}，按下Center键')
    #             self.logger.info('开机向导-其它页')
    #             self.center(3)
    #         cur_page = self.get_page()
    #     self.logger.info('进入主系统')
    #     sleep(3)
    #     return True

    def get_focus_index(self, list_resource_id, sub_class=linear_class, type_='focused', return_all_num=False):
        item_list = self.d.xpath(f'//*[@resource-id="{list_resource_id}"]//{sub_class}').all()
        for i, item in enumerate(item_list):
            item_info = item.info
            # self.logger.info(f'item_info: {item_info}')
            if item_info.get(type_) is True or item_info.get(type_) == 'true':
                return i if not return_all_num else (i, len(item_list))
        else:
            return -1

    def to_list_top(self, list_resource_id, sub_class=linear_class, ori='v'):
        """
        @param list_resource_id: 列表resourceId
        @param sub_class: 列表项的类名
        @param ori: 按键方向：v:上下，h:左右
        """
        now_index = self.get_focus_index(list_resource_id, sub_class=sub_class)
        if now_index == -1:
            self.logger.info('列表中无焦点')
            if ori == 'v':
                self.up(2)
            else:
                self.left(2)
        now_index = self.get_focus_index(list_resource_id, sub_class=sub_class)
        while now_index != 0:
            if now_index == -1:
                return False
            if ori == 'v':
                self.up()
            else:
                self.left()
            now_index = self.get_focus_index(list_resource_id, sub_class=sub_class)
        return True

    def focus_compare_len(self, list_resource_id, sub_class=linear_class):
        """
        判断页面中的列表项判断焦点是否在列表最下方
        :param list_resource_id: 列表的ResourceId
        :param sub_class: 列表子类
        :return:
        """
        item_list = self.d.xpath(f'//*[@resource-id="{list_resource_id}"]/{linear_class}').all()
        list_len = len(item_list)
        curr_index = self.get_focus_index(list_resource_id, sub_class=sub_class)
        self.logger.debug(f'list_resource_id: {list_resource_id}, sub_class: {sub_class}, list_len: {list_len}, curr_index: {curr_index}')
        return (list_len - 1) == curr_index

    def select_language(self):
        """
        选择开机向导语言，判断焦点并且down
        当找到后按下center键确定
        当未找到时在结果中标明未找到语言，并且报错ElementNotFound
        :return:
        """
        if self.d(resourceId=google_desc_resource_id).exists and not self.d(
                resourceId=google_desc_resource_id).get_text():
            self.logger.info('开机向导-选择语言')
            self.back(2, 3)
            lang_true = self.find_item(google_list_resource_id, google_item_resource_id, setup_language)
            if lang_true:
                self.logger.info(f'选择语言为：{setup_language}')
                self.center(3)
            else:
                raise Exception('未查找到目标语言，重启再次尝试')

    def select_region(self):
        """
        选择开机向导语言，判断焦点并且down
        当找到后按下center键确定
        当未找到时在结果中标明未找到语言，并且报错ElementNotFound
        :return:
        """
        google_desc_obj = self.d(resourceId=google_desc_resource_id)
        gtv_region_description_res = google_desc_obj.exists and not google_desc_obj.get_text()
        region_page = self.get_page() in region_pages
        if gtv_region_description_res or region_page:
            self.logger.info('开机向导-选择国家页面')
            self.center(10)

    def get_keyboard_focus_info(self, info='resourceId'):
        last_lines = self.d.xpath(
            f'//*[@resource-id="{keyboard_area_resource_id}"]/{linear_class}[2]/{frame_class}').all()
        for item in last_lines:
            item_info = item.info
            if item_info.get('focused') is True or item_info.get('focused') == 'true':
                return item_info.get(info)

    def get_item_focus(self, list_resource_id=None, title_resource_id=None, sub_class=linear_class):
        """
        得到页面中的焦点所在显示文字
        :param list_resource_id: 页面中列表的ResourceId
        :param title_resource_id: 列表中文字的ResourceId
        :param sub_class: 类名
        :return:
        """
        if not list_resource_id:
            list_resource_id = google_list_resource_id
        if not title_resource_id:
            title_resource_id = google_item_resource_id
        item_list = self.d.xpath(f'//*[@resource-id="{list_resource_id}"]/{sub_class}').all()
        for i, item in enumerate(item_list, 1):
            item_info = item.info
            # self.logger.debug(item_info)
            if item_info.get('focused') is True or item_info.get('focused') == 'true':
                focus_text = self.d.xpath(
                    f'//*[@resource-id="{list_resource_id}"]/{sub_class}[{i}]') \
                    .child(f'//*[@resource-id="{title_resource_id}"]').get_text()
                self.logger.debug(f'当前光标文字为: [{focus_text}]')
                return focus_text

    def find_item(self, list_resource_id, key_resource_id, keyword, fuzzy=False, sub_class=linear_class, ori='v'):
        if ori == 'v':
            self.up(3)
        else:
            self.left(3)
        self.logger.debug(f'查找关键字：{keyword}')
        find_res = False
        item_now = self.get_item_focus(list_resource_id, key_resource_id, sub_class)
        item_last = None
        while item_last != item_now:
            item_last = item_now
            if keyword_match(keyword, item_now, fuzzy):
                find_res = True
                break
            if ori == 'v':
                self.up()
            else:
                self.left()
            item_now = self.get_item_focus(list_resource_id, key_resource_id, sub_class)
        else:
            self.logger.debug('当前在列表最上方，开始下翻')

        if not find_res:
            item_last = None
            while item_last != item_now:
                item_last = item_now
                if keyword_match(keyword, item_now, fuzzy):
                    find_res = True
                    break
                if ori == 'v':
                    self.down()
                else:
                    self.right()
                item_now = self.get_item_focus(list_resource_id, key_resource_id, sub_class)
        if find_res:
            self.logger.info(f'查找到关键字：{keyword}')
        return find_res

    def enter_item_path(self, list_resource_id, key_resource_id, path_list, fuzzy=False):
        """
        进入指定路径
        :param list_resource_id: 页面中列表的ResourceId
        :param key_resource_id: 列表中文字的ResourceId
        :param path_list: 路径列表
        :param fuzzy: 是否模糊匹配
        :return:
        """
        if isinstance(path_list, str):
            path_list = [path_list]
        for item in path_list:
            if self.find_item(list_resource_id, key_resource_id, item, fuzzy):
                self.center(3)
            else:
                return False
        else:
            return True

    def get_list_items(self, list_resource_id, key_resource_id, sub_class=linear_class, ori='v'):
        if not (self.d(resourceId=list_resource_id).exists and self.d(resourceId=key_resource_id).exists):
            return False

        item_list = list()
        if self.to_list_top(list_resource_id, sub_class, ori=ori):
            item_focus = self.get_item_focus(list_resource_id, key_resource_id, sub_class)
            while (not self.focus_compare_len(list_resource_id, sub_class)) and (item_focus not in item_list):
                item_list.append(item_focus)
                if ori == 'v':
                    self.down()
                else:
                    self.right()
                item_focus = self.get_item_focus(list_resource_id, key_resource_id, sub_class)
            if item_focus not in item_list:
                item_list.append(self.get_item_focus(list_resource_id, key_resource_id, sub_class))
        return item_list

    def enter_settings_path(self, path_list, fuzzy=False, open_settings=True):
        """
        进入设置的指定路径
        :path_list: 路径列表
        :fuzzy: 是否模糊匹配
        """
        if open_settings:
            self.open_app(settings_page, 'S', 5)
        self.up(2, 2)
        return self.enter_item_path(settings_list_resource_id, settings_title_resource_id, path_list, fuzzy)

    def screenshot(self, path):
        self.d.screenshot(path)

    def exception_operate(self):
        self.reset_uiautomator()
        self.home()
        self.back(3, 6)

    def travel_setting_list(self, list_resource_id=settings_list_resource_id, item_resource_id=settings_title_resource_id):
        if self.to_list_top(list_resource_id):
            for _ in range(len(self.d(resourceId=item_resource_id)) - 1):
                self.down(2)
                self.back(2)

    def get_text_resource_id(self, text, match_type='', parent_step=2):
        """
        获取指定文字的list 和 item 的resource_id
        """
        if match_type == 'contains':
            element = self.d.xpath(f'//android.widget.TextView[contains(@text, "{text}")]')
        elif match_type == 'startswith':
            element = self.d.xpath(f'//android.widget.TextView[starts-with(@text, "{text}")]')
        else:
            element = self.d.xpath(f'//android.widget.TextView[@text="{text}"]')
        # 当文字为空时返回None
        if not element.exists:
            return None
        item_resource_id = element.info.get('resourceId')
        for i in range(parent_step):
            element = element.parent()
        # element = element.parent().parent()
        while not element.info.get('resourceId'):
            try:
                self.logger.debug(element.info)
                element = element.parent()
            except AttributeError as e:
                self.logger.debug(e.__str__())
                # 当节点为空时返回None
                return None
        list_resource_id = element.info.get('resourceId')
        return list_resource_id, item_resource_id

    def click_list_text(self, text, sleep_time=3, match_type='', parent_step=2, sub_class=linear_class):
        """
        点击列表中的某项
        """
        match_res = self.get_text_resource_id(text, match_type, parent_step=parent_step)
        self.logger.info(f'match_res: {match_res}')
        if match_res:
            if self.find_item(match_res[0], match_res[1], text, not match_type == '', sub_class=sub_class):
                self.center(sleep_time)
                return True
        else:
            return False

    def before_play_video_check(self):
        """
        播放视频前的权限检查，暂需手动调用
        """
        for _ in range(rerun_times):
            if self.get_page() == sdk_version_page:
                self.logger.info('SDK版本提示页')
                self.center()
            sleep(3)
            if self.get_page() == permission_page:
                self.logger.info('权限页')
                self.center()
            sleep(3)

    def change_sound_mode(self):
        if self.find_item(settings_list_resource_id, settings_title_resource_id, 'Dolby', True):
            self.logger.info('点击Dolby Sounds菜单')
        elif self.find_item(settings_list_resource_id, settings_title_resource_id, 'Mode', True):
            self.logger.info('点击Sound Mode菜单')

        for i in range(5):
            self.center(2)
            self.down(1, i)
            self.center(3)
        self.back(2)
        self.center(2, 2)
        self.back(2, 2)

    def gtv_box_change_sound_format(self, format_):
        if self.enter_settings_path(['Display', 'Advanced sound settings', 'Select formats'], True, True):
            if self.find_item(settings_list_1_resource_id, settings_title_resource_id, format_, True):
                self.center(3)
                return True

    def gtv_tv_change_sound_mode(self, mode_):
        self.open_app(get_dict_value_in_key(sound_settings_dict, self.build_name, sound_settings_dict['default']), 'S', 5)
        if self.find_item(settings_list_resource_id, settings_title_resource_id, 'Sound mode', True) or self.find_item(settings_list_resource_id, settings_title_resource_id, 'Dolby Sound', True):
            self.center(3)
            if self.find_item(settings_list_1_resource_id, settings_title_resource_id, mode_, True):
                self.center(3)
                self.back(3)
                return True

    def random_picture_format(self):
        item_random_sound_format = choice(tv_picture_modes)
        self.logger.info(f'切换图像模式为：{item_random_sound_format}')
        self.gtv_tv_change_picture_mode(item_random_sound_format)

    def gtv_tv_change_picture_mode(self, mode_, back=True):
        self.open_app(get_dict_value_in_key(picture_setting_dict, self.build_name, picture_setting_dict['default']), 'S', 5)
        if self.find_item(settings_list_resource_id, settings_title_resource_id, 'Picture mode', True):
            self.center(3)
            if self.find_item(settings_list_1_resource_id, settings_title_resource_id, mode_, True):
                self.center(3)
                if back:
                    self.back(3)
                return True

    def gtv_box_change_resolution(self, resolution):
        if self.enter_settings_path(['Display', 'Resolution', resolution], True, True):
            sleep(5)
            self.center(3)
            return True
        return False

    def random_sound_format(self):
        item_sound_list = box_sound_format_list if self.build_name in box_device else tv_sound_modes
        item_random_sound_format = choice(item_sound_list)
        self.logger.info(f'当前切换音效模式为：{item_random_sound_format}')
        if self.build_name in box_device:
            self.gtv_box_change_sound_format(item_random_sound_format)
        else:
            self.gtv_tv_change_sound_mode(item_random_sound_format)

    def global_dimming_play(self, item_play_time):
        """
        Global/Local Dimming播放
        """
        self.power_menu()
        if self.d(resourceId=message_resource_id, text='Screen off').exists:
            self.logger.info('Power菜单中Screen off存在，进入Screen off模式')
            # 一般Screen off都在Power菜单的第一项，所以直接按确定即可
            self.center(3)
        else:
            self.back(3)
            self.logger.info('Power菜单中Screen off不存在，使用命令进入Screen off模式')
            self.send_adb('settings put global screen_off_control 0')
        self.logger.info(f'阶段播放时间{item_play_time}s')
        sleep(item_play_time // 2)
        if self.send_adb('settings get global screen_off_control') == '0':
            self.logger.info('唤醒屏幕')
            self.power()
        sleep(item_play_time // 2)

    def long_play_uat(self, play_type):
        for i in range(1, 61):
            self.logger.info(f'{play_type}长播循环：{i}')
            sleep(10)
            self.volume_up(randint(1, 5))
            sleep(10)
            self.volume_down(5)


if __name__ == '__main__':
    d = UiADB('10.192.53.104')
    d.random_sound_format()

