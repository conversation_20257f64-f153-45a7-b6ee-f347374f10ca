import unittest
import os
from time import sleep

from script.testcases_ww.params import mtbf_loop_times
from script.testcases_ww.ui_adb import UiADB


bt_pair_page = 'com.android.tv.settings/com.android.tv.settings.accessories.AddAccessoryActivity'
setting_list_resource_id = 'com.android.tv.settings:id/list'
setting_icon_resource_id = 'android:id/icon'
setting_title_resource_id = 'android:id/title'


class Bluetooth(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def test_bluetooth_list(self):
        """
        遍历蓝牙设备列表
        """
        for _ in range(mtbf_loop_times):
            self.device.logger.info(f'查找Remotes & Accessories')
            enter_remote_res = self.device.enter_settings_path(['Remotes'], fuzzy=True)
            self.assertTrue(enter_remote_res, '没有找到 Remotes & Accessories')
            self.device.logger.info(f'遍历设备列表')
            if self.device.to_list_top(setting_list_resource_id):
                for _ in range(len(self.device.d(resourceId=setting_icon_resource_id)) - 1):
                    self.device.down(2)
                    self.device.center(2)
                    self.device.travel_setting_list()

    def test_pair(self):
        """
        搜索配对
        """
        for _ in range(mtbf_loop_times):
            self.device.logger.info(f'进入搜索配对界面')
            enter_pair_res = self.device.enter_settings_path(['Remotes', 'accessory'], fuzzy=True)
            self.assertTrue(enter_pair_res, '没有找到Pair accessory')
            self.assertEqual(self.device.get_page(), bt_pair_page, '未打开蓝牙配对界面')
            sleep(60)
            self.device.back()
