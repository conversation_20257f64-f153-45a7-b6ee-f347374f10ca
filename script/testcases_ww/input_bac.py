#!/user/bin/python
# -*- coding:utf-8 -*-
import unittest
import os
import subprocess
from script.testcases_ww.adb_command import *
# from uiautomator import device as d
import uiautomator2 as u2
import configparser

# 播放视频文件夹
stress_test_folder = '111AAA StressFolder'
# 单条用例切源循环次数
loop_times = 10
# 切源间隔时间
switch_time = 30

youtube_play = 'am start -W -a android.intent.action.VIEW -d ""https://www.youtube.com/watch?v=c2Zr50b4xpE"" -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.ShellActivity'


media_activity = 'com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity'


# input_type_list = ['AV', 'HDMI1']
input_cmd_prefix = 'am start -a android.intent.action.VIEW -d content://android.media.tv/'
# TV: input_cmd_prefix + channel
# Other:  input_cmd_prefix + passthrough/INPUT_ID
input_id_dict = {
    'HDMI1': {
        'venom;irobot': 'com.mediatek.tvinput/.hdmi.HDMIInputService/HW4',
        'ladybird;river': 'com.mediatek.tis/.HdmiInputService/HW2',
        'watchmen': 'com.droidlogic.tvinput/.services.Hdmi1InputService/HW5'
    },
    'HDMI2': {
        'venom;irobot': 'com.mediatek.tvinput/.hdmi.HDMIInputService/HW5',
        'ladybird;river': 'com.mediatek.tis/.HdmiInputService/HW3',
        'watchmen': 'com.droidlogic.tvinput/.services.Hdmi2InputService/HW6'
    },
    'HDMI3': {
        'venom': 'com.mediatek.tvinput/.hdmi.HDMIInputService/HW6',
        'ladybird;river': 'com.mediatek.tis/.HdmiInputService/HW4',
        'irobot;watchmen': False
    },
    'AV': {
        'venom;irobot': 'com.mediatek.tvinput/.composite.CompositeInputService/HW1',
        'river': 'com.mediatek.tis/.CompositeInputService/HW5',
        'watchmen': 'com.droidlogic.tvinput/.services.AV1InputService/HW1',
        'ladybird': False
    }
}

class Input(unittest.TestCase):
    def setUp(self):
        """
        called before each test method start.
        """
        """docstring for ClassName"""
        # setUp之后再读取配置文件
        cf = configparser.ConfigParser()
        cf.read("script/testcases_ww/config.ini", encoding="utf-8")
        if not os.path.exists("script/testcases_ww/config.ini"):
            print("script/testcases_ww/config.ini does not exists")
        self.tv_id = cf.get('devices_info', 'tv_id')
        print("tv id:{}".format(self.tv_id))
        # 当前设备Build name
        self.build_name = timeout_command('adb -s {} shell getprop ro.build.product'.format(self.tv_id))
        print(f'Build name: {self.build_name}')

        # 是否GTV
        is_gtv = True if timeout_command('adb -s {} shell getprop | grep gtv'.format(self.tv_id)) else False
        print(f'Is GTV: {is_gtv}')

        self.d = u2.connect(self.tv_id)    # 初始化uiautomator2

        for i in range(2):
            try:
                for _ in range(4):
                    adb_back()
                time.sleep(1)
                adb_home()
                time.sleep(2)
            except IOError as exp:
                print(str(exp))
                # self.restart_uiautomator()
                adb_home()

    # def restart_uiautomator(self):
    #     for i in range(2):
    #         self.d.stop()
    #         time.sleep(5)
    #         d.server.start()
    #         time.sleep(5)

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        back2home_page()

    def testSwithChannel(self):
        """
        lanuch the TVSource and testSwithChannel
        """
        timeout_command(f'adb -s {self.tv_id} shell {input_cmd_prefix}/channel')
        for i in range(10):
            adb_quickdown()
            time.sleep(50)
        for i in range(10):
            adb_quickdown()
            time.sleep(50)

    def test_HDMI2toHDMI3(self):
        """
        同一通路信号源切换测试
        """
        for _ in range(loop_times):
            self.switch_to_input('HDMI2')
            time.sleep(switch_time)
            self.switch_to_input('HDMI3')
            time.sleep(switch_time)

    def test_HDMI1toHDMI2(self):
        """
        同一通路信号源切换测试
        """
        for _ in range(loop_times):
            self.switch_to_input('HDMI1')
            time.sleep(switch_time)
            self.switch_to_input('HDMI2')
            time.sleep(switch_time)

    def test_TVtoLocal(self):
        """
        同一通路信号源切换测试
        """
        for i in range(loop_times):
            self.switch_to_input('LOCAL')
            time.sleep(switch_time)
            self.switch_to_input('TV')
            time.sleep(switch_time)

    def test_TVtoAV(self):
        """
        同一通路信号源切换测试
        """
        for i in range(loop_times):
            self.switch_to_input('AV')
            time.sleep(switch_time)
            self.switch_to_input('TV')
            time.sleep(switch_time)

    def switch_to_input(self, input_type):
        if input_type == 'LOCAL':
            self.playUSBvideo()
        else:
            if input_type == 'TV':
                item_input_cmd = input_cmd_prefix + 'channel'
            else:
                item_input_cmd = input_cmd_prefix + 'passthrough/'
                input_id = get_dict_value_in_key(input_id_dict[input_type], self.build_name)
                if not input_id:    # None 或 False
                    if input_id is None:
                        print(f'暂未适配该项目：{self.build_name}')
                    else:
                        print(f'该项目无该源：{input_type}')
                    return False
                else:
                    item_input_cmd += input_id.replace('/', '%2F')
            timeout_command(f'adb -s {self.tv_id} shell {item_input_cmd}')
        return True

    def test_HDMI(self):
        """
        HDMI源长时间播放
        """
        for i in range(10):
            self.pingbaidu()
            time.sleep(1080)

    def pingbaidu(self):
        try:
            proc = subprocess.Popen("adb -s {} shell ping www.baidu.com".format(self.tv_id), shell=True, stdout=PIPE, stderr=STDOUT).wait(timeout=1)
        except Exception as exp:
            print(str(exp))
            print('finish ping test')

    def playUSBvideo(self):
        for i in range(5):
            try:
                timeout_command(f'adb -s {self.tv_id} shell am force-stop {media_activity.split("/")[0]}')
                time.sleep(5)
                start_app(media_activity)
                time.sleep(10)
                # assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev').wait(
                #     timeout=20), 'launch Media Explorer failed!'
                # d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev').click(timeout=3)
                # d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click(timeout=3)
                adb_left()
                time.sleep(5)
                adb_down()
                time.sleep(5)
                adb_center()
                time.sleep(10)
                self.d(text=stress_test_folder).click()
                print('find folder')
                break
            except Exception as exp:
                print(str(exp))
                [adb_quickdown() for _ in range(9)]
        time.sleep(5)
        for i in range(2):
            # 点击开始播放
            adb_center()
            if self.d(resourceId='com.xiaomi.mitv.mediaexplorer:id/no_file_layout').wait(timeout=10):
                adb_back()
            else:
                # 播放30秒
                time.sleep(30)
                # 暂停
                adb_center()
                time.sleep(3)
                [adb_back() for _ in range(2)]
            adb_down()

    def test_playYouTube(self):
        timeout_command(f'adb -s {self.tv_id} shell {youtube_play}')
        time.sleep(30)
