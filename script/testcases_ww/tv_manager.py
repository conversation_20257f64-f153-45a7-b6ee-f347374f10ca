import os
import unittest

from script.testcases_ww.params import box_device, mtbf_loop_times
from script.testcases_ww.ui_adb import *

apk_name = 'gaana'

tv_manager_page = 'com.xiaomi.mitv.tvmanager/.MainTvManagerActivity'
optimize_resource_id = 'com.xiaomi.mitv.tvmanager:id/optimize'
func_list_resource_id = 'com.xiaomi.mitv.tvmanager:id/main_grid'
func_title_resource_id = 'com.xiaomi.mitv.tvmanager:id/main_grid_item_title'
circle_title_resource_id = 'com.xiaomi.mitv.tvmanager:id/title'
circle_subtitle_resource_id = 'com.xiaomi.mitv.tvmanager:id/subtitle'
circle_clean_resource_id = 'com.xiaomi.mitv.tvmanager:id/cleaning_icon'
boosting_resource_id = 'com.xiaomi.mitv.tvmanager:id/boost_anima_progress'
app_data_title_resource_id = 'com.xiaomi.mitv.tvmanager:id/app_name'
apk_list_resource_id = 'com.xiaomi.mitv.tvmanager:id/apk_list'
apk_title_resource_id = 'com.xiaomi.mitv.tvmanager:id/apkmanager_list_item_name'
uninstall_list_resource_id = 'com.xiaomi.mitv.tvmanager:id/uninstall_list'
uninstall_title_resource_id = 'com.xiaomi.mitv.tvmanager:id/app_info_name'


class TVManager(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def to_tv_manager(self):
        self.device.open_app(tv_manager_page, 'S')
        self.device.up(2)
        self.assertTrue(self.device.d(resourceId=optimize_resource_id).exists, '打开TV Manager失败')

    def test_item_exist(self, text):
        ele = self.device.d(resourceId=func_title_resource_id, textContains=text)
        if not ele.exists:
            self.skipTest(f'当前设备不支持{text}')

    def install_app(self):
        self.to_tv_manager()
        self.device.down(1, 2)
        self.assertTrue(
            self.device.find_item(func_list_resource_id, func_title_resource_id, 'Install via USB', True, relative_class, 'h'),
            '未查找到Install via USB'
        )
        self.device.center(5, 2)
        self.assertTrue(
            self.device.find_item(apk_list_resource_id, apk_title_resource_id, apk_name, True, relative_class),
            f'未找到{apk_name}'
        )
        self.device.center(5)
        massage_item = self.device.d(resourceId=message_resource_id)
        if massage_item.exists and 'For your security' in massage_item.get_text():
            self.device.logger.info('Media Player权限提醒')
            self.device.left(2)
            self.device.center(3)
        if self.device.get_page() == install_apps_settings_page:
            self.device.logger.info('设置安装未知应用权限页')
            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Media Player'):
                self.device.center(3)
                self.device.back(3)
        self.device.left(2)
        self.device.center(10)
        self.device.center(3, 2)
        self.assertTrue(apk_name in self.device.get_page(), f'未成功打开{apk_name}')
        self.device.back(2, 3)
        self.device.home(3)

    def uninstall_app(self):
        self.to_tv_manager()
        self.device.down(1, 2)
        self.assertTrue(
            self.device.find_item(func_list_resource_id, func_title_resource_id, 'Uninstall', True, relative_class, 'h'),
            '未查找到Uninstall app'
        )
        self.device.center(5)
        self.assertTrue(
            self.device.find_item(uninstall_list_resource_id, uninstall_title_resource_id, apk_name, True, relative_class),
            f'未找到{apk_name}'
        )
        self.device.center(5, 2)
        self.device.back(2, 3)
        self.device.home(3)

    def test_launch_tv_manager(self):
        """
        测试启动退出TVManager
        :return:
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.device.back(2, 2)
            self.device.home(3)

    def test_optimize(self):
        """
        优化测试
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.device.center(1)
            optimize = self.device.d(resourceId=circle_clean_resource_id)
            self.assertTrue(optimize.exists, '未进行优化')
            for i in range(10):
                if optimize.exists:
                    self.device.logger.info('优化中...')
                    sleep(5)
                else:
                    self.device.logger.info('优化结束')
                    break
            self.device.back(2, 2)
            self.device.home(10)

    def test_memory_boost(self):
        """
        内存加速
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.test_item_exist('Memory')
            self.device.down(1, 2)
            for i in range(2):
                self.assertTrue(self.device.find_item(func_list_resource_id, func_title_resource_id, 'Memory', True, relative_class, 'h'),
                                '未查找到Memory boost')
                self.device.center(20)
                self.device.left(2)
                self.device.center(2)
                if not self.device.d(textContains='Cleaning').exists:
                    self.device.logger.info('Everything looks good')
                    self.device.back(2)
                else:
                    sleep(25)
                    self.assertTrue(self.device.d(text='Boosted successfully').exists, '未找到Boosted successfully')
                    self.device.center(3)
            self.device.back(2, 2)

    def test_trash_clean(self):
        """
        垃圾清理
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.test_item_exist('Cleaner')
            self.device.down(1, 2)
            for i in range(2):
                self.assertTrue(self.device.find_item(func_list_resource_id, func_title_resource_id, 'Cleaner', True, relative_class, 'h'),
                                '未查找到Cleaner')
                self.device.center(20)
                self.device.center(30)
                self.assertTrue(self.device.d(text='Done').exists, '未找到Done')
                self.device.left(2)
                self.device.center(3)

    def test_install_uninstall_app(self):
        """
        测试安装与卸载应用
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.test_item_exist('Install')
            self.test_item_exist('Uninstall')
            self.install_app()
            self.uninstall_app()

    def test_deep_clean(self):
        """
        TVManager深度清理
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.test_item_exist('Deep clean')
            self.device.down(1, 2)
            self.assertTrue(
                self.device.find_item(func_list_resource_id, func_title_resource_id, 'Deep', True, relative_class, 'h'),
                '未查找到')
            self.device.center(3)

        if self.device.d(resourceId=circle_subtitle_resource_id).get_text() == '0 large files':
            self.device.logger.info('0 large files')
        else:
            self.device.logger.info('清理大文件')
            self.device.left(1, 2)
            self.device.center(5)
            self.device.back(2)

        self.device.logger.info('清理应用数据')
        self.device.left(1, 2)
        self.device.right(2)
        self.device.center(5)
        app_data_items = self.device.d(resourceId=app_data_title_resource_id)
        self.assertTrue(app_data_items.exists, '进入应用数据页面失败')
        for i in range(len(app_data_items)):
            self.device.center(1)
        self.device.back(2)

        self.device.logger.info('深度清理')
        self.device.right(1, 2)     # 光标在Full clean上
        self.device.center(2)       # 确定
        self.device.right(2)        # 光标放在Cancel
        self.device.center(2)       # 选择Cancel 回到Full clean界面
        self.device.center(2)       # 再次确定
        self.device.left(2)         # 光标放在OK
        self.device.center(20)       # 确定Full clean
        self.device.back(2, 3)

    def test_tv_manager_setting(self):
        """
        TVManager设置
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.test_item_exist('Settings')
            self.device.down(1, 2)
            self.assertTrue(
                self.device.find_item(func_list_resource_id, func_title_resource_id, 'Settings', True, relative_class, 'h'),
                '未查找到')
            self.device.center(3)
            self.device.up(2)
            self.device.center(3)
            self.device.down(2)
            self.device.right(1, 4)
            self.device.up(2)
            self.device.center(2)
            self.device.back(2, 3)
            self.device.home(3)

    def test_data_usage(self):
        """
        Data usage 列表遍历
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_tv_manager()
            self.test_item_exist('usage')
            self.device.down(1, 2)
            self.assertTrue(self.device.find_item(func_list_resource_id, func_title_resource_id, 'usage', True, relative_class, 'h'),
                            '未查找到')
            self.device.center(10)
            self.device.down(2, 20)
            self.device.back(2, 2)


