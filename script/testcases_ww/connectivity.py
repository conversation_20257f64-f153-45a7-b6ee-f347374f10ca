import unittest
import os
from script.testcases_ww.ui_adb import *


class Settings(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def test_turn_wifi(self):
        """
        打开关闭Wi-Fi
        :return:
        """
        self.assertTrue(self.device.enter_settings_path('Network', fuzzy=True), '进入Network & Internet失败')
        for i in range(120):
            self.device.logger.info(f'第{i}次打开关闭Wi-Fi')
            self.device.center(7, 2)
