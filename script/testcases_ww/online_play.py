import unittest
import os
from time import sleep, time
from random import choice, randint

from script.testcases_ww.params import box_device, all_time, operate_play_time, play_operates, youtube, volume_step_max, \
    netflix, not_support_netflix_device
from script.testcases_ww.ui_adb import *

# 应用活动名
youtube_music = 'com.google.android.youtube.tvmusic/com.google.android.apps.youtube.tvmusic.activity.MainActivity'
prime_video = 'com.amazon.amazonvideo.livingroom/com.amazon.ignition.IgnitionActivity'
spotify = 'com.spotify.tv.android/.SpotifyTVActivity'



class OnlinePlay(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def test_video_netflix(self):
        if self.device.build_name in not_support_netflix_device:
            self.skipTest('此设备不支持Netflix，故跳过')
        self.device.open_app(netflix, 'S', 20)
        self.device.center(50, 2)
        self.play_content('netflix')

    def test_video_prime(self):
        """
        因网络原因，暂不实现
        """
        pass

    def test_video_youtube(self):
        self.device.open_app(youtube, 'S', 10)
        self.device.right(2, 2)
        self.device.down(2)
        self.device.center(20, 2)
        self.play_content('youtube')

    def test_audio_youtube(self):
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.device.open_app(youtube_music, 'S', 10)
        self.device.right(2, 2)
        self.device.down(2)
        self.device.center(20, 2)
        self.play_content('youtube_music')

    def test_audio_spotify(self):
        # 需要先切换音效模式
        self.device.random_sound_format()
        self.device.open_app(spotify, 'S', 10)
        self.device.down(2, 2)
        self.device.center(5,2)
        self.device.back()
        self.play_content('spotify')

    def seek(self, direction='back', step=5, app_name=None):
        """
        快进快退
        @params direction: 快进还是快退
        @params step: 快进或快退的步长
        @params app_name:
        """
        # 快进快退前操作
        if app_name == 'spotify':
            self.device.up(2, 2)

        # 快进快退
        if direction == 'back':
            self.device.left(0, step)
        elif direction == 'forward':
            self.device.right(0, step)
        sleep(2)

        # 快进快退后恢复播放的操作
        if app_name in ['youtube_music', 'youtube', 'netflix']:
            self.device.center(3)
        elif app_name == 'spotify':
            self.device.back()

    def play_content(self, app_name):
        start_time = time()
        while time() - start_time < all_time * 60:
            item_play_time = randint(10, operate_play_time * 60)
            item_operate = choice(play_operates)
            self.device.logger.info(f'阶段播放时间{item_play_time // 2}s后执行操作{item_operate}后再播放{item_play_time // 2}s')
            sleep(item_play_time // 2)
            if item_operate == 'pause-resume':
                # 暂停操作
                if app_name in ['youtube_music', 'spotify', 'youtube']:
                    self.device.center(1, 2)
                elif app_name == 'netflix':
                    self.device.center()
                pause_time = randint(1, 10)
                self.device.logger.info(f'暂停播放{pause_time}s')
                sleep(pause_time)
                # 继续播放操作
                if app_name in ['youtube_music', 'spotify', 'youtube', 'netflix']:
                    self.device.center()
                    self.device.back()
            elif item_operate == 'seek-back':
                back_step = randint(1, 5)
                self.device.logger.info(f'快退{back_step}步')
                self.seek('back', back_step, app_name)
            elif item_operate == 'seek-forward':
                forward_step = randint(1, 5)
                self.device.logger.info(f'快进{forward_step}步')
                self.seek('forward', forward_step, app_name)
            elif item_operate == 'volume-up_down':
                # 这里调高音量弄少一些，防止声音太大了
                up_step = randint(1, volume_step_max)
                self.device.logger.info(f'调高音量{up_step}')
                self.device.volume_up(up_step)
                sleep(item_play_time // 2)
                self.device.logger.info(f'调低音量{volume_step_max}')
                self.device.volume_down(volume_step_max)
            elif item_operate == 'ffga':
                self.device.google_voice_up_down(False)
            sleep(item_play_time // 2)
        return True


