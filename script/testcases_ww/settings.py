import time
import unittest
import os
from time import sleep

from script.testcases_ww.params import box_device, box_resolution_list, mtbf_loop_times
from script.testcases_ww.ui_adb import *


bt_pair_page = 'com.android.tv.settings/com.android.tv.settings.accessories.AddAccessoryActivity'
setting_list_resource_id = 'com.android.tv.settings:id/list'
setting_icon_resource_id = 'android:id/icon'
setting_title_resource_id = 'android:id/title'


class Settings(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def launch_settings(self):
        self.device.open_app(settings_page, 'S', 5)
        self.assertTrue(self.device.get_page() == settings_page, '打开Settings失败')

    def check_item_exist(self, text, all_name=None):
        ele = self.device.d(resourceId=settings_title_resource_id, textContains=text)
        if not ele.exists:
            self.skipTest(f'当前设备不支持{all_name if all_name else text}')

    def test_channel_input(self):
        """
        遍历Channel & Inputs
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Settings-Channel，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.check_item_exist('Inputs', 'Channel & Inputs')
            self.assertTrue(self.device.enter_settings_path('Channels', True, False), 'Not Found Channel & Inputs')
            self.assertTrue(self.device.enter_settings_path('Channels', True, False), 'Not Found Channel')
            sleep(5)
            self.device.back(3)
            self.assertTrue(self.device.enter_settings_path('inputs', True, False), 'Not Found External inputs')
            sleep(5)
            self.device.back(1, 3)
            self.device.home()

    def test_display_sound(self):
        """
        打开Display & Sound
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Settings-Display-Picture/Sound/Audio，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.check_item_exist('Display', 'Display & Sound')
            self.assertTrue(self.device.enter_settings_path('Display', True, False), 'Not Found Display & Sound')
            self.assertTrue(self.device.enter_settings_path('Picture', True, False), 'Not Found Picture')
            sleep(5)
            self.device.back(3)
            self.assertTrue(self.device.enter_settings_path('Sound', True, False), 'Not Found Sound')
            sleep(5)
            self.device.back(3)
            self.assertTrue(self.device.enter_settings_path('Audio', True, False), 'Not Found Audio output')
            sleep(5)
            self.device.back(1, 3)
            self.device.home()

    def test_network(self):
        """
        打开Network & Internet
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.check_item_exist('Network', 'Network & Internet')
            self.assertTrue(self.device.enter_settings_path('Network', True, False), 'Not Found Network & Internet')
            self.device.down(2, 10)
            self.device.back(1, 2)
            self.device.home()

    def test_account(self):
        """
        打开Account & sign in
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.check_item_exist('Accounts', 'Account & sign in')
            self.assertTrue(self.device.enter_settings_path('Accounts', True, False), 'Not Found Accounts & sign-in')
            self.assertTrue(self.device.enter_settings_path('account', True, False), 'Not Found Add an account')
            sleep(5)
            self.device.back(5)
            self.assertTrue(self.device.enter_settings_path('kid', True, False), 'Not Found Add a kid')
            sleep(5)
            self.device.back(5)
            self.device.back(1, 2)
            self.device.home()

    def test_privacy(self):
        """
        遍历Privacy
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.check_item_exist('Privacy')
            self.assertTrue(self.device.enter_settings_path('Privacy', True, False), 'Not Found Privacy')
            for i in range(10):
                self.device.down(i)
                self.device.center(5)
                self.device.down(5)
                self.device.back(3)
            self.device.back(1, 2)
            self.device.home()

    def test_apps(self):
        """
        遍历Apps
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.check_item_exist('Apps')
            self.assertTrue(self.device.enter_settings_path('Apps', True, False), 'Not Found Apps')
            for i in range(8):
                self.device.center(5)
                self.device.down(2, 5)
                self.device.back(2)
                self.device.down(2)
            self.device.back(2, 2)

    def test_system(self):
        """
        遍历System
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.device.down(1, 10)
            self.check_item_exist('System')
            self.assertTrue(self.device.enter_settings_path('System', True, False), 'Not Found System')
            for i in range(12):
                cur_item = self.device.get_item_focus(setting_list_resource_id, setting_title_resource_id)
                if cur_item.lower() in ['system sounds', 'restart']:
                    sleep(2)
                else:
                    self.device.center(2)
                    self.device.down(2, 5)
                    self.device.back(2)
                self.device.down(2)
            self.device.back(2, 2)

    def test_accessibility(self):
        """
        遍历Accessibility
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.device.down(1, 10)
            self.check_item_exist('Accessibility')
            self.assertTrue(self.device.enter_settings_path('Accessibility', True, False), 'Not Found Accessibility')
            for i in range(10):
                self.device.center(2)
                cur_item = self.device.get_item_focus(setting_list_resource_id, setting_title_resource_id)
                if cur_item.lower() in ['bold text', 'audio description', 'high contrast text']:
                    self.device.center(2)
                else:
                    self.device.down(2, 2)
                    self.device.back(3)
                self.device.down(3)

    def test_about(self):
        """
        遍历About
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.device.down(1, 10)
            self.check_item_exist('System')
            self.assertTrue(self.device.enter_settings_path(['System', 'About'], True, False), 'Not Found About')
            for i in range(11):
                cur_item = self.device.get_item_focus(setting_list_resource_id, setting_title_resource_id)
                if cur_item.lower() in ['system update', 'device name', 'status', 'legal information']:
                    self.device.center(2)
                    if cur_item.lower() == 'status':
                        self.device.center(2)
                        self.device.down(2, 5)
                    elif cur_item.lower() == 'legal information':
                        self.device.center(2)
                        for j in range(8):
                            self.device.center(10)
                            self.device.back(5)
                            self.device.down(2)
                    else:
                        sleep(5)
                    self.device.back(2)
                self.device.down(2)

    def test_help(self):
        """
        遍历Help & Feedback
        """
        for _ in range(mtbf_loop_times):
            self.launch_settings()
            self.device.down(1, 10)
            self.check_item_exist('Help', 'Help & Feedback')
            self.assertTrue(self.device.enter_settings_path('Help', True, False), 'Not Found Help & Feedback')
            for i in range(2):
                self.device.center(5)
                self.device.back(2, i + 1)
                self.device.down(2)
            self.device.back(1, 2)
            self.device.home()

    def test_box_change_resolution(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子支持此用例')
        for resolution in box_resolution_list:
            self.device.gtv_box_change_resolution(resolution)
            self.device.home(3, 2)
            sleep(30)
            self.device.screen_cap(3, 10)

