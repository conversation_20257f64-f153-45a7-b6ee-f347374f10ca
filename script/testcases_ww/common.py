import logging
import os
import re
import zipfile
import base64

from subprocess import Popen, PIPE, STDOUT
from logging.handlers import RotatingFileHandler
from time import strftime, localtime, mktime
from datetime import datetime
from secrets import token_urlsafe

import yaml

from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter
from openpyxl.cell import MergedCell


def init_log_dir(log_dir_list):
    """
    初始化日志目录：当目录不存在新建
    :param log_dir_list: 日志目录列表
    :return:
    """
    if isinstance(log_dir_list, str):
        log_dir_list = [log_dir_list]
    for log_dir in log_dir_list:
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)


def create_xlsx(result_file_name, title):
    """
    创建Excel文件 若存在则删除，并创建表头，设置列宽为25，
    :param result_file_name: 全路径名称
    :param title:
    :return:
    """
    if os.path.exists(result_file_name):
        os.remove(result_file_name)
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.append(title)
    # 列宽
    for j in range(1, worksheet.max_column + 1):
        worksheet.column_dimensions[get_column_letter(j)].width = 25
    xlsx_style(worksheet)
    workbook.save(result_file_name)

def xlsx_style(sheet):
    """
    设置行高和字体居中
    :param sheet: 表格对象
    :return:
    """
    alignment_center = Alignment(horizontal='center', vertical='center')
    font = Font(name="微软雅黑", size=12)
    # 行高
    for row in range(1, sheet.max_row + 1):
        sheet.row_dimensions[row].height = 18
        for col in range(1, sheet.max_column):
            sheet.cell(row, col).alignment = alignment_center
            sheet.cell(row, col).font = font

        sheet.cell(row, sheet.max_column).alignment = Alignment(horizontal='left', vertical='center')
        sheet.cell(row, sheet.max_column).font = font

def update_xlsx_row(file, row_key, new_value, sheet_name=None):
    """

    :param file: xlsx文件路径
    :param row_key: 原来的值关键字，需要是能根据一行的几个值匹配到某具体的一行 (['', 'xxxxx'], 1)
    :param new_value: 要修改的值 ('Y', 5) 或 (['kk', 'yy'], 4)
    :param sheet_name: 表名称
    :return:
    """
    c_file = load_workbook(file)
    if sheet_name:
        sheet = c_file[sheet_name]  # 当前激活的工作表
    else:
        sheet = c_file.active
    for row in range(2, sheet.max_row + 1):
        row_data = []
        for col in range(1, len(row_key[0]) + row_key[1]):
            cell = sheet.cell(row=row, column=col)
            cell_value = cell.value
            if cell_value is None:
                cell_value = ''
            row_data.append(cell_value)
        if row_data == list(row_key[0]):
            if isinstance(new_value[0], tuple):
                for col, val in enumerate(new_value[0], new_value[1]):
                    sheet.cell(row=row, column=col).value =  val
            else:
                sheet.cell(row=row, column=new_value[1]).value = new_value[0]
            break

    c_file.save(file)

def append_xlsx(file, row):
    """
    在结果文件中添加结果
    :param row: 本次添加结果
    :return:
    """
    xfile = load_workbook(file)
    sheet = xfile.active
    if isinstance(row[0], list):
        for ro in row:
            sheet.append(ro)
    else:
        sheet.append(row)
    xlsx_style(sheet)
    xfile.save(file)


def popen_cmd(cmd, stdout=True, stderr=True, process=False, out_file=None):
    if out_file:    # 输出到文件
        p = Popen(cmd, shell=True, stdout=open(out_file, mode='w', encoding='utf-8'))
        process = True
    elif not stdout:
        p = Popen(cmd, shell=True)
    else:
        if stderr:
            p = Popen(cmd, shell=True, stdout=PIPE, stderr=STDOUT)
        else:
            p = Popen(cmd, shell=True, stdout=PIPE)

    if not process:
        p = p.stdout.read().decode('utf-8', 'ignore').strip('\r\n').strip()
    return p


def time_conversion(x):
    """
    将xxx秒转化为 x分x秒 或 x时x分x秒
    :param x: 指定秒
    :return: 格式化时间字符串
    """
    if x < 60:
        return str(x) + "秒"
    elif x < 3600:
        minutes = x // 60
        seconds = x % 60
        return str(minutes) + "分" + str(seconds) + "秒"
    else:
        hours = x // 3600
        minutes = (x % 3600) // 60
        seconds = (x % 3600) % 60
        return str(hours) + "时" + str(minutes) + "分" + str(seconds) + "秒"


def zip_dir(directory, zip_file=None):
    """
    将指定目录下所有文件打包，若第二个参数为空，则打包名为指定目录中的文件夹，并放在同级目录中
    :param directory: 指定目录，可有多个：'目录1' 或 ['目录2', '目录2', '目录3']
    :param zip_file: 压缩文件的路径，xxx/xx/xx/demo.zip
    :return: 返回压缩文件的完整路径
    """
    try:
        if isinstance(directory, str):
            directory = [directory]
        if zip_file is None:
            zip_file = os.path.join(os.path.dirname(directory[0]), directory[0].split('\\')[-1] + '.zip')
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for dire in directory:
                for root, dirs, files in os.walk(dire):
                    for file in files:
                        full_path = os.path.join(root, file)
                        relative_path = os.path.relpath(full_path, os.path.dirname(dire))
                        zipf.write(full_path, arcname=relative_path)
    except Exception as e:
        print(e)
        zip_file = False
    finally:
        return zip_file


def unzip_file(zipfile_path, extract_dir=None):
    """
    解压zip包到指定目录
    :param zipfile_path:zip包路径
    :param extract_dir: 解压的目录，当为空时，解压到zip同目录下
    :return:
    """
    if not extract_dir:
        zipfile_path_dir = os.path.dirname(zipfile_path)
        zipfile_name = os.path.basename(zipfile_path)
        extract_dir = os.path.join(zipfile_path_dir, os.path.splitext(zipfile_name)[0])
    with zipfile.ZipFile(zipfile_path, 'r') as zf:
        zf.extractall(extract_dir)
    return extract_dir


def get_file_size(file):
    """
    得到文件大小，单位：MB
    :param file: 指定文件
    :return: 文件大小，MB
    """
    return os.path.getsize(file) / (1024 * 1024)


def format_files_name(dir_, log_file_name):
    """
    格式化指定目录下的指定多个文件
    如：log.log, log.log.1, log.log.2, log.log.3 ==》 log.log.1, log.log.2, log.log.3 log.log.4
    :param dir_: 指定路径
    :param log_file_name: 多个文件共有前缀，如log.log
    :return:
    """
    files = sorted([f for f in os.listdir(dir_) if f.startswith(log_file_name) and f != log_file_name],
                   key=lambda x: int(x.split('.')[-1]))
    for i in range(len(files) - 1, -1, -1):
        os.rename(os.path.join(dir_, files[i]), os.path.join(dir_, f'{log_file_name}.{i + 2}'))
    os.rename(os.path.join(dir_, log_file_name), os.path.join(dir_, f'{log_file_name}.1'))


def get_float_range(start, end, step=1):
    """
    获取两个整数之间的所有一位小数的等分间隔列表
    :param start: 开始数字
    :param end: 结束数字
    :param step: 间隔，默认是1，最小可取0.1
    :return:
    """
    return [i/10.0 for i in range(int(float(start)*10), int(float(end)*10)+1, int(float(step)*10))]


def get_time_now(time_format='second'):
    """
    以指定格式获取当前时间
    :return:
    """
    if time_format == 'second':
        tf = '%Y-%m-%d-%H-%M-%S'
    elif time_format == 'date':
        tf = '%Y-%m-%d %X'
    elif time_format == 'time':
        tf = '%X'
    elif time_format == 'set':
        tf = '%m%d%H%M%Y.%S'
    else:
        tf = '%Y-%m-%d'
    return strftime(tf, localtime())


def format_folder_name(folder_name_str):
    """
    格式化目录名称，将windows规定不能有的，以及空格和中文符号 都替换为 下划线
    :param folder_name_str:
    :return:
    """
    replace_chars = [':', '/', '?', '*', '"', '<', '>', '|', ' ', '、', '，', '。', '！', '：', '？', '（', '）', '&']
    target_char = '_'
    for char in replace_chars:
        folder_name_str = folder_name_str.replace(char, target_char)
    return folder_name_str


# 实现日志对象
class Log:
    def __init__(self, log_name=None, console_level='DEBUG', file_level='DEBUG', customer_console_format=None, customer_handler_format=None):
        self.logger = logging.getLogger(f'{token_urlsafe(10)}{get_time_now()}')
        self.logger.setLevel('DEBUG')

        # Console handler
        console = logging.StreamHandler()
        console_format = customer_console_format if customer_console_format is not None else '%(asctime)s %(levelname)s %(filename)s[%(lineno)d line]: %(message)s'
        console_formatter = logging.Formatter(console_format)
        console.setFormatter(console_formatter)
        console.setLevel(console_level)
        self.logger.addHandler(console)

        # File handler
        # maxBytes是生成单个日志文件的最大值，目前设置的是300M
        # backupCount是生成备份日志文件的最大数，目前设置的是300个
        # 生成的日志文件这样：log.log, log.log.1, log.log.2, log.log.3 。。。
        # 日志文件说明：log.log.3是生成最早的日志文件，而log.log是时间最晚的
        if log_name:    # 仅仅在有文件名称时才创建记录到文件的日志对象
            handler = RotatingFileHandler(log_name, maxBytes=300*1024*1024, backupCount=300, encoding='utf-8')
            handler_format = customer_handler_format if customer_handler_format is not None else '%(asctime)s %(levelname)s %(filename)s[%(lineno)d line]: %(message)s'
            handler_formatter = logging.Formatter(handler_format)
            handler.setFormatter(handler_formatter)
            handler.setLevel(file_level)
            self.logger.addHandler(handler)

    def get_logger(self):
        return self.logger


def read_xl(xl_file, sheet_name=None, return_dict=False):
    """
    读取excel内容，默认以二维数组的形式返回
    :param xl_file:文件名称
    :param sheet_name: 表格名称，当打开默认的表格时可为空
    :param return_dict: 是否以第一列为键，剩余列为值的字典数据返回
    :return: 返回读取到的内容
    """
    c_file = load_workbook(xl_file)
    if sheet_name:
        sheet = c_file[sheet_name]  # 当前激活的工作表
    else:
        sheet = c_file.active

    data = []
    for row in range(2, sheet.max_row + 1):
        row_data = []
        for col in range(1, sheet.max_column + 1):
            cell = sheet.cell(row=row, column=col)
            if isinstance(cell, MergedCell):  # 判断该单元格是否为合并单元格
                for merged_range in sheet.merged_cells.ranges:  # 循环查找该单元格所属的合并区域
                    if cell.coordinate in merged_range:
                        # 获取合并区域左上角的单元格作为该单元格的值返回
                        cell = sheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                        break
            cell_value = cell.value
            if cell_value:
                row_data.append(cell_value)
            else:
                row_data.append('')
        data.append(row_data)

    if return_dict:
        data_dict = {}
        for data_item in data:
            data_dict[data_item[0]] = data_item[1:]
        data = data_dict
    return data


def read_yaml(yaml_file):
    """
    读取yml文件的内容
    :param yaml_file: yml文件或对象
    :return:
    """
    if isinstance(yaml_file, bytes):
        return yaml.load(yaml_file, Loader=yaml.FullLoader)
    with open(yaml_file, "r", encoding="utf-8") as f:
        data = yaml.load(f, Loader=yaml.FullLoader)
        return data

def pic_to_py(picture_names, py_name):
    """
    将图像文件转换为py文件
    :param py_name: py文件名称
    :param picture_names: 图像名称列表
    :return:
    """
    write_data = []
    if isinstance(picture_names, str):
        picture_names = [picture_names]
    for picture_name in picture_names:
        filename = picture_name.replace('\\', '/').split('/')[-1].replace('.', '_')
        open_pic = open("%s" % picture_name, 'rb')
        b64str = base64.b64encode(open_pic.read())
        open_pic.close()
        # 注意这边b64str一定要加上.decode()
        write_data.append('%s = "%s"\n' % (filename, b64str.decode()))

    f = open('%s.py' % py_name, 'w+')
    for data in write_data:
        f.write(data)
    f.close()


def time_str_to_stamp(time_str):
    # 将时间字符串转换为datetime对象
    # time_str = '2023-12-11 08:47:45'
    time_obj = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    # 将datetime对象转换为时间戳
    return int(mktime(time_obj.timetuple()))


def keyword_match(keyword, item, fuzzy):
    """
    对比关键字和字符串
    """
    if fuzzy:
        find_res = keyword.lower() in item.lower()
    else:
        find_res = keyword == item
    return find_res


def get_dict_value_in_key(dict_data, build_name, default=None):
    """
    判断build_name是否在dict_data的key中，如果存在则返回对应的value
    :dict_data: key各个项须以英文分号隔开
    :build_name:
    :return: value
    """
    res = default
    for key, value in dict_data.items():
        if build_name in key.split(';'):
            res = value
            break
    return res


# 定义一个新的异常，用于结束测试的异常
class EndTestException(Exception):
    def __init__(self, error_info):
        super().__init__(self)
        self.error_info = error_info

    def __str__(self):
        return str(self.error_info)

