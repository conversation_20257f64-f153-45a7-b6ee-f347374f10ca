import unittest
import os
from random import choice
from time import sleep

from script.testcases_ww.params import box_device, youtube_4k_video, not_support_4k_device
from script.testcases_ww.simple_adb import *


class UATLongPlay(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = SimapleADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def long_play_uat(self, play_type):
        for i in range(1, 61):
            self.device.logger.info(f'{play_type}长播循环：{i}')
            sleep(30)

    def test_hdmi_long_play_uat(self):
        """
        HDMI源长播
        """
        if self.device.build_name not in box_device:

            self.long_play_uat('HDMI')
        else:
            resolution_steps = 6 if self.device.build_name in not_support_4k_device else 8
            self.device.logger.info(f'设备{"不" if self.device.build_name in not_support_4k_device else ""}支持4k，故分辨率菜单有{resolution_steps}项')
            for i in range(resolution_steps):
                self.device.logger.info('播放YouTube 4k视频')
                self.device.open_app(youtube_4k_video, sleep_time=30)
                self.device.logger.info('恢复分辨率为Auto')
                self.device.center(3)
                self.device.up(2)
                self.device.right(1, 8)
                self.device.center(3, 2)
                self.device.up(1, resolution_steps)
                self.device.center(5)
                self.device.back(10)
                # 等待YouTube菜单消失后继续
                self.device.logger.info('修改分辨率')
                self.device.center(3)
                self.device.up(2)
                self.device.right(1, 8)
                self.device.center(3, 2)
                self.device.down(2, i + 1)
                self.device.center(5)
                self.device.back(3)
                self.device.logger.info('修改分辨率后播放10分钟')
                sleep(600)

    def test_av_long_play_uat(self):
        """
        AV源长播
        """
        self.long_play_uat('AV')

    def test_live_tv_long_play_uat(self):
        """
        LiveTV长播
        """
        self.long_play_uat('LiveTV')

    def test_YouTube_long_play_uat(self):
        """
        连接蓝牙音响长播YouTube
        """
        self.long_play_uat('YouTube')

    def test_miracast_long_play_uat(self):
        """
        Miracast投屏长播
        """
        self.long_play_uat('Miracast')

    def test_chromecast_long_play_uat(self):
        """
        chromecast长播
        """
        self.long_play_uat('Chromecast')
