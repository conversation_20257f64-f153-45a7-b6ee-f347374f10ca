import os
import unittest
from os import times

from script.testcases_ww.params import box_device, mtbf_loop_times
from script.testcases_ww.ui_adb import *


class Picture(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def launch_settings_display(self):
        self.assertTrue(self.device.enter_settings_path('Display', True), 'Not Found Display & Sound')

    def test_picture(self):
        """
        遍历Picture菜单
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Settings-Display-Picture，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.device.logger.info('进入Picture菜单')
            self.assertTrue(self.device.enter_settings_path('Picture', True, False), 'Not found Picture')

            self.device.logger.info('点击Picture Mode')
            self.assertTrue(
                self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Picture', True),
                '未在Picture菜单中找到Picture Mode')
            self.device.center(5)
            self.device.back(2)

            self.device.logger.info('点击Backlight')
            self.assertTrue(
                self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Backlight', True),
                '未在Picture菜单中找到Backlight')
            self.device.center(5)
            self.device.left(1, 50)
            self.device.right(1, 30)
            self.device.back(2)

            self.device.logger.info('点击Basic Video')
            self.assertTrue(
                self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Basic', True),
                '未在Picture菜单中找到Basic Video')
            self.device.center(5)
            self.device.down(2, 10)
            self.device.back(2)

            self.device.logger.info('点击Advanced settings')
            self.assertTrue(
                self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Advanced', True),
                '未在Picture菜单中找到Advanced settings')
            self.device.center(5)
            self.device.down(2, 7)
            self.device.back(2)

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Ambient', True):
                self.device.logger.info('点击Ambient light detection')
                self.device.center(2)
                self.device.down(2)
                self.device.center(2, 3)

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Global', True):
                self.device.logger.info('点击Global dimming菜单')
                self.device.center(2)
                self.device.down(2)
                self.device.center(2, 3)

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Local', True):
                self.device.logger.info('Local dimming菜单')
                self.device.center(2)
                self.device.down(2)
                self.device.center(2, 3)

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Intelligent', True):
                self.device.logger.info('点击Intelligent switch菜单')
                self.device.center(2)
                self.device.down(2)
                self.device.center(2, 3)

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'AIPQ', True):
                self.device.logger.info('点击AIPQ菜单')
                self.device.center(2)
                self.device.down(2)
                self.device.center(2, 3)

            self.device.logger.info('点击Reset Current Picture')
            self.assertTrue(
                self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'Reset', True),
                '未在Picture菜单中找到Reset Current Picture')
            self.device.center(5)
            self.device.down(1, 2)
            self.device.right(2)
            self.device.center(2)
            self.device.back(1, 2)

    def test_sound(self):
        """
        遍历Sound菜单
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持Settings-Display-Sound，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.device.logger.info('进入Sound菜单')
            self.assertTrue(self.device.enter_settings_path('Sound', True, False), 'Not found Sound')

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'DTS ', True):
                self.device.logger.info('点击DTS Virtual:X 菜单')
                self.device.center(2)
                self.device.center(2, 2)
                self.device.down(2)
                self.device.center(2, 2)
                self.device.down(2)
                self.device.center(2, 2)
                self.device.back(2)

            self.device.change_sound_mode()

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'leveler', True):
                self.device.logger.info('点击Volume Leveler菜单')
                self.device.center(2, 2)

            if self.device.find_item(settings_list_resource_id, settings_title_resource_id, 'DAC', True):
                self.device.logger.info('点击DAC-4 dialogue enhancer菜单')
                for i in range(4):
                    self.device.center(2)
                    self.device.down(1, i)
                    self.device.center(2)
                self.device.center(2, 2)
            self.device.back(2, 2)

    def test_box_display_text_scaling(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.assertTrue(self.device.enter_settings_path('Text scaling', True, False), 'Not Found Text scaling')
            for i in range(3):
                self.device.center(5)
                self.device.down(1)
            self.device.up(1, 3)
            self.device.center(3)

    def test_box_display_resolution(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.assertTrue(self.device.enter_settings_path('Resolution', True, False), 'Not Found Resolution')
            for i in range(20):
                self.device.down(1)
                self.device.center(8)
                self.device.center(3)
            self.device.up(1, 21)
            self.device.center(8, 2)

    def test_box_display_dynamic_range(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.assertTrue(self.device.enter_settings_path('Dynamic range', True, False), 'Not Found Dynamic range & Color format')
            if self.device.d(text='Match content dynamic range').exists:
                self.device.logger.info('遍历Match content dynamic range')
                for i in range(2):
                    self.device.center(3)
                    self.device.down(2, i)
                    self.device.center(5)
                self.device.logger.info('恢复Match content dynamic range默认选项')
                self.device.center(3)
                self.device.up()
                self.device.center(5)
            if self.device.d(text='Dolby Vision Processing').exists:
                self.device.logger.info('遍历Dolby Vision Processing')
                self.device.down(2)
                for i in range(2):
                    self.device.center(3)
                    self.device.down(2, i)
                    self.device.center(5)
                self.device.logger.info('恢复Dolby Vision Processing默认选项')
                self.device.center(3)
                self.device.up()
                self.device.center(5)

    def test_box_display_match_content(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.assertTrue(self.device.enter_settings_path('Match content frame rate', True, False), 'Not Found Match content frame rate')
            for i in range(2):
                self.device.down(1)
                self.device.center(3)
            self.device.up(1, 2)
            self.device.center(3)

    def test_box_display_advanced_display(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.assertTrue(self.device.enter_settings_path('Advanced display settings', True, False), 'Not Found Advanced display settings')
            self.device.logger.info('遍历Preferred dynamic range')
            self.device.center(3)
            for i in range(2):
                self.device.down(1)
                self.device.center(5)
            self.device.logger.info('恢复Preferred dynamic range默认选项')
            self.device.up(1, 2)
            self.device.center(5)

            self.device.logger.info('开关Allow game mode')
            self.device.back(3)
            self.device.down(2)
            self.device.center(5, 2)

            self.device.down(2)
            self.device.center(2)
            self.device.logger.info('遍历Format selection')
            self.device.logger.info('设置Manual')
            self.device.down(2)
            self.device.center(3)
            for i in range(2):
                self.device.down()
                self.device.center(5, 2)
            self.device.logger.info('恢复Format selection默认选项')
            self.device.up(2, 3)
            self.device.center(3)

    def test_box_display_advanced_sound(self):
        if self.device.build_name not in box_device:
            self.skipTest('仅盒子功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.launch_settings_display()
            self.assertTrue(self.device.enter_settings_path('Advanced sound settings', True, False), 'Advanced sound settings')
            self.device.logger.info('遍历Sound Select formats')
            for i in range(2):
                self.device.center(3)
                self.device.down(1)
                self.device.center(3)
            self.device.logger.info('恢复Sound Select formats默认值')
            self.device.center(3)
            self.device.up(1, 2)
            self.device.center(3)

            self.device.logger.info('遍历DAC-4 dialogue enhancer')
            self.device.down(2)
            for i in range(3):
                self.device.center(3)
                self.device.down(1)
                self.device.center(3)
            self.device.logger.info('恢复DAC-4 dialogue enhancer默认值')
            self.device.center(3)
            self.device.up(1, 3)
            self.device.center(3)
