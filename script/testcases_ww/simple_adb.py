import os
from threading import Thread
from time import sleep, time, strftime

from script.testcases_ww.common import Log, EndTestException, get_time_now, popen_cmd
from script.testcases_ww.params import screen_cap_folder, check_input_id_cmd

not_root_list = ['ermission Denial', 'inaccessible or not found', 'ermission denied']

# 等待设备进入系统时间或连接时间，默认15分钟，若未仍未完成操作则报错
wait_device_connect_time = 900
# 设置重启、Reset的最大尝试次数
reset_retry_max_times = 10
# 等待进入重启、Reset的最大时间，默认2分钟，若未检测到重试，重试几次后若还是无法进入，则报错
wait_device_disconnect_time = 120
# 获取屏幕当前聚焦窗口
current_focused_window = "dumpsys window | grep mFocusedWindow | sed 's/}//g' | awk '{print $3}'"
# Reset页面
reset_page = 'com.android.tv.settings/com.android.tv.settings.device.storage.ResetActivity'
# google 语音
google_voice_page = 'com.google.android.katniss/com.google.android.apps.tvsearch.app.launch.trampoline.SearchActivityTrampoline'
# 查看是否静音
get_audio_mute_status = "dumpsys audio | grep -A 1 'STREAM_MUSIC:' | grep 'Muted:'"
# 关闭自动同步时间
close_auto_time = 'settings put global auto_time 0'
# ping Wi-Fi的IP地址
ping_wifi_cmd = "ping -c 5 $(ip addr | grep 'scope global wlan0' | sed 's/\\// /g' | awk '{print $2}')"
# Wi-Fi ping通的关键字
ping_wifi_success = '64 bytes from'
# settings页面
settings_page = 'com.android.tv.settings/com.android.tv.settings.MainSettings'
# LiveTV设置广播
live_tv_settings_broadcast = 'mitv.globalkey.KEYCODE_SETTINGS'

# 设置指定电视设备
# os.environ["ANDROID_SERIAL"] = '8870EC6FA0680800F5F2'


class SimapleADB:
    def __init__(self, sn, log_name=None, console_level='DEBUG', file_level='DEBUG'):
        self.sn = sn
        self.logger = Log(log_name=log_name, console_level=console_level, file_level=file_level).get_logger()
        if sn:
            if '.' in sn:
                self.send_cmd(f'adb connect {sn}')
            if 'ro.build.product' not in self.send_cmd(f'adb -s {sn} shell getprop | grep ro.build.product'):
                raise EndTestException('设备连接失败')
            self.send_adb('root')
            self.real_sn = self.get_real_sn()
            self.version_number = self.get_version_number()
            self.version_type = self.get_version_type()
            self.model_name = self.get_model_name()
            self.product_name = self.get_product_name()
            self.build_name = self.get_build_name()
            self.is_gtv = self.get_is_gtv()
            self.is_mtk = self.get_is_mtk()
            self.wifi_chip = self.get_wifi_chip()
            self.rom_version = self.get_rom_version()

    def write_cmds(self, cmds):
        if isinstance(cmds, list):
            for cmd in cmds:
                if isinstance(cmd, str):
                    self.send_adb(cmd)
                else:
                    if len(cmd) == 1:
                        self.send_adb(cmd[0])
                    elif len(cmd) == 2:
                        self.send_adb(cmd[0])
                        sleep(cmd[1])

    def send_cmd(self, cmd, stdout=True, stderr=True, process=False, out_file=None):
        p = popen_cmd(cmd, stdout=stdout, stderr=stderr, process=process, out_file=out_file)
        self.logger.debug(f'执行命令: [{cmd}], 结果: [{p}]')
        return p

    def send_adb(self, cmd, root=False, stdout=True, stderr=True, process=False, sn=None, out_file=None):
        """
        执行adb命令：
        当存在权限问题时先adb root
        当offline时，先reconnect offline
        :param cmd: adb命令
        :param root: 是否先执行root
        :param stderr:
        :return:执行结果
        """
        curr_sn = sn if sn else self.sn
        root_cmd = f'adb -s {curr_sn} root'

        if cmd in ('su', 'root') or root:
            self.send_cmd(root_cmd)
            if cmd in ('su', 'root'):
                return

        if not cmd.startswith('adb '):
            cmd = f'adb -s {curr_sn} shell {cmd}'

        # 当只获取执行的process对象时，先执行ls以检查设备连接状态，否则会导致在设备连接有问题的情况下执行process持续报错
        if (process or out_file) and 'ro.build.product' in self.send_adb('getprop | grep ro.build.product'):
            return self.send_cmd(cmd, stdout=stdout, stderr=stderr, out_file=out_file, process=process)

        start_time = time()
        already_connected = False
        while True:
            adb_res = self.send_cmd(cmd, stdout=stdout, stderr=stderr)
            if any(line in adb_res for line in not_root_list):
                root_res = self.send_cmd(root_cmd)
                if 'restarting adbd as root' in root_res:
                    self.logger.debug('设备root成功')
                    adb_res = self.send_cmd(cmd, stdout=stdout, stderr=stderr)

            # adb_temp = ''
            res_list = adb_res.split('\r\n')
            if any(curr_sn in line and 'not found' in line for line in res_list):
                adb_temp = self.send_cmd(f'adb connect {curr_sn}')
            elif any(item in adb_res for item in
                     [f'{curr_sn}\toffline', 'adb.exe: device offline', 'error: device offline', 'error: closed']):
                if 'error:closed' not in adb_res:
                    self.send_cmd('adb reconnect offline')
                if already_connected:
                    self.send_cmd(f'adb disconnect {curr_sn}')
                adb_temp = self.send_cmd(f'adb connect {curr_sn}')
            elif 'daemon not running' in adb_res:  # 增加adb异常后重启的关键字捕获
                continue
            else:
                return adb_res

            if f'already connected to {curr_sn}' in adb_temp:
                already_connected = True
            else:
                already_connected = False

            if time() - start_time > wait_device_connect_time:
                raise EndTestException(f'等待{wait_device_connect_time / 60:.1f}分钟未检测到设备')
            self.logger.debug('等待设备连接中')
            sleep(10)

    def get_devices_list(self):
        device_list = []
        adb_devices = self.send_cmd('adb devices')
        device_flag = False
        for line in adb_devices.split('\r\n'):
            if 'List of devices attached' == line:
                device_flag = True
                continue
            if device_flag:
                device_sn, device_info = line.split()
                device_name = self.get_model_name(device_sn) if device_info == 'device' else f'{device_info}!!!'
                if not device_name:
                    device_name = self.get_product_name(device_sn)
                device_list.append(f'{device_sn}--{device_name}')
        return device_list

    def get_product_name(self, sn=None):
        return self.send_adb('getprop ro.product.name', sn=sn).capitalize()

    def get_build_name(self):
        return self.send_adb('getprop ro.build.product')

    def get_model_name(self, sn=None):
        mn = self.send_adb('getprop ro.boot.assm_mn', sn=sn)
        return mn[:4] if mn else self.send_adb('getprop ro.boot.model_name', sn=sn).upper()

    def get_version_number(self):
        return self.send_adb('getprop ro.build.version.incremental')

    def get_version_type(self):
        return self.send_adb('getprop ro.build.type')

    def get_wifi_chip(self):
        return self.send_adb('getprop wlan.wifi_chip')

    def get_rom_version(self):
        return self.send_adb('getprop ro.build.software.version')

    def get_is_gtv(self):
        return True if self.send_adb('getprop vendor.mitv.gtv') else False

    def get_is_mtk(self):
        return False if self.send_adb('getprop | grep aml') else True

    def get_real_sn(self):
        return self.send_adb('getprop ro.boot.serialno')

    def get_package_version(self, package):
        """
        得到指定包的版本号，当该包不存在时返回空
        :param package: 指定包名
        :return:
        """
        if package and package not in self.send_adb(f'pm list package | grep {package}'):
            return ''
        return self.send_adb(f"dumpsys package {package} | grep versionName | awk -F '=' '{{print $2}}'")

    def get_device_info(self):
        return {
            'product': self.product_name,
            'build': self.build_name,
            'real_sn': self.real_sn,
            'model': self.model_name,
            'version_num': self.version_number,
            'version_type': self.version_type,
            'version': f'R{self.version_number}_{self.version_type}',
            'wb': self.wifi_chip,
            'rom_version': self.rom_version,
            'is_gtv': self.is_gtv,
            'is_mtk': self.is_mtk
        }

    def check_keyword(self, keyword, cmd, timeout):
        def find_keyword():
            nonlocal is_find, process
            try:
                with process:
                    sleep_lines = 100  # 每查询100行，睡眠0.1秒，以防止cpu阻塞问题
                    for cc in process.stdout:
                        c = cc.decode('utf-8', 'ignore').strip('\r\n').strip()
                        self.logger.debug(c)
                        if keyword in c:
                            is_find = True
                            break
                        sleep_lines -= 1
                        if sleep_lines == 0:
                            sleep_lines = 100
                            sleep(0.1)
            except Exception as e:
                self.logger.error(f'读取进程输出时发生错误')
                self.logger.exception(e)

        is_find = False
        start_time = time()
        try:
            process = self.send_adb(cmd, process=True)
            Thread(target=find_keyword, args=()).start()
            sleep(3)
            while time() - start_time < timeout and not is_find:
                if process.poll() is None:
                    self.logger.debug('命令执行中，10秒后再次检查')
                    sleep(10)
                else:
                    self.logger.debug('命令执行已结束，重新执行')
                    process.wait()
                    process = self.send_adb(cmd, process=True)
                    Thread(target=find_keyword, args=()).start()
                    sleep(3)
            if process.poll() is None:
                process.terminate()
        except Exception as e:
            self.logger.debug('执行命令时发生错误')
            self.logger.exception(e)
        return is_find

    def check_device_power(self, msg):
        sleep(5)
        devices_list = self.send_adb('adb devices')
        if self.sn not in devices_list or f'{self.sn}:5555\toffline' in devices_list or f'{self.sn}\toffline' in devices_list:
            self.logger.debug(f'设备进入{msg}状态...')
            return True
        else:
            self.logger.debug(f'等待设备进入{msg}...')
            return False

    def reboot(self):
        retry_times = reset_retry_max_times
        while retry_times > 0:
            self.logger.debug(f'重启电视')
            retry_times -= 1
            enter_reboot = False
            self.send_adb('setprop sys.powerctl reboot')
            start_reboot = time()
            while time() - start_reboot < wait_device_disconnect_time:
                if self.check_device_power('重启'):
                    enter_reboot = True
                    break
            if enter_reboot:
                break
            else:
                self.logger.debug('重启失败，再次尝试')
        else:
            raise Exception('重启失败')
        return self.check_boot_complete()

    def check_boot_complete(self):
        """
        检查设备是否已boot完成，若15分钟还未完成，说明设备出现问题
        :return: boot完成返回True，失败返回False
        """
        start_time = time()
        while True:
            boot_flag = self.send_adb('getprop dev.bootcomplete')
            if boot_flag == '1':
                self.logger.debug('boot完成，进入系统')
                sleep(10)
                boot_complete = True
                break
            elif not boot_flag:
                self.logger.debug('设备已连接，进入系统中...')
                sleep(10)

            if time() - start_time > 1800:
                raise Exception('已检测到设备，但30分钟仍未进入系统 ')
            sleep(3)
        return boot_complete

    def get_page(self):
        """
        获取当前页面包名/活动
        @return: package/activity
        """
        curr_page = self.send_adb(current_focused_window)
        if len(curr_page.split()) > 1:
            curr_page = curr_page.split()[0]
        self.logger.debug(f'当前页面为：{curr_page}')
        return curr_page

    def reset(self):
        """
        对设备进行Reset操作，流程：
        先打开reset页面 -> 点击Reset -> 点击Erase everything
        :return:
        """
        retry_times = reset_retry_max_times
        while retry_times > 0:
            retry_times -= 1
            enter_reset = False
            self.send_adb(f'am start -S "{reset_page}"', root=True)  # 打开reset页面
            sleep(3)
            if reset_page == self.get_page():
                self.logger.debug('成功进入Reset页面')
                self.down(2)
                self.center(2)
                self.logger.debug('按下Reset')
                self.down(2)
                self.center(2)
                self.logger.debug('按下Erase everything')
                start_reset = time()
                while time() - start_reset < wait_device_disconnect_time:
                    if self.check_device_power('Reset'):
                        enter_reset = True
                        break
                if enter_reset:
                    break
                else:
                    self.logger.debug('Reset失败，再次尝试')
            else:
                self.back()
        else:
            raise EndTestException('Reset失败')
        return self.check_boot_complete()

    def check_wifi(self):
        if '.' in self.sn:
            self.logger.debug('当前通过网络连接设备，跳过网络检测')
            return True
        wifi_res = self.send_adb(ping_wifi_cmd)
        for i in range(60):
            if ping_wifi_success in wifi_res:
                self.logger.debug('Wi-Fi回连成功')
                return True
            else:
                self.logger.debug(f'Wi-Fi回连失败，再次等待连接_{i}')
                sleep(5)
                wifi_res = self.send_adb(ping_wifi_cmd)
        else:
            self.logger.debug('Wi-Fi回连失败')
            return False

    def set_time_now(self):
        self.logger.debug('设置系统时间为当前时间')
        self.send_adb(close_auto_time, True)
        self.send_adb(f'date {get_time_now("set")} set', True)
        sleep(5)

    # 封装上下左右等按键操作，两个参数：第一个是按键后等待时间，第二个是按几次
    def down(self, sleep_time=1, times=1):
        self.logger.info(f'DOWN--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_DPAD_DOWN')
            sleep(sleep_time)

    def up(self, sleep_time=1, times=1):
        self.logger.info(f'UP--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_DPAD_UP')
            sleep(sleep_time)

    def left(self, sleep_time=1, times=1):
        self.logger.info(f'LEFT--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_DPAD_LEFT')
            sleep(sleep_time)

    def right(self, sleep_time=1, times=1):
        self.logger.info(f'RIGHT--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_DPAD_RIGHT')
            sleep(sleep_time)

    def center(self, sleep_time=1, times=1):
        self.logger.info(f'CENTER--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_DPAD_CENTER')
            sleep(sleep_time)

    def back(self, sleep_time=1, times=1):
        self.logger.info(f'BACK--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_BACK')
            sleep(sleep_time)

    def del_key(self, times=1):
        self.logger.info(f'DEL--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_DEL')

    def home(self, sleep_time=3, times=1):
        self.logger.info(f'HOME--sleep_time={sleep_time}--times={times}')
        for i in range(times):
            self.send_adb('input keyevent KEYCODE_HOME')
            sleep(sleep_time)

    def epg(self, sleep_time=5):
        self.logger.info(f'EPG--sleep_time={sleep_time}')
        self.send_adb('input keyevent 172')

    def power(self, sleep_time=3):
        self.logger.info(f'POWER')
        self.send_adb('input keyevent KEYCODE_POWER')
        sleep(sleep_time)

    def power_menu(self):
        self.logger.info(f'POWER MENU')
        self.send_adb('input keyevent --longpress KEYCODE_POWER')
        sleep(3)

    def dashboard(self):
        self.logger.info(f'DASHBOARD')
        self.send_adb('input keyevent --longpress KEYCODE_HOME')
        sleep(3)

    def volume_down(self, step=1):
        self.logger.info(f'VOLUME_DOWN---step={step}')
        for i in range(step):
            self.send_adb('input keyevent KEYCODE_VOLUME_DOWN')

    def volume_up(self, step=1):
        self.logger.info(f'VOLUME_UP---step={step}')
        for i in range(step):
            self.send_adb('input keyevent KEYCODE_VOLUME_UP')

    def volume_adjust(self, type_='up', step=10):
        """
        调节音量
        @params type_: 调整方向，up为调高，down为调低
        @params step: 调整步长，默认10
        """
        if type_ == 'up':
            self.volume_up(step)
        else:
            self.volume_down(step)

    def mute(self, sleep_time=3):
        if 'true' in self.send_adb(get_audio_mute_status):
            self.logger.info('当前设备已静音')
        else:
            self.logger.info('静音！')
            self.send_adb('input keyevent KEYCODE_VOLUME_MUTE')
            sleep(sleep_time)

    def input_text(self, string):
        self.send_adb(f"input text '{string}'")

    def open_app(self, app_page, type_='n', sleep_time=5):
        # -f 0x04000000 使用已有的活动，不新建活动
        self.logger.info(f'打开app: {app_page}')
        open_cmd = app_page if app_page.startswith('am start') else f'am start -{type_} {app_page} -f 0x04000000'
        self.send_adb(open_cmd)
        sleep(sleep_time)

    def broadcast_start(self, broadcast, sleep_time=5):
        self.send_adb(f'am broadcast -a {broadcast}')
        sleep(sleep_time)

    def open_live_tv_settings(self):
        self.broadcast_start(live_tv_settings_broadcast)

    def google_search_text(self, text):
        self.open_app(google_voice_page, sleep_time=6)  # 唤起并等待出现搜索图标
        self.left(2)    # 使光标聚焦在键盘上
        self.center(2)
        self.input_text(text)   # 输入要搜索的文字
        # 以下三行：按下键盘上的搜索键后等待10秒
        self.down(1, 3)
        self.left(2)
        self.center(10)

    def google_voice_up_down(self, is_pause=False):
        self.logger.info('唤起Google语音助手')
        self.open_app(google_voice_page, sleep_time=6)  # 唤起并等待出现搜索图标
        self.back(10)
        if is_pause:
            self.center(3)

    def screen_cap(self, times=2, timeout=10):
        self.logger.info(f'截图{times}次，间隔{timeout}秒')
        self.send_adb('mkdir {}'.format(screen_cap_folder))
        temp_time = strftime("%Y%m%d-%H%M%S")
        for i in range(1, times + 1):
            self.send_adb(f'screencap -p {screen_cap_folder}/{temp_time}_{i}.png')
            sleep(timeout)

    def get_input_id(self):
        return self.send_adb(check_input_id_cmd)


if __name__ == '__main__':
    pass
