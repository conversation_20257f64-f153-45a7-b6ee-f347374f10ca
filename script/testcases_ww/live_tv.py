import json
import os
import unittest
from time import time

from script.testcases_ww.params import box_device, mtbf_loop_times, live_tv_page, live_tv_ok_resource_id
from script.testcases_ww.ui_adb import *

# 进入TV源的命令
to_tv = 'android.intent.action.VIEW -d content://android.media.tv/channel'
scan_page = 'com.mitv.setup/com.mitv.setup.auto.ScanActivity'
# 扫台结果列表resource id
scan_result_list_resource_id = 'com.mitv.setup:id/ll_search_result'
# 扫台最大时间/分钟
scan_timeout = 5
# 切台步数
switch_step = 10
# 切台时单个频道播放时间/秒
switch_play_time = 15


class LiveTV(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.device = UiADB(os.getenv('ANDROID_SERIAL'), console_level='INFO')

    def setUp(self):
        self.device.back(2, 5)
        self.device.home(3)

    def open_to_tv(self):
        self.device.open_app(to_tv, 'a', sleep_time=10)
        if self.device.d(resourceId=live_tv_ok_resource_id, text='OK').exists:
            self.device.center(5)

    def to_scan_page(self):
        # 在设置中去进入扫台界面
        # self.assertTrue(
        #     self.device.enter_settings_path(['Channels', 'Channels', 'Channel scan'], True),
        #     '进入LiveTV扫台失败')

        # # 从LiveTV菜单中去进入扫台界面
        # self.open_to_tv()
        # self.device.open_live_tv_settings()
        # self.assertTrue(self.device.click_list_text('Channel scan', 3, parent_step=3, sub_class=relative_class), '点击Channel scan失败')

        # 直接通过命令进入扫台界面
        self.open_to_tv()
        self.device.open_app(scan_page)

    def test_open_live_tv(self):
        """
        打开退出LiveTV
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.device.logger.info('进入LiveTV app')
            self.open_to_tv()
            self.device.logger.info('退出LiveTV')
            self.device.home(3)

    def test_scan_switch_uat(self):
        """
        LiveTV扫台切台
        """
        scan_type = os.getenv('SCAN_TYPE')
        switch_type = os.getenv('SWITCH_TYPE')
        if switch_type not in ['up_down', 'epg']:
            raise Exception('扫台参数[switch_type]错误')
        self.device.logger.info(f'扫台类型：{scan_type}，切台方式：{switch_type}')
        for i in range(5):
            self.device.logger.info(f'循环---{i}')
            self.to_scan_page()
            if scan_type.lower() in ['t', 'isdb', 'atsc-air']:
                if self.device.d(text='Antenna').exists:
                    self.device.click_list_text('Antenna')
                elif self.device.d(text='Air').exists:
                    self.device.click_list_text('Air')
                else:
                    raise Exception('请检查扫台方式是否正确')
            elif scan_type.lower() in ['c', 'atsc-c']:
                if self.device.d(text='Cable').exists:
                    self.device.click_list_text('Cable')
            elif scan_type.lower() == 's':
                if self.device.d(text='Advanced options').exists:
                    self.device.click_list_text('Advanced options')
                if self.device.d(text='Satellite').exists:
                    self.device.click_list_text('Satellite')
                else:
                    raise Exception('请检查扫台方式是否正确')
                self.device.click_list_text('Fixed antenna or DiSEqC')
                if self.device.d(text='Genaral satallites').exists:
                    self.device.click_list_text('Genaral satallites')
                self.device.click_list_text('Start')
            else:
                raise Exception('扫台参数[scan_type]错误')
            # 当个别产品的 T 和 C时，可能在点击第一层菜单后会出现Digital选项
            # 并且当没有Digital 只有Analog时，说明该机器在该制式中只有ATV扫台，报错
            if self.device.d(text='Digital').exists:
                self.device.click_list_text('Digital')
            elif self.device.d(text='Analog').exists:
                raise Exception(f'此机器在{scan_type}下只支持Analog扫台')
            # 判断是否进入扫台状态
            if self.device.d(resourceId=scan_result_list_resource_id).exists:
                self.device.logger.info('扫台进行中。。。')
            start_time = time()
            scan_res = False
            while time() - start_time < scan_timeout * 60:
                if self.device.d(text='Close').exists:
                    self.device.logger.info('扫台结束')
                    scan_res = True
                    self.device.center(3)
                    break
                sleep(20)
            self.device.home()
            if not scan_res:
                self.device.logger.info(f'{scan_timeout}分钟未扫台结束，跳过切台步骤')
            else:
                self.device.logger.info('开始进行切台')
                self.open_to_tv()
                if switch_type.lower() == 'up_down':
                    self.device.down(switch_play_time, switch_step)
                    self.device.up(switch_play_time, switch_step)
                elif switch_type.lower() == 'epg':
                    self.device.epg()
                    for _ in range(switch_step):
                        self.device.down(1)
                        self.device.center(switch_play_time)
                    for _ in range(switch_step):
                        self.device.up(1)
                        self.device.center(switch_play_time)
                self.device.home(3)

    def test_channel_setup(self):
        """
        频道初始化
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.device.logger.info('进入LiveTV app')
            self.open_to_tv()
            self.device.logger.info('点击频道初始化')
            self.device.d(text='Channel setup').click()
            self.device.center(60 * 2)

    def test_scan_t2(self):
        """
        T2 channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Antenna').exists:
                self.skipTest('扫台列表无Antenna，故跳过')
            self.device.logger.info('点击Antenna扫台')
            self.assertTrue(self.device.click_list_text('Antenna', 60 * 2), '点击Antenna失败')
            self.device.center(3)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_c(self):
        """
        Cable channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Cable').exists:
                self.skipTest('扫台列表无Cable，故跳过')
            self.device.logger.info('点击Cable扫台')
            self.assertTrue(self.device.click_list_text('Cable', 60 * 2), '点击Cable失败')
            self.device.center(3)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_s(self):
        """
        Satellite channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Satellite').exists:
                self.skipTest('扫台列表无Satellite，故跳过')
            self.device.logger.info('点击Satellite扫台')
            self.assertTrue(self.device.click_list_text('Satellite', 2), '点击Satellite失败')
            self.device.center(2, 2)
            sleep(60 * 5)
            self.device.center(3)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_advance_t2_analog(self):
        """
        Advance T2 Analog channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Advanced options').exists:
                self.skipTest('扫台列表无Advanced options，故跳过')
            self.device.logger.info('点击Advanced options')
            self.assertTrue(self.device.click_list_text('Advanced options', 2), '点击 Advanced options失败')
            self.assertTrue(self.device.click_list_text('Antenna', 2), '点击Antenna失败')
            self.assertTrue(self.device.click_list_text('Analog', 60 * 3), 'Analog')
            self.device.center(2)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_advance_t2_digital(self):
        """
        Advance T2 Digital channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Advanced options').exists:
                self.skipTest('扫台列表无Advanced options，故跳过')
            self.device.logger.info('点击Advanced options')
            self.assertTrue(self.device.click_list_text('Advanced options', 2), '点击Advanced options失败')
            self.assertTrue(self.device.click_list_text('Antenna', 2), '点击Antenna失败')
            self.assertTrue(self.device.click_list_text('Digital', 60 * 2), '点击Digital失败')
            self.device.center(2)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_advance_t2_all(self):
        """
        Advance T2 Digital and analog channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Advanced options').exists:
                self.skipTest('扫台列表无Advanced options，故跳过')
            self.device.logger.info('点击Advanced options')
            self.assertTrue(self.device.click_list_text('Advanced options', 2), '点击Advanced options失败')
            self.assertTrue(self.device.click_list_text('Antenna', 2), '点击Antenna失败')
            self.assertTrue(self.device.click_list_text('Digital and analog', 60 * 5), '点击Digital and analog失败')
            self.device.center(2)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_advance_c_analog(self):
        """
        Advance C Analog channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Advanced options').exists:
                self.skipTest('扫台列表无Advanced options，故跳过')
            self.device.logger.info('点击Advanced options')
            self.assertTrue(self.device.click_list_text('Advanced options', 2), '点击Advanced options失败')
            self.assertTrue(self.device.click_list_text('Cable', 2), '点击Cable失败')
            self.assertTrue(self.device.click_list_text('Analog', 60 * 3), '点击Analog失败')
            self.device.center(2)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_advance_c_digital(self):
        """
        Advance C Digital channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Advanced options').exists:
                self.skipTest('扫台列表无Advanced options，故跳过')
            self.device.logger.info('点击Advanced options')
            self.assertTrue(self.device.click_list_text('Advanced options', 2), '点击Advanced options失败')
            self.assertTrue(self.device.click_list_text('Cable', 2), '点击Cable失败')
            self.assertTrue(self.device.click_list_text('Digital', 2), '点击Digital失败')
            self.device.center(60 * 3)
            self.device.center(2)
            self.device.logger.info('返回桌面')
            self.device.back(2, 2)
            self.device.home(3)

    def test_scan_advance_c_all(self):
        """
        Advance C Digital and analog channel scan
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.to_scan_page()
            if not self.device.d(text='Advanced options').exists:
                self.skipTest('扫台列表无Advanced options，故跳过')
            self.device.logger.info('点击Advanced options')
            self.assertTrue(self.device.click_list_text('Advanced options', 2), '点击Advanced options失败')
            self.assertTrue(self.device.click_list_text('Cable', 2), '点击Cable失败')
            self.assertTrue(self.device.click_list_text('Digital and analog', 2), '点击Digital and analog失败')
            self.device.center(60 * 5)
            self.device.center(2)
            self.device.logger.info('返回桌面')
            self.device.center(2)
            self.device.back(2, 2)
            self.device.home(3)

    def test_live_tv_settings(self):
        """
        遍历LiveTV的设置菜单
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for _ in range(mtbf_loop_times):
            self.open_to_tv()
            for i in range(7):
                self.device.open_live_tv_settings()
                self.device.down(2, i)
                self.device.center(10)
                self.device.back(2)

    def test_live_tv_more_settings(self):
        """
        遍历LiveTV的More Settings
        """
        if self.device.build_name in box_device:
            self.skipTest('盒子不支持此功能，故跳过')
        for i in range(mtbf_loop_times):
            self.open_to_tv()
            self.assertTrue(
                self.device.enter_settings_path(['Channels', 'Channels'], True),
                '进入LiveTV菜单失败')
            for j in range(8):
                cur_item = self.device.get_item_focus(settings_list_resource_id, settings_title_resource_id)
                self.device.center(10)
                if cur_item.lower() in ['update channels automatically']:
                    self.device.center(2)
                else:
                    self.device.back(2)
                self.device.down(2)
        self.device.back(2, 3)
        self.device.home(3)


class LiveTVWithoutSetup(LiveTV):
    def setUp(self):
        pass

    def test_open_live_tv_uat(self):
        """
        UAT进出LiveTV
        """
        for i in range(1, 61):
            self.device.home(3)
            self.device.logger.info(f'第{i}次进入LiveTV app')
            self.open_to_tv()
            sleep(20)

    def test_live_tv_long_play_uat(self):
        """
        LiveTV长播
        """
        # 先判断是否在TV源中，若不在则进入，否则直接播放
        cur_page = self.device.get_page()
        cur_input = self.device.get_input_id()
        if cur_page in live_tv_page and cur_input == '':
            self.device.logger.info('当前在TV源中')
        else:
            self.open_to_tv()
        self.device.long_play_uat('LiveTV')

