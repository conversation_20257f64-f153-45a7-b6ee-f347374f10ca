#!/usr/bin/python
# -*- coding:utf-8 -*- 

import unittest
from uiautomator import device as d
from testconfig import config
from random import choice
import time


import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases_m19/config.ini',encoding='utf-8')
PLAY_TIME = config_.get('play_options','play_time')
PLAY_TIME_LONG = config_.get('play_options','play_time_long')
MOVIES = config_.get('play_options','movie_nums')
TARGET_DIR = config_.get('play_options','target_dir')
keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']

# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR = str(config['play_options']['target_dir'])
# keys = ['back', 'home', 'up', 'down', 'left', 'right', 'enter', 'menu']
AP1 = 'ASUS'
AP2 = 'ASUS_5G'


class test1(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")

        d.wakeup()
        time.sleep(10)
        d.press.back()
        time.sleep(1)
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        for _ in range(3):
            d.press.back()
            time.sleep(1)
        time.sleep(1)
        d.press.home()

    def switchWifi(self):  # switch WIFI, then play youtube
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'), 'can not start youtube'
        time.sleep(2)
        d.press.up()
        time.sleep(2)
        d.press.enter()
        d.press.right()
        d.press.right()
        d.press.right()
        # input:escape plan
        d.press(0x00000021)
        d.press(0x0000002f)
        d.press(0x0000001f)
        d.press(0x0000001d)
        d.press(0x0000002c)
        d.press(0x00000021)
        d.press(0x0000003e)
        d.press(0x0000002c)
        d.press(0x00000028)
        d.press(0x0000001d)
        d.press(0x0000002a)
        time.sleep(5)
        d.press.back()
        # d(text='SEARCH').click()
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.home()
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.connectivity.NetworkActivity'), 'failed'
        d.press.down()
        d(textContains='See all').click()
        time.sleep(5)
        i = 0
        while i < 45:
            if (d(text=AP1).exists):
                d(text=AP1).click()
                break
            else:
                d.press.down()
                i = i + 1

        if d(text="CONNECT").exists:
            d(text="CONNECT").click()
        else:
            d.press.home()
        time.sleep(5)
        d.press.home()

        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.activity.TvGuideActivity'), 'can not start youtube'
        time.sleep(2)
        d.press.up()
        time.sleep(2)
        d.press.enter()
        d.press.right()
        d.press.right()
        d.press.right()
        # input:escape plan
        d.press(0x00000021)
        d.press(0x0000002f)
        d.press(0x0000001f)
        d.press(0x0000001d)
        d.press(0x0000002c)
        d.press(0x00000021)
        d.press(0x0000003e)
        d.press(0x0000002c)
        d.press(0x00000028)
        d.press(0x0000001d)
        d.press(0x0000002a)
        time.sleep(5)
        d.press.back()
        d.press.enter()
        time.sleep(PLAY_TIME)
        d.press.home()
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.connectivity.NetworkActivity'), 'failed'
        d.press.down()
        d(textContains='See all').click()
        time.sleep(5)
        i = 0
        while i < 45:
            if (d(text=AP2).exists):
                d(text=AP2).click()
                break

            else:
                d.press.down()
                i = i + 1

        if d(text="CONNECT").exists:
            d(text="CONNECT").click()
        else:
            d.press.home()

        time.sleep(5)
        d.press.home()

    def testupdate(self):  # version update
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings'), 'Settings failed'
        assert d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.about.AboutActivity'), 'About failed'
        d(textContains="system update").click()
        if (d(text="Check for update").exists):
            d(text="Check for update").click()
            time.sleep(300)
        if (d(textContains="Restart").exists):
            d(textContains="Restart").click()
            time.sleep(300)
        else:
            d.press.home()
        time.sleep(3600)
