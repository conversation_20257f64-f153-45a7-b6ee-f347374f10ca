#!/usr/bin/python
# -*- coding:utf-8 -*-

import unittest
from uiautomator import device as d
from testconfig import config
from random import choice
import os
import time
from time import sleep

import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases_m19/config.ini',encoding='utf-8')
WIFI_SSID = config_.get('account','wifi_ssid')
WIFI_PWD = config_.get('account','wifi_pwd')
NETFLIX = config_.get('account','netflix')
NETFLIX_PWD = config_.get('account','netflix_pwd')
SLING = config_.get('account','sling')
SLING_PWD = config_.get('account','sling_pwd')
GOOGLE = config_.get('account','google')
GOOGLE_PWD = config_.get('account','google_pwd')
VUDU = config_.get('account','vudu')
VUDU_PWD = config_.get('account','vudu_pwd')
PANDORA = config_.get('account','pandora')
PANDORA_PWD = config_.get('account','pandora_pwd')
SHOWTIME = config_.get('account','showtime')
SHOWTIME_PWD = config_.get('account','showtime_pwd')
CBS = config_.get('account','cbs')
CBS_PWD = config_.get('account','cbs_pwd')
HULU = config_.get('account','hulu')
HULU_PWD = config_.get('account','hulu_pwd')

PLAY_TIME = config_.get('play_options','play_time')
PLAY_TIME_LONG = config_.get('play_options','play_time_long')
MOVIES = config_.get('play_options','movie_nums')
TARGET_DIR = config_.get('play_options','target_dir')
keys = ['up', 'down', 'left', 'right', 'enter']
AP1 = config_.get('play_options','ap1')
AP2 = 'ASUS_5G'
AP3 = config_.get('play_options','ap3')

# WIFI_SSID = str(config['account']['wifi_ssid'])
# WIFI_PWD = str(config['account']['wifi_pwd'])
# NETFLIX = str(config['account']['netflix'])
# NETFLIX_PWD = str(config['account']['netflix_pwd'])
# SLING = str(config['account']['sling'])
# SLING_PWD = str(config['account']['sling_pwd'])
# GOOGLE = str(config['account']['google'])
# GOOGLE_PWD = str(config['account']['google_pwd'])
# VUDU = str(config['account']['vudu'])
# VUDU_PWD = str(config['account']['vudu_pwd'])
# PANDORA = str(config['account']['pandora'])
# PANDORA_PWD = str(config['account']['pandora_pwd'])
# SHOWTIME = str(config['account']['showtime'])
# SHOWTIME_PWD = str(config['account']['showtime_pwd'])
# CBS = str(config['account']['cbs'])
# CBS_PWD = str(config['account']['cbs_pwd'])
# HULU = str(config['account']['hulu'])
# HULU_PWD = str(config['account']['hulu_pwd'])
#
# PLAY_TIME = int(config['play_options']['play_time'])
# PLAY_TIME_LONG = int(config['play_options']['play_time_long'])
# MOVIES = int(config['play_options']['movie_nums'])
# TARGET_DIR = str(config['play_options']['target_dir'])
# keys = ['up', 'down', 'left', 'right', 'enter']
# AP1 = str(config['play_options']['ap1'])
# AP2 = 'ASUS_5G'
# AP3 = str(config['play_options']['ap3'])
num = 5  # num of download apps from GooglePlayStore
num1 = 5  # num of download games from GooglePlayGames


class test1(unittest.TestCase):
    def setUp(self):
        """
        called before  each test method start.
        """
        d.watcher("AUTO_FC_WHEN_ANR").when(text="稍后升级").click(text="稍后升级")
        d.watcher("EXIT_VST_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_MOLI_THIRD_APP").when(textContains="退出").click(text="退出")
        d.watcher("EXIT_DIANLIMAO_THIRD_APP").when(textContains="确定退出电视猫视频？").click(text="确定")
        d.watcher("EXIT_XUNLEI_THIRD_APP").when(textContains="是否退出迅雷看看？").click(text="确定")
        d.watcher("INSTALL_NEW_VERSION").when(textContains="您要安装此应用的新版本吗？").click(text="取消")
        d.watcher("EXIT_SOHU_APP").when(textContains="主人，您真的要离开吗？记得常来看我呀").click(text="确定")
        d.watcher("PASS_NOTIFICATION").when(textContains="确认").click(text="确认")
        d.watcher("PASS_VST_UPDATE1").when(textContains="下次更新").click(text="下次更新")
        d.watcher("PASS_TOGIC_UPDATE").when(packageName='com.togic.livevideo', textContains="已阅读").press('enter',
                                                                                                         'enter')
        d.watcher("PASS_VST_UPDATE2").when(textContains='根据国家现行政策规定').press('enter')
        d.watcher("PASS_NO_RESPONSE").when(textContains='无响应').click(text="确定")
        d.watcher("PASS_STOP_RUN").when(textContains='停止运行').click(text="确定")

        d.wakeup()
        time.sleep(10)
        self.presskey("back", 3)
        time.sleep(1)
        d.press.home()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        d.watchers.remove("AUTO_FC_WHEN_ANR")
        d.watchers.remove("EXIT_VST_THIRD_APP")
        d.watchers.remove("EXIT_SOHU_APP")
        d.watchers.remove("PASS_NOTIFICATION")
        d.watchers.remove("PASS_VST_UPDATE1")
        d.watchers.remove("PASS_VST_UPDATE2")
        d.watchers.remove("PASS_TOGIC_UPDATE")
        d.server.adb.raw_cmd("pkill uiautomator")
        for _ in range(5):
            d.press.back()
            time.sleep(1)
        time.sleep(1)
        d.press.home()

    def presskey(self, key, num):
        for i in range(num):
            getattr(d.press, key)()

      # supplement by lzj
        # 连接wifi

    def testwifiConnect(self):
        d.server.adb.raw_cmd(' shell am start -S com.android.tv.settings/.MainSettings')
        sleep(1)
        d(text='Network & Internet').click()
        sleep(1)
        d.press.up()
        sleep(5)

        def wifi_connect():

            d(text='See all').click()
            sleep(1)
            for _ in range(50):
                d.press.up()
            sleep(1)
            d.press.down()
            sleep(1)
            for _ in range(100):
                if not d(text=WIFI_SSID):
                    d.press.down()
                    sleep(1)
                else:
                    break
            d(text=WIFI_SSID).click()
            sleep(1)
            d.server.adb.raw_cmd("shell input text '%s'" % WIFI_PWD)
            sleep(1)
            d.press.enter()
            sleep(1)
            # 查询wifi是否连接成功
            for _ in range(50):
                d.press.up
            sleep(1)
            d.press.down()

        # switch_status=d(resourceId='android:id/switch_widget')[0].checked
        # print switch_status
        if d(resourceId='android:id/switch_widget')[0].checked == False:
            sleep(3)
            print('wifi switch is off')
            d.press.up()
            d.press.enter()
            sleep(5)
            wifi_connect()
        elif d(resourceId='android:id/switch_widget')[0].checked and d(resourceId="android:id/icon").exists:
            sleep(5)
            print ('wifi is ok')
            wifi_connect()

        else:
            pass

    def testRename(self):
        d.server.adb.raw_cmd('shell am start -S com.android.tv.settings/.MainSettings')
        for _ in range(5):
            d.press.down()
        d(text='Device Preferences').click()
        sleep(1)
        d(text='About').click()
        sleep(1)
        d(text='Device name').click()
        sleep(1)
        d(text='Change').click()
        sleep(1)
        for _ in range(5):
            d.press.up()
        d.press.enter()
        sleep(1)

        d(text='Device name').click()
        sleep(1)
        d(text='Change').click()
        sleep(1)
        for _ in range(5):
            d.press.up()
        sleep(1)
        d.press.down()
        sleep(1)
        d.press.enter()
        sleep(1)

        def device_change():
            d(text='Device name').click()
            sleep(1)
            d(text='Change').click()
            sleep(1)
            for _ in range(5):
                d.press.up()
            sleep(1)

        for i in range(3):
            device_change()
            for j in range(i+2):
                d.press.down()
            d.press.enter()
            sleep(1)
        sleep(5)

        device_change()
        for _ in range(5):
            d.press.down()
        d(text="Enter custom name…").click()
        sleep(2)
        d.server.adb.raw_cmd('shell input text liangzhaojun')
        sleep(2)
        d.press.enter()
        sleep(2)
        d.press.home()

    def testDisplaySound(self):
        d.server.adb.raw_cmd('shell am start -S com.android.tv.settings/.MainSettings')
        sleep(1)
        for _ in range(6):
            d.press.down()
        if d(resourceId='android:id/title').exists and d(text='Device Preferences').exists:
            d(text='Device Preferences').click()
            d(text='Display & Sound').click()
            # Mactch content check
            if d(text='Match content').exists:
                d(text='Match content').click()
                sleep(1)
                d.press.down()
                d.press.enter()
                sleep(2)
                d.press.back()
                sleep(1)

            # Screen resolution check
            if d(text='Screen resolution').exists:
                d(text='Screen resolution').click()
                if d(text='Auto switch to best resolution').exists:
                    d.press.down()
                    if d(text='Off').exists and d(resourceId='android:id/summary').exists:
                        d.press.enter()
                        sleep(1)
                        if d(className='android.widget.TextView').exists and d(
                                resourceId='com.android.tv.settings:id/dialog_ok').exists:
                            # d(text='OK').click()
                            d.press.down()
                            d.press.right()
                            d.press.enter()
                            sleep(1)
                    else:
                        d.press.enter()

                if d(text='Display Mode').exists:
                    d.press.down()
                    d.press.enter()
                    for _ in range(20):
                        d.press.up()
                    d.press.enter()
                    sleep(1)
                    d.press.right()
                    d.press.enter()
                    sleep(1)

                    for _ in range(10):
                        d.press.down()
                        d.press.enter()
                        sleep(1)
                        d.press.right()
                        d.press.enter()
                        sleep(1)

                    for _ in range(20):
                        d.press.up()
                    d.press.enter()
                    sleep(2)
                    d.press.right()
                    d.press.enter()
                    d.press.back()

                if d(text='Dolby Vision').exists:
                    d.press.down()
                    d.press.enter()
                    for _ in range(2):
                        d.press.up()
                    d.press.enter()
                    sleep(3)
                    d.press.down()
                    d.press.enter()
                    sleep(3)
                    for _ in range(2):
                        d.press.back()
                else:
                    d.press.back()
                    sleep(1)

            # Screen position check
            if d(text='Screen position').exists:
                d(text='Screen position').click()
                d.press.up()
                for i in range(20):
                    d.press.down()
                    for j in range(i):
                        d.press.enter()
                    d.press.up()
                    for j in range(i):
                        d.press.enter()
                d.press.back()

            # Hdmi CEC check
            if d(text='HDMI CEC').exists:
                d(text='HDMI CEC').click()
                for _ in range(2):
                    d.press.enter()
                    sleep(3)
                for _ in range(3):
                    d.press.down()
                    d.press.enter()
                for _ in range(3):
                    d.press.enter()
                    d.press.up()
                d.press.back()

            # System sounds check
            if d(text='System sounds').exists:
                for _ in range(2):
                    d(text='System sounds').click()

            # Advanced sound settings check
            if d(text='Advanced sound settings').exists:
                d(text='Advanced sound settings').click()
                sleep(1)
                if d(text='Select formats').exists:
                    d(text='Select formats').click()
                    sleep(1)
                    if d(text='Automatic: Use device reported formats').exists and d(
                            text='None: Never use surround sound').exists and d(
                        text='Passthough: According to the compatibility of sink device, keep the original format output').exists:
                        d(text='Automatic: Use device reported formats').click()
                        sleep(1)
                        d(text='Select formats').click()
                        sleep(1)
                        d(text='None: Never use surround sound').click()
                        sleep(1)
                        d(text='Select formats').click()
                        sleep(1)
                        d(text='Passthough: According to the compatibility of sink device, keep the original format output').click()
                        sleep(1)

                    d.press.back()

    def testnetflixSignin(self):
        d.server.adb.raw_cmd("shell pm clear com.netflix.ninja")
        time.sleep(5)
        assert d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity'), 'launch NETFLIX failed'
        time.sleep(30)
        d.press.down()
        d.press.enter()
        time.sleep(5)
        d.server.adb.raw_cmd("shell input text '%s'" % NETFLIX)
        time.sleep(5)
        for i in range(3):
            d.press.down()
        d.press.enter()
        time.sleep(3)
        d.server.adb.raw_cmd("shell input text '%s'" % NETFLIX_PWD)
        time.sleep(5)
        for i in range(5):
            d.press.down()
        d.press.enter()
        time.sleep(5)
        d.press.enter()
        time.sleep(3)

    def testGooglesignin(self):
        d.server.adb.raw_cmd("shell pm clear com.google.android.gms")
        time.sleep(5)
        d.server.adb.raw_cmd("shell am start -n com.android.tv.settings/com.android.tv.settings.MainSettings")
        time.sleep(3)
        if d(text="Accounts & sign-in").exists and d(packageName="com.android.tv.settings").exists:
            d(text="Accounts & sign-in").click()
            time.sleep(5)
            d(text="Google").click()
            time.sleep(5)
            d(text="Sign In").click()
            time.sleep(10)
            assert d(text="Use your Google Account").wait.exists(timeout=10000), "can enter google account sign in"
            d.server.adb.raw_cmd("shell input text xm11111111111")
            time.sleep(5)
            d.press.enter()
            time.sleep(5)
            assert d(text="Show password").wait.exists(timeout=10000), "google account password input failed"
            d.server.adb.raw_cmd("shell input text Xiaomi@123")
            time.sleep(5)
            d.press.enter()
            time.sleep(10)

    def testSlingdownload(self):
        d.server.adb.raw_cmd(
            "shell am start -n com.android.vending/com.google.android.finsky.tvmainactivity.TvMainActivity")
        time.sleep(5)
        d.press.up()
        time.sleep(5)
        d.press.left()
        time.sleep(5)
        d.press.enter()
        time.sleep(5)
        d.press.right()
        time.sleep(5)
        d.server.adb.raw_cmd("shell input text sling")
        time.sleep(5)
        d.press.back()
        time.sleep(5)
        if d(text="Open").wait.exists(timeout=3000):
            pass
        else:
            assert d(text="Install").wait.exists(timeout=3000), "can not find install button in slings download"
            d(text="Install").click()
            time.sleep(15)
            assert d(text="Open").wait.exists(timeout=3000), "can not find open button in slings download"

    def testSlingSignin(self):
        d.server.adb.raw_cmd("shell pm clear com.sling")
        time.sleep(5)
        assert d.server.adb.raw_cmd(
            'shell am start -n com.sling/com.movenetworks.StartupActivity'), "launch sling failed"
        time.sleep(20)
        if d(text="DISMISS").wait.exists(timeout=3000):
            d(text="DISMISS").click
            time.sleep(5)
        if d(textContains="Sign In").wait.exists(timeout=3000):
            d.press.right()
            d.press.enter()
        else:
            assert False, "can not found Sign in button"
        time.sleep(5)
        if d(textContains="Email Address").wait.exists(timeout=3000):
            d.server.adb.raw_cmd("shell input text '%s'" % SLING)
            time.sleep(3)
            d.press.enter()
            d.server.adb.raw_cmd("shell input text '%s'" % SLING_PWD)
            time.sleep(3)
            d.press.enter()
            time.sleep(10)
        else:
            assert False, "not found text 'Email Address'"

    def testVUDUSignin(self):
        d.server.adb.raw_cmd("shell pm clear air.com.vudu.air.DownloaderTablet")
        time.sleep(5)
        assert d.server.adb.raw_cmd(
            'shell am start -n air.com.vudu.air.DownloaderTablet/com.vudu.android.app.activities.account.WelcomeActivity'), 'launch vudu failed'
        time.sleep(15)
        if d(textContains="UPDATE NOW").wait.exists(timeout=3000):
            d.press.enter()
            if d(packageName="com.google.android.gms").exists and d(descriptionContains="Sign in").exists:
                d.server.adb.raw_cmd("shell input text '%s'" % GOOGLE)
                time.sleep(5)
                d.press.enter()
                if d(descriptionContains="Welcome").wait.exists(timeout=3000):
                    d.server.adb.raw_cmd("shell input text '%s'" % GOOGLE_PWD)
                    time.sleep(5)
                    d.press.enter()
                    time.sleep(2)
                    d.press.enter()
                    if d(text='UPDATE').wait.exists(timeout=3000):
                        d.press.enter()
                        for i in range(10):
                            if d(text="OPEN").wait.exists():
                                break
                            else:
                                time.sleep(10)
                        d(text="OPEN").click()
                    else:
                        assert False, "UPDATE not exists"
                else:
                    assert False, "Welcome not exists"
            else:
                assert False, "not found Sign in "
        elif d(text="Sign In").exists and d(packageName="air.com.vudu.air.DownloaderTablet").exists:
            d.press.down()
            d.press.enter()
            time.sleep(2)
            d.press.enter()
            time.sleep(5)
            if d(text="Email").exists:
                d.press.enter()
                time.sleep(2)
                d.server.adb.raw_cmd("shell input text '%s'" % VUDU)
                time.sleep(2)
                d.press.enter()
                time.sleep(2)
                d.server.adb.raw_cmd("shell input text '%s'" % VUDU_PWD)
                time.sleep(2)
                d.press.enter()
                d.press.enter()
                time.sleep(2)
                if d(textContains="too many authorized").exists:
                    assert False, " VUDU:Please deauthorize one of your devices and try again"
                else:
                    time.sleep(10)
                    d.press.enter()
                    for i in range(4):
                        d.press.back()
                    d.press.home()
            else:
                assert False, "not found text Email"
        else:
            assert False, "Sign in not exists"

    def testpandorasignin(self):
        d.server.adb.raw_cmd("shell pm clear com.pandora.android.atv")
        time.sleep(5)
        assert d.server.adb.raw_cmd(
            'shell am start -n com.pandora.android.atv/com.pandora.android.MainActivity'), 'launch pandora failed'
        time.sleep(30)
        if d(text="Log In").exists and d(packageName="com.pandora.android.atv").exists:
            d(text='Log In').click()
            time.sleep(10)
            if d(text="Log In with Email").wait.exists(timeout=3000):
                d(text="Log In with Email").click()
                time.sleep(5)
                if d(text="Password").exists and d(packageName="com.pandora.android.atv").exists:
                    d.server.adb.raw_cmd("shell input text '%s'" % PANDORA)
                    time.sleep(3)
                    d.press.enter()
                    d.server.adb.raw_cmd("shell input text '%s'" % PANDORA_PWD)
                    time.sleep(3)
                    d.press.back()
                    time.sleep(1)
                    d.press.down()
                    d.press.enter()
                    time.sleep(15)
                    assert d(text="My Stations").exists, 'sign in pandora fail'
                else:
                    assert False, 'not found text Password'

            else:
                assert False, "not found log in with email"
        else:
            assert False, 'launch pandora failed'

    def testshowtimesignin(self):
        d.server.adb.raw_cmd("shell pm clear com.showtime.standalone")
        time.sleep(5)
        assert d.server.adb.raw_cmd(
            'shell am start -n com.showtime.standalone/com.showtime.showtimeanytime.activities.IntroActivity'), 'launch showtime failed'
        time.sleep(20)
        if d(text="RETRY").exists and d(packageName="com.showtime.standalone").exists:
            d(text="RETRY").click()
            time.sleep(5)
            assert not d(text="RETRY"), 'network not reach'
        elif d(textContains="ALREADY SUBSCRIBED").exists and d(packageName="com.showtime.standalone").exists:
            d(textContains="ALREADY SUBSCRIBED").click()
            time.sleep(4)
            if d(textContains="Password").exists and d(packageName="com.showtime.standalone").exists:
                d.server.adb.raw_cmd("shell input text '%s'" % SHOWTIME)
                time.sleep(2)
                d.press.enter()
                d.server.adb.raw_cmd("shell input text '%s'" % SHOWTIME_PWD)
                time.sleep(2)
                d.press.enter()
                time.sleep(5)
                if d(textContains="can't reach Showtime").exists:
                    assert False, 'can not reach showtime'
                elif d(textContains="MENU").exists and d(textContains="SEARCH").exists:
                    d.press.enter()
                    time.sleep(15)
                else:
                    assert False, "sign in showtime failed"
            else:
                assert False, "text Password not exists"
        else:
            assert "launch showtime failed"

    def testcbs(self):
        d.server.adb.raw_cmd("shell pm clear com.cbs.ott")
        time.sleep(5)
        assert d.server.adb.raw_cmd('shell am start -n com.cbs.ott/.ui.activity.HomeActivity'), 'launch cbs failed'
        time.sleep(20)
        if d(text="DISCOVER").exists and d(packageName="com.cbs.ott").exists:
            for i in range(4):
                d.press.down()
                time.sleep(1)
            for i in range(4):
                d.press.up()
                time.sleep(1)
            for i in range(10):
                d.press.right()
                time.sleep(1)
                d.press.enter()
                time.sleep(5)
                d.press.back()
            for _ in range(100):
                key = choice(keys)
                getattr(d.press, key)()
        d.server.adb.raw_cmd('shell am start -n com.cbs.ott/.ui.activity.HomeActivity')
        time.sleep(5)
        for _ in range(5):
            time.sleep(5)
            d.press.right()
            d.press.enter()
            time.sleep(5)
            if d(text="START WATCHING").exists and d(packageName="com.cbs.ott").exists:
                d(text="START WATCHING").click()
                time.sleep(6)
                for i in range(10):
                    if not d(text="START WATCHING").exists:
                        time.sleep(5)
                    else:
                        break
                d.press.back()
            else:
                d.press.back()

    def testhulusignin(self):
        d.server.adb.raw_cmd("shell pm clear com.hulu.livingroomplus")
        time.sleep(5)
        assert d.server.adb.raw_cmd(' shell am start -S com.hulu.livingroomplus/.MainActivity'), 'launch cbs failed'
        time.sleep(20)
        if d(text="Exit Application").exists and d(packageName="com.hulu.livingroomplus").exists:
            d.press.enter()
            assert False, 'network not reach'
        elif d(text="LOG IN").exists and d(packageName="com.hulu.livingroomplus").exists:
            d.press.left()
            d.press.enter()
            time.sleep(5)
            d.press.enter()
            time.sleep(5)
            if d(text="<EMAIL>").exists and d(packageName="com.hulu.livingroomplus").exists:
                d.server.adb.raw_cmd("shell input text '%s'" % HULU)
                time.sleep(3)
                d.press.enter()
                d.press.enter()
                d.server.adb.raw_cmd("shell input text '%s'" % HULU_PWD)
                time.sleep(3)
                d.press.enter()
                time.sleep(1)
                d.press.enter()
                time.sleep(15)
                assert not d(textContains="Authentication Error").exists, "Authentication Error"
                d.press.enter()
            else:
                assert False, "text <EMAIL> not exists "
        else:
            assert False, "LOG IN not exists"

    def testnetflix(self):
        d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity')
        time.sleep(10)
        d.press.enter()
        time.sleep(60)
        d.press.back()
        time.sleep(2)
        for _ in range(10):
            d.press.down()
            time.sleep(1)
            d.press.enter()
            time.sleep(5)
            d.press.enter()
            time.sleep(8)
            d.press.back()
            d.press.back()
        for _ in range(100):
            key = choice(keys)
            getattr(d.press, key)()
            time.sleep(1)

    def testsling(self):
        d.server.adb.raw_cmd('shell am start -n com.sling/com.movenetworks.StartupActivity')
        time.sleep(20)
        for _ in range(10):
            if d(text="MY TV").exists and d(text="ON NOW").exists:
                for i in range(10):
                    d.press.enter()
                    time.sleep(60)
                    d.press.back()
                    time.sleep(2)
                    self.presskey("right", i)
            elif d(textContains="not available").exists:
                assert False, "Streaming is not available in your location"
            else:
                time.sleep(3)
        else:
            assert False, "Launch sling failed"

    def testvudu(self):
        d.server.adb.raw_cmd(
            'shell am start -n air.com.vudu.air.DownloaderTablet/com.vudu.android.app.activities.account.WelcomeActivity')
        time.sleep(15)
        if d(text="My Vudu").exists:
            d.press.down()
            time.sleep(2)
            for i in range(5):
                d.press.right()
                d.press.enter()
                time.sleep(2)
                if d(text="WATCH TRAILER").exists:
                    d.press.enter()
                    time.sleep(60)
                    self.presskey("back", 2)
                else:
                    d.press.back()
        else:
            assert False, "Vudu not at home"

    def testpandora(self):
        d.server.adb.raw_cmd('shell am start -n com.pandora.android.atv/com.pandora.android.MainActivity')
        time.sleep(20)
        for _ in range(10):
            if d(resourceId="com.pandora.android.atv:id/fullscreen_content").exists:
                for i in range(5):
                    for _ in range(5):
                        d.press.enter()
                        time.sleep(30)
                        d.press.back()
                        time.sleep(2)
                        self.presskey("right", i)
                    d.press.down()
                break
            else:
                time.sleep(5)
        else:
            assert False, "Pandora not at home"

    def testshowtime(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.showtime.standalone/com.showtime.showtimeanytime.activities.IntroActivity')
        time.sleep(20)

    def testhulu(self):
        d.server.adb.raw_cmd(' shell am start -S com.hulu.livingroomplus/.MainActivity')
        time.sleep(20)

    def appsleep(self):
        time.sleep(20)
        d.press.home()
        time.sleep(5)

    def testlaunchapp(self):
        assert d.server.adb.raw_cmd('shell am start -n com.netflix.ninja/.MainActivity'), 'launch NETFLIX failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.sling/com.movenetworks.StartupActivity'), "launch sling failed"
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n air.com.vudu.air.DownloaderTablet/com.vudu.android.app.activities.account.WelcomeActivity'), 'launch vudu failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.pandora.android.atv/com.pandora.android.MainActivity'), 'launch pandora failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.showtime.standalone/com.showtime.showtimeanytime.activities.IntroActivity'), 'launch showtime failed'
        self.appsleep()
        assert d.server.adb.raw_cmd('shell am start -n com.cbs.ott/.ui.activity.HomeActivity'), 'launch cbs failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(' shell am start -S com.hulu.livingroomplus/.MainActivity'), 'launch hulu failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.music/.tv.HomeActivity'), 'launch google music failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.videos/com.google.android.apps.play.movies.tv.usecase.home.TvHomeActivity'), 'launch google video fialed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.cobalt.activity.MainActivity'), 'launch youtube failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.droidlogic.videoplayer/.FileList'), 'launch videoplayer failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.play.games/com.google.android.gms.games.pano.ui.profile.PanoGamesSignUpActivity'), 'launch google games failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.android.vending/com.google.android.finsky.activities.TvMainActivity'), 'launch google store failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.nousguide.android.rbtv/com.redbull.launch.SplashActivity'), 'launch rebbull failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.hbo.hbonow/com.hbo.go.LaunchActivity'), 'launch hbonow failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n tv.pluto.android/.leanback.controller.LeanbackSplashOnboardActivity'), 'launch pluto failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.spotify.tv.android/.SpotifyTVActivity'), 'launch spotify failed'
        self.appsleep()
        assert d.server.adb.raw_cmd('shell am start -n com.hbo.go/.LaunchActivity'), 'launch hbogo failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.handmark.sportcaster/com.onelouder.cbssportstv.MainActivity'), 'launch cbssports failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.cbsnews.ott/.activities.StartActivity'), 'launch cbsnews failed'
        self.appsleep()
        assert d.server.adb.raw_cmd(
            'shell am start -n com.google.android.tv/com.android.tv.TvActivity'), 'launch livetv failed'
        self.appsleep()
        time.sleep(20)
        d.press.back()
        d.press.home()

    def presshome(self):
        d.press.home()
        time.sleep(2)
        self.presskey("back", 2)
        self.presskey("up", 3)
        self.presskey("left", 3)

    def testhome(self):
        d.press.home()
        time.sleep(2)
        for _ in range(10):
            d.press.down()
        for _ in range(10):
            d.press.up()
        for _ in range(10):
            d.press.down()
            for _ in range(10):
                d.press.right()
        for _ in range(10):
            d.press.up()
            for _ in range(10):
                d.press.right()
        for i in range(10):
            self.presskey("down", i + 1)
            for _ in range(2):
                d.press.right()
            d.press.enter()
            time.sleep(15)
            d.press.enter()
            if not d(text="Apps").exists:
                time.sleep(5)
            self.presskey("home", 2)

    # 桌面上第一行favorite apps6个应用进入退出
    def testfavoriteapps(self):
        for i in range(6):
            d.press.home()
            time.sleep(2)
            self.presskey("back", 2)
            self.presskey("left", 2)
            self.presskey("up", 2)
            d.press.down()
            self.presskey("right", i)
            time.sleep(1)
            d.press.enter()
            time.sleep(10)

    # Channels list中遍历netflix的推荐并点击一个内容播放1分钟
    def testChannelslist_netflix(self):
        self.presshome()
        for i in range(10):
            if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Netflix").exists:
                for _ in range(5):
                    if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Netflix").focused:
                        d.press.right()
                        self.presskey("right", 10)
                        self.presskey("left", 5)
                        d.press.enter()
                        time.sleep(10)
                        d.press.enter()
                        time.sleep(60)
                        self.presskey("back", 3)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    # Channels list中遍历redbull的推荐并点击一个内容播放1分钟
    def testChannelslist_redbull(self):
        self.presshome()
        for i in range(10):
            if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Red Bull TV").exists:
                for _ in range(5):
                    if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Red Bull TV").focused:
                        d.press.right()
                        self.presskey("right", 10)
                        self.presskey("left", 5)
                        d.press.enter()
                        time.sleep(10)
                        d.press.enter()
                        time.sleep(60)
                        self.presskey("back", 3)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    # Channels list中遍历Google Play Music的推荐并点击一个内容播放1分钟
    def testChannelslist_GooglePlayMusic(self):
        self.presshome()
        for i in range(10):
            if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Play Music").exists:
                for _ in range(5):
                    if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Play Music").focused:
                        d.press.right()
                        self.presskey("right", 10)
                        d.press.enter()
                        time.sleep(5)
                        d.press.right()
                        d.press.down()
                        d.press.enter()
                        if d(text="No music in the library"):
                            self.presskey("back", 3)
                        else:
                            time.sleep(30)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    # Channels list中遍历YouTube的推荐并点击一个内容播放1分钟
    def testChannelslist_YouTube(self):
        self.presshome()
        for i in range(10):
            if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="YouTube").exists:
                for _ in range(5):
                    if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="YouTube").focused:
                        d.press.right()
                        self.presskey("right", 10)
                        d.press.enter()
                        time.sleep(5)
                        time.sleep(60)
                        self.presskey("back", 3)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    # Channels list中遍历Play Movies & TV的推荐并点击一个内容播放1分钟
    def testChannelslist_PlayMovies(self):
        self.presshome()
        for i in range(10):
            if d(resourceId="com.google.android.tvlauncher:id/channel_logo", description="Play Movies & TV").exists:
                for _ in range(5):
                    if d(resourceId="com.google.android.tvlauncher:id/channel_logo",
                         description="Play Movies & TV").focused:
                        d.press.right()
                        self.presskey("right", 10)
                        d.press.enter()
                        time.sleep(5)
                        if d(text="PLAY TRAILER").wait.exists(timeout=10000):
                            d.press.enter()
                            time.sleep(60)
                        self.presskey("back", 3)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testRedbullPlay(self):
        self.presshome()
        d.server.adb.raw_cmd('shell am start -n com.nousguide.android.rbtv/com.redbull.launch.SplashActivity')
        time.sleep(60)
        d.press.up()
        time.sleep(2)
        for _ in range(5):
            self.presskey("right", 5)
            time.sleep(2)
            self.presskey("left", 5)
            time.sleep(2)
        d.press.back()
        time.sleep(2)
        d.press.down()
        d.press.left()
        d.press.up()
        time.sleep(1)
        self.presskey("down", 4)
        d.press.back()
        time.sleep(180)

    def testYoutubePlay(self):
        self.presshome()
        d.server.adb.raw_cmd(
            'shell am start -n com.google.android.youtube.tv/com.google.android.apps.youtube.tv.cobalt.activity.MainActivity')
        time.sleep(20)
        self.presskey("right", 2)
        d.press.enter()
        time.sleep(10)
        # 快进
        d.press.enter()
        time.sleep(1)
        d.press.up()
        self.presskey("right", 3)
        time.sleep(2)
        d.press.down()
        time.sleep(5)
        # 快退
        d.press.enter()
        time.sleep(1)
        d.press.up()
        self.presskey("left", 3)
        time.sleep(2)
        d.press.down()
        time.sleep(5)
        # 暂停
        self.presskey("enter", 2)
        time.sleep(10)
        d.press.enter()
        # 下一个
        time.sleep(1)
        d.press.right()
        d.press.enter()
        time.sleep(10)
        # 上一个
        d.press.enter()
        d.press.left()
        d.press.enter()
        time.sleep(10)
        # 音量调节
        d.server.adb.raw_cmd("input KEYCODE_VOLUME_UP")
        time.sleep(2)
        d.server.adb.raw_cmd("input KEYCODE_VOLUME_DOWN")
        time.sleep(2)
        # 播放3分钟
        time.sleep(180)

    def testGooglePlayMovies(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.google.android.videos/com.google.android.apps.play.movies.tv.usecase.home.TvHomeActivity')
        time.sleep(10)
        self.presskey("right", 2)
        self.presskey("down", 4)
        d.press.enter()
        for _ in range(10):
            if d(text="PLAY TRAILER").wait.exists(timeout=10000):
                d.press.enter()
                time.sleep(20)
                self.presskey("right", 3)
                time.sleep(20)
                self.presskey("left", 3)
                time.sleep(20)
                d.press.back()
                time.sleep(5)
                d.press.down()
                time.sleep(1)
                d.press.enter()
            else:
                d.press.back()
                time.sleep(5)
                d.press.down()
                time.sleep(1)
                d.press.enter()
                time.sleep(1)

    def testLaunchAndExitLitvTV(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        if d(textContains="Watch content from your apps like watching channels on TV").exists:
            self.presskey("right", 4)
            time.sleep(1)
            d.press.enter()
            self.presskey("right", 3)
            d.press.enter()
            time.sleep(5)
            d.press.enter()
        else:
            time.sleep(10)
        self.presskey("back", 3)
        d.press.home()

    def testLiveChannelsChannels(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        # 遥控器上键下键切换channels
        for _ in range(10):
            d.press.down()
            time.sleep(10)
        for _ in range(10):
            d.press.up()
            time.sleep(10)
        d.server.adb.raw_cmd("input KEYCODE_VOLUME_UP")
        time.sleep(2)
        d.server.adb.raw_cmd("input KEYCODE_VOLUME_DOWN")
        time.sleep(2)
        # 点击确认键在现有的channels之间切换
        d.press.enter()
        time.sleep(1)
        d.press.enter()
        d.press.right()
        d.press.down()
        d.press.enter()
        d.press.back()
        time.sleep(180)

    def testLiveChannelsProgramGuid(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        time.sleep(1)
        d.press.enter()
        d.press.left()
        for _ in range(5):
            d.press.down()
            time.sleep(2)
            d.press.up()
            time.sleep(2)
        d.press.right()
        d.press.right()
        d.press.enter()
        time.sleep(60)

    def testLiveChannelsClosedCaptions(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        d.press.down()
        d.press.enter()
        time.sleep(2)
        d.press.down()
        d.press.enter()
        time.sleep(2)
        # display
        d.press.enter()
        d.press.enter()
        d.press.down()
        # language
        for i in range(10):
            d.press.enter()
            self.presskey("down", i)
            d.press.enter()
        # textsize
        d.press.down()
        for i in range(5):
            d.press.enter()
            self.presskey("down", i)
            d.press.enter()
        for _ in range(5):
            d.press.down()
            d.press.enter()

    def testLiveChannelsPIP(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        d.press.down()
        d.press.right()
        d.press.enter()
        time.sleep(10)
        d.press.enter()
        time.sleep(30)
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        self.presskey("back", 3)

    def testLiveChannelsTVOptionsSettings(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        d.press.down()
        self.presskey("right", 3)
        d.press.enter()
        d.press.enter()
        time.sleep(1)
        for _ in range(20):
            d.press.up()
            d.press.enter()
        for _ in range(20):
            d.press.down()
            d.press.enter()

    def testLiveChannelsChannelsourcePLutoTV(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        d.press.down()
        self.presskey("right", 3)
        d.press.enter()
        d.press.down()
        d.press.enter()
        d.press.down()
        d.press.enter()
        time.sleep(30)

    def testLiveChannelsChannelsourceplayMovies(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        d.press.down()
        self.presskey("right", 3)
        d.press.enter()
        d.press.down()
        d.press.enter()
        d.press.enter()
        time.sleep(30)

    def testLiveChannelsGetmoresources(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        d.press.down()
        self.presskey("right", 3)
        d.press.enter()
        d.press.down()
        d.press.enter()
        self.presskey("down", 3)
        d.press.enter()
        time.sleep(30)

    def testLiveChannelsGoogleAll(self):
        d.server.adb.raw_cmd('shell am start -n com.google.android.tv/com.android.tv.TvActivity')
        time.sleep(10)
        d.press.enter()
        for i in range(5):
            for _ in range(10):
                d.press.right()
            for _ in range(10):
                d.press.left()
        d.press.down()
        for i in range(5):
            for _ in range(4):
                d.press.right()
            for _ in range(4):
                d.press.left()
        self.presskey("back", 4)

    def testGoogleSearch(self):
        self.presshome()
        time.sleep(1)
        d.press.enter()
        time.sleep(3)
        d.press.back()
        self.presshome()
        time.sleep(1)
        d.press.right()
        d.press.enter()
        time.sleep(3)
        d.server.adb.raw_cmd("input text video")
        time.sleep(5)
        d.press.enter()
        time.sleep(5)
        d.press.enter()
        time.sleep(30)

    def testGooglePlayStore(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.android.vending/com.google.android.finsky.activities.TvMainActivity')
        time.sleep(10)
        if d(resourceId="com.android.vending:id/row_header", text="Home").exists:
            d.press.down()
            for _ in range(5):
                d.press.right()
                d.press.enter()
                time.sleep(5)
                if d(resourceId="com.android.vending:id/lb_action_button", text="INSTALL").exists:
                    d(resourceId="com.android.vending:id/lb_action_button", text="INSTALL").click()
                    time.sleep(30)
                    break
                elif d(resourceId="com.android.vending:id/lb_action_button", text="UNINSTALL").exists:
                    d(resourceId="com.android.vending:id/lb_action_button", text="UNINSTALL").click()
                    time.sleep(30)
                    break
                elif d(resourceId="com.android.vending:id/lb_action_button", text="OPEN").exists:
                    d(resourceId="com.android.vending:id/lb_action_button", text="OPEN").click()
                    time.sleep(30)
                    break
                else:
                    d.press.back()

    def testGooglePlayGames(self):
        d.server.adb.raw_cmd(
            'shell am start -n com.google.android.play.games/com.google.android.gms.games.pano.activity.MainPanoActivity')
        time.sleep(8)
        if d(resourceId="com.google.android.play.games:id/action_title", text="Next").exists:
            for _ in range(6):
                d.press.enter()
                time.sleep(2)
            for _ in range(3):
                d.press.right()
            d.press.enter()

    def testSettingsNetwork(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        if d(resourceId="android:id/title", text="Wi-Fi").exists:
            for _ in range(10):
                if d(index=1, instance=1).focused:
                    # wifi focused
                    d.press.enter()
                    time.sleep(5)
                    break
                else:
                    d.press.down()

    def testSettingsgooglecast(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Google Cast").exists:
                for _ in range(10):
                    if d(index=2, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSettingsDisplay(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Display").exists:
                for _ in range(10):
                    if d(index=3, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSwitchDisplayMode(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Display").exists:
                for _ in range(10):
                    if d(index=3, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(3)
                        d.press.enter()
                        time.sleep(1)
                        d.press.down()
                        for j in range(8):
                            d.press.enter()
                            self.presskey("down", j)
                            d.press.enter()
                            time.sleep(5)
                            d.press.right()
                            d.press.enter()
                            d.press.back()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSwitchColorSpace(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Display").exists:
                for _ in range(10):
                    if d(index=3, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(3)
                        d.press.enter()
                        time.sleep(1)
                        d.press.down()
                        time.sleep(1)
                        d.press.down()
                        for j in range(6):
                            d.press.enter()
                            self.presskey("down", j)
                            d.press.enter()
                            time.sleep(8)
                            d.press.back()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testScreenPosition(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Display").exists:
                for _ in range(10):
                    if d(index=3, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(3)
                        d.press.down()
                        time.sleep(1)
                        d.press.enter()
                        time.sleep(1)
                        d.press.down()
                        for _ in range(20):
                            d.press.enter()
                        d.press.up()
                        for _ in range(20):
                            d.press.enter()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSettingsSound(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Sound").exists:
                for _ in range(10):
                    if d(index=4, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSettingsSwitchSound(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Sound").exists:
                for _ in range(10):
                    if d(index=4, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        # 开个系统声音
                        d.press.enter()
                        time.sleep(1)
                        d.press.enter()
                        d.press.down()
                        # dolby sounds
                        for j in range(3):
                            d.press.enter()
                            time.sleep(1)
                            self.presskey("down", j)
                            d.press.enter()
                            time.sleep(1)
                        d.press.enter()
                        d.press.enter()
                        # surround sound
                        d.press.down()
                        for j in range(3):
                            d.press.enter()
                            time.sleep(1)
                            self.presskey("down", j)
                            d.press.enter()
                            time.sleep(1)
                        d.press.enter()
                        d.press.enter()
                        time.sleep(1)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSettingsApps(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Apps").exists:
                for _ in range(10):
                    if d(index=5, instance=0).focused:
                        # wifi focused
                        d.press.enter()
                        time.sleep(5)
                        break
                    else:
                        d.press.down()
                break
            else:
                d.press.down()

    def testSettingsScreensaver(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Screen saver").exists:
                for _ in range(5):
                    d.press.down()
                    time.sleep(1)
                time.sleep(5)
                d.press.enter()
                time.sleep(2)
                self.presskey("down", 3)
                d.press.enter()
                time.sleep(5)
                d.press.back()
                time.sleep(5)
                break

            else:
                d.press.down()

    def testSettingsStorageandreset(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for i in range(10):
            if d(resourceId="android:id/title", text="Screen saver").exists:
                for _ in range(6):
                    d.press.down()
                    time.sleep(1)
                time.sleep(5)
                d.press.enter()
                d.press.back()
                time.sleep(5)
                break

            else:
                d.press.down()

    def testSettingsAbout(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(7):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsDateandtime(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(8):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsLanguage(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(9):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsKeyboard(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(10):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsHomescreen(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(11):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsSearch(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(12):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsGoogle(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(13):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsSpeech(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(14):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsPlaybacksettings(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(15):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsAccessibility(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(16):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsHDMICEC(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(17):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsAddaccessory(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(18):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        time.sleep(30)
        d.press.back()
        time.sleep(5)

    def testSettingsLocation(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(20):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsSecurityrestrictions(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(21):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsUsageDiagnostics(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(22):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)

    def testSettingsAddAcount(self):
        d.server.adb.raw_cmd('shell am start -n com.android.tv.settings/.MainSettings')
        for _ in range(24):
            d.press.down()
            time.sleep(1)
        time.sleep(5)
        d.press.enter()
        d.press.back()
        time.sleep(5)
