#!/usr/bin/python
# -*- coding:utf-8 -*-

import time
import os
from subprocess import PIPE, STDOUT, Popen

# 设置TCL电视为默认设备
os.environ['ANDROID_SERIAL'] = '4C04902902004A0B4'

def timeout_command(command):
    """执行ADB命令"""
    try:
        state = Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
        return out
    except Exception as e:
        print(f"命令执行失败: {e}")
        return ""

def press_power():
    """按电源键"""
    print("📱 按电源键...")
    timeout_command('adb shell input keyevent KEYCODE_POWER')

def get_screen_status():
    """获取屏幕状态"""
    status = timeout_command('adb shell getprop sys.screen.turn_on')
    return status.strip()

def quick_str_test():
    """快速STR测试"""
    print("🔍 检查当前屏幕状态...")
    current_status = get_screen_status()
    print(f"当前状态: {current_status}")
    
    if 'true' in current_status:
        print("💡 屏幕当前是亮的，测试熄屏...")
        press_power()
        time.sleep(3)
        
        new_status = get_screen_status()
        print(f"熄屏后状态: {new_status}")
        
        if 'false' in new_status:
            print("✅ 熄屏成功！")
            
            print("等待2秒后测试亮屏...")
            time.sleep(2)
            
            press_power()
            time.sleep(3)
            
            final_status = get_screen_status()
            print(f"亮屏后状态: {final_status}")
            
            if 'true' in final_status:
                print("✅ 亮屏成功！STR测试完成")
                return True
            else:
                print("❌ 亮屏失败")
                return False
        else:
            print("❌ 熄屏失败")
            return False
    else:
        print("🌙 屏幕当前是熄灭的，测试亮屏...")
        press_power()
        time.sleep(3)
        
        new_status = get_screen_status()
        print(f"亮屏后状态: {new_status}")
        
        if 'true' in new_status:
            print("✅ 亮屏成功！")
            
            print("等待2秒后测试熄屏...")
            time.sleep(2)
            
            press_power()
            time.sleep(3)
            
            final_status = get_screen_status()
            print(f"熄屏后状态: {final_status}")
            
            if 'false' in final_status:
                print("✅ 熄屏成功！STR测试完成")
                return True
            else:
                print("❌ 熄屏失败")
                return False
        else:
            print("❌ 亮屏失败")
            return False

if __name__ == "__main__":
    print("=" * 40)
    print("TCL电视快速STR测试")
    print("设备: 4C04902902004A0B4")
    print("=" * 40)
    
    # 检查设备连接
    devices = timeout_command('adb devices')
    if "4C04902902004A0B4" not in devices:
        print("❌ TCL电视设备未连接")
        exit(1)
    
    print("✅ 设备连接正常")
    print()
    
    result = quick_str_test()
    
    print("\n" + "=" * 40)
    if result:
        print("🎉 STR测试成功！")
    else:
        print("💥 STR测试失败！")
    print("=" * 40)
