# Screencaps提取脚本使用说明

## 脚本概述

提供了三个不同的脚本来提取screencaps文件：

1. **`extract_screencaps.sh`** - 基础版本，扁平化提取所有文件
2. **`extract_screencaps_advanced.sh`** - 高级版本，支持多种选项
3. **`extract_screencaps_folders.sh`** - 文件夹版本，直接复制整个文件夹

## 脚本对比

| 功能 | 基础版 | 高级版 | 文件夹版 |
|------|--------|--------|----------|
| 扁平化文件提取 | ✅ | ✅ | ❌ |
| 保持目录结构 | ❌ | ✅ | ❌ |
| 复制整个文件夹 | ❌ | ✅ | ✅ |
| 文件类型过滤 | ❌ | ✅ | ❌ |
| 预览模式 | ❌ | ✅ | ✅ |
| 详细输出 | ✅ | ✅ | ✅ |

## 使用场景

### 1. 快速提取所有截图文件（推荐新手）
```bash
./extract_screencaps.sh
```
**结果**：所有截图文件被重命名并复制到 `extracted_screencaps/` 目录
```
extracted_screencaps/
├── screencaps1_1.png
├── screencaps1_2.jpg
├── screencaps2_1.png
└── screencaps2_2.mp4
```

### 2. 保持原始文件夹结构
```bash
./extract_screencaps_advanced.sh -s
```
**结果**：保持原始目录结构
```
extracted_screencaps/
├── test1/screencaps/image1.png
├── test1/screencaps/image2.jpg
├── test2/screencaps/video1.mp4
└── test3/screencaps/image3.png
```

### 3. 直接复制整个文件夹（推荐）
```bash
./extract_screencaps_folders.sh
```
**结果**：保持上级目录结构的完整复制
```
extracted_screencaps/
├── test1/                    # 保持原始上级目录名
│   └── screencaps/
│       ├── image1.png
│       ├── image2.jpg
│       └── subfolder/
│           └── image3.png
├── test2/                    # 保持原始上级目录名
│   └── screencaps/
│       ├── video1.mp4
│       └── image4.png
└── project_a/                # 保持原始上级目录名
    └── screencaps/
        └── screenshot.png
```

### 4. 只提取特定类型文件
```bash
./extract_screencaps_advanced.sh -t png,jpg
```
**结果**：只提取图片文件

### 5. 预览模式（不实际复制）
```bash
./extract_screencaps_folders.sh -d
```
**结果**：显示将要执行的操作，但不实际复制

## 详细使用说明

### extract_screencaps.sh（基础版）
```bash
# 基本使用
./extract_screencaps.sh

# 特点：
# - 自动查找所有screencaps文件夹
# - 将文件重命名为 screencaps1_1.png, screencaps1_2.jpg 等
# - 简单易用，适合快速提取
```

### extract_screencaps_advanced.sh（高级版）
```bash
# 显示帮助
./extract_screencaps_advanced.sh -h

# 基本使用
./extract_screencaps_advanced.sh

# 指定输出目录
./extract_screencaps_advanced.sh -o /tmp/my_screenshots

# 保持目录结构
./extract_screencaps_advanced.sh -s

# 只提取图片文件
./extract_screencaps_advanced.sh -t png,jpg,jpeg

# 复制整个文件夹
./extract_screencaps_advanced.sh -f

# 预览模式
./extract_screencaps_advanced.sh -d

# 详细输出
./extract_screencaps_advanced.sh -v

# 组合使用
./extract_screencaps_advanced.sh -f -o /tmp/folders -d -v
```

### extract_screencaps_folders.sh（文件夹版）
```bash
# 显示帮助
./extract_screencaps_folders.sh -h

# 基本使用（推荐）
./extract_screencaps_folders.sh

# 指定输出目录
./extract_screencaps_folders.sh -o /tmp/screencaps_backup

# 预览模式
./extract_screencaps_folders.sh -d
```

## 输出结果说明

### 文件命名规则

#### 扁平化模式：
- 单个screencaps文件夹：`screencaps_1.png`, `screencaps_2.jpg`
- 多个screencaps文件夹：`screencaps1_1.png`, `screencaps2_1.jpg`

#### 文件夹模式：
- 保持上级目录结构：`parent_dir/screencaps/`
- 重名处理：`parent_dir_2/screencaps/`, `parent_dir_3/screencaps/`

**示例**：
- 原始：`test1/screencaps/` → 复制为：`test1/screencaps/`
- 原始：`project_a/screencaps/` → 复制为：`project_a/screencaps/`
- 重名：两个`test1/screencaps/` → 复制为：`test1/screencaps/` 和 `test1_2/screencaps/`

### 统计信息
所有脚本都会显示：
- 找到的文件夹数量
- 处理的文件数量
- 文件类型统计
- 操作结果

## 常见问题

### Q: 如何选择合适的脚本？
**A**: 
- 需要快速提取所有文件 → 使用基础版
- 需要保持文件夹结构 → 使用文件夹版（推荐）
- 需要高级功能（过滤、预览等）→ 使用高级版

### Q: 文件夹版和高级版的-f选项有什么区别？
**A**: 
- 文件夹版：专门设计用于复制文件夹，功能更稳定
- 高级版-f：作为高级版的一个功能选项

### Q: 如何处理重名冲突？
**A**: 
- 脚本会自动检测重名并添加后缀（如 `screencaps_2`）
- 不会覆盖已存在的文件或文件夹

### Q: 支持哪些文件类型？
**A**: 
- 默认：所有文件类型
- 高级版可通过 `-t` 参数指定特定类型

## 安全提示

1. **使用预览模式**：首次使用建议加上 `-d` 参数预览
2. **备份重要数据**：复制前确保原始数据有备份
3. **检查磁盘空间**：确保目标目录有足够空间
4. **权限检查**：确保对源目录和目标目录有适当权限

## 示例场景

### 测试报告整理
```bash
# 将所有测试的截图文件夹复制到报告目录
./extract_screencaps_folders.sh -o /path/to/test_report/screenshots
```

### 问题排查
```bash
# 只提取图片文件进行问题分析
./extract_screencaps_advanced.sh -t png,jpg -o /tmp/debug_images
```

### 存储清理
```bash
# 预览将要提取的内容
./extract_screencaps_folders.sh -d
# 确认后执行实际提取
./extract_screencaps_folders.sh -o /backup/screencaps
```
