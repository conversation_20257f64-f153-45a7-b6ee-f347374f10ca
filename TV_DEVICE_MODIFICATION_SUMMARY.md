# TV设备ID自动化修改总结

## 修改概述
已成功修改 `script/testcases/adb_command.py` 文件，实现从配置文件自动读取TV设备ID，无需手动传递设备参数。

## 主要修改内容

### 1. 配置文件读取
- 添加了 `read_smartshare_config()` 函数，从 `script/smartshare/config.ini` 读取设备信息
- 自动读取 `[devices_info]` 节下的 `tv_id` 参数
- 设置全局变量 `TV_DEVICE_ID = 70070206600000030`

### 2. timeout_command函数增强
- 修改 `timeout_command()` 函数，当 `device` 参数为 `None` 时自动使用全局 `TV_DEVICE_ID`
- 自动在adb命令中插入 `-s {device}` 参数

### 3. 所有遥控器函数简化
移除了所有函数的 `device` 参数，现在这些函数会自动使用全局设备ID：

**遥控器操作函数：**
- `adb_home()` - 按Home键
- `adb_back()` - 按Back键  
- `adb_up()` - 按向上键
- `adb_down()` - 按向下键
- `adb_left()` - 按向左键
- `adb_right()` - 按向右键
- `adb_center()` - 按确定键
- `adb_volume_up()` - 音量调大
- `adb_volume_down()` - 音量调小
- `adb_power()` - 电源键
- `adb_menu()` - 菜单键

**快速操作函数：**
- `adb_quickup()`, `adb_quickdown()`, `adb_quickleft()`, `adb_quickright()`
- `longpress_left()`, `longpress_right()`, `longpress_power()`
- `press_power()`, `breathing_screen(times)`

**应用和系统函数：**
- `start_app(package_activity, wait_time=10)`
- `start_service(package_service, wait_time=10)`
- `start_broadcast(package_broadcast, wait_time=10)`
- `get_front_package()`
- `back2home_page()`

**设备检测函数：**
- `get_tv_manufacturer()`
- `is_xiaomi_tv()`, `is_tcl_tv()`, `is_hisense_tv()`, `is_huawei_tv()`

**截图函数：**
- `ensure_screencap_dir()`
- `xiaomi_screencap(screenshot_2, screen_count, capture_path)`
- `generic_screencap(screenshot_2, screen_count, capture_path)`
- `tcl_keyevent_screencap(screenshot_2, screen_count, capture_path)`
- `tvqs_screeencap(screenshot_2=10, interation=1, delay_start=3, interval_time=60, rename_keyword="screencap", screen_count=2)`

## 使用方法

### 之前的使用方式（已废弃）：
```python
device_id = "70070206600000030"
adb_home(device_id)
adb_back(device_id)
tvqs_screeencap(device=device_id)
```

### 现在的使用方式（推荐）：
```python
# 直接调用，自动使用配置文件中的设备ID
adb_home()
adb_back()
tvqs_screeencap()
```

## 配置文件位置
`script/smartshare/config.ini`:
```ini
[devices_info]
phone_id = 711d9be
tv_id = 70070206600000030
```

## 验证结果
- ✅ 成功从配置文件读取TV设备ID: `70070206600000030`
- ✅ 所有adb命令自动添加 `-s 70070206600000030` 参数
- ✅ 所有函数调用无需手动传递设备参数
- ✅ 向后兼容，现有代码无需修改即可工作

## 优势
1. **简化调用** - 无需每次都传递设备参数
2. **集中管理** - 设备ID统一在配置文件中管理
3. **自动化** - 启动时自动读取配置，无需手动设置
4. **向后兼容** - 现有代码无需修改
5. **易于维护** - 更换设备只需修改配置文件

## 注意事项
- 如果配置文件不存在或读取失败，`TV_DEVICE_ID` 将为 `None`，此时adb命令将使用默认设备
- 确保 `script/smartshare/config.ini` 文件存在且格式正确
- 设备ID格式应与adb设备列表中显示的一致
