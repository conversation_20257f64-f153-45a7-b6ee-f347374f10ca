#! /usr/bin/python
# _*_ coding:utf-8 _*_
import sys
import os
import time,datetime
from feishu.feishu import <PERSON><PERSON><PERSON><PERSON>
from logger import add_log
from basic_adb import Basic_ADB
from tvadb.tv_adb import TvAdb

class ADB_Guard():
    """作为子线程被调起，主线程fail之后，子线程应该自动就会fail了"""
    def __init__(self,device_id,build_url = "",build_user = "unknown"):
        self.device_id = device_id
        self.build_url = build_url
        self.build_user = build_user
        self.basic_adb = Basic_ADB(self.device_id)
        self.tv = TvAdb()
        self.feishu = FeiShu()
        print("ADB guard is going to guard device : {}".format(self.device_id))
        if not os.path.exists("monitor_adb_device"):
            os.mkdir("monitor_adb_device")
        log_folder = os.path.join("monitor_adb_device", time.strftime("%Y%m%d-%H%M%S"))
        os.makedirs(log_folder)
        self.monitor_log = os.path.join(log_folder, "execute.log")
        add_log(self.monitor_log,"result save to: {}".format(log_folder))
        self.running = True

    def run_guard(self):
        start = time.time()
        while self.running == True:
            if self.check_connect() == True:
                start = time.time()
            else:
               if time.time() - start >= 1600:  # 1hour = 60 * 60
                    add_log(self.monitor_log,"{} : 设备{}状态异常超过半小时，发送飞书通知".format(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'),self.device_id))
                    self.feishu.send_message_interactive(receiver=self.build_user,
                                                    subject="Monkey测试adb异常断连通知",
                                                    message_items={"执行人": self.build_user,
                                                                   "电视ip地址": self.device_id,
                                                                   "压测任务地址": self.build_url,
                                                                   "备注": "电视adb异常断连超过半小时，请检查电视状态"},
                                                    subject_background_color="red")
                    start = time.time()  # 超时之后，更新start时间，重新计时
            time.sleep(120)
        else:
            add_log(self.monitor_log,"{} : 测试执行完成，disconnect电视设备，退出adb guard".format(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S')))
            self.basic_adb.adb_disconnect()

    def check_connect(self):
        state1 = self.basic_adb.timeout_command("adb -s {} shell 'ls -d'".format(self.device_id),timeout=5,output=True)
        add_log(self.monitor_log,"{} : check device:{}".format(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'),state1))
        if state1 == '.':
            return True
        else:
            if self.tv.reconnect(device_id=self.device_id):
                time.sleep(3)
                self.basic_adb.adb_root()
                # self.basic_adb.adb_remount()
            return False

if __name__ == '__main__':
    device_id = sys.argv[1]
    build_url = sys.argv[2]
    build_user = sys.argv[3]
    guard = ADB_Guard(device_id,build_url,build_user)
    guard.run_guard()