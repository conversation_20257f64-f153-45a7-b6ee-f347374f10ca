#!/system/bin/sh
foldername=$1
procrank  2>/dev/null > /sdcard/MiTVTest/${foldername}/procrank.log &
sleep 3
dmesg > /sdcard/MiTVTest/${foldername}/dmesg.log &
sleep 5
mount > /sdcard/MiTVTest/${foldername}/mount.log &
sleep 1
#在/sdcard目录下无法写入此dumpsys
dumpsys meminfo > /data/local/tmp/dumpsys_meminfo.log &
sleep 5
mv/data/local/tmp/dumpsys.log /sdcard/MiTVTest/${foldername}/

#在/sdcard目录下无法写入此dumpsys
dumpsys cpuinfo > /data/local/tmp/dumpsys_cpuinfo.log &
sleep 1
mv/data/local/tmp/dumpsys.log /sdcard/MiTVTest/${foldername}/

top -m 10 -n 2 > /sdcard/MiTVTest/${foldername}/top.log &
sleep 6
#tcpdump -c 6 > /sdcard/MiTVTest/${foldername}/tcpdump.log &
sleep 3
#dumpstate > /sdcard/MiTVTest/${foldername}/dumpstate.log &
#bugreport > /sdcard/MiTVTest/${foldername}/bugreport.log
