# -*- coding:utf-8 -*-
# todo 解析logcat以外的bug，如reboot，黑屏
import os
import shutil
import argparse
import time

from tvadb.tv_adb import TvAdb
try:
    from log_parser import LogParser,Prop,add_log
    from Dispaly_Detection import *
    from image_compare import *
    from frame_detection import *
except Exception as e:
    from .log_parser import Log<PERSON>ars<PERSON>,Prop,add_log
    from .Dispaly_Detection import *
    from .image_compare import *
    from .frame_detection import *
class AnalysisError():
    def __init__(self,device_id,resultFolder):
        """
        解析日志，按一次压测
        :param device_id:
        :param resultFolder: 本次压测的result folder
        """
        self.device_id = device_id
        self.resultfolder = resultFolder
        self.tv = TvAdb()
        self.prop = Prop(self.device_id,self.resultfolder)
        self.parse = LogParser(self.device_id,self.resultfolder)

    def beforecase(self,case_folder):
        """
        在case开始的时候，记录电视基本信息用于后续判断reboot
        :param case_folder: 当前case目录（已经包含了resultfolder了）
        :return:
        """
        self.case_folder = case_folder
        self.ifIssue_dict = {"reboot_kernel": 0,
                             "reboot_systemserver": 0,
                             "reboot_hardware": 0,
                             "system_server_hung": 0,
                             "video_screen": 0,
                             "start_video":0
                             }
        self.basic_info = {"reboot_flag": None,
                           "pid_before": 0,
                           "base_version": None,
                           "launcher_mode": None,
                           "SupportSimpleMode": None,
                           "system_mode": None,
                           "system_server_start_count":-1}
        self.get_base_info(self.case_folder)

    def analysing(self,case_folder,save_bug_info=False,miplayererror = False,rm_pictures=False,deadsystem=False,miracast = False,speedui=False):
        """
        自动解析错误日志
        :param case_folder: 本次case的case folder
        :param save_bug_info: 是否保存issuefile、data/log等信息，默认为不存
        :param miplayererror: 是否解析小米播放器异常（音视频异常、黑屏），默认为不解析
        :param rm_pictures: 判断黑屏，如果没有异常，是否删除截图，默认为不删除
        :return:
        """
        print("\n************************* Analysis Error Log *************************")
        self.case_folder = case_folder
        self.caselog = os.path.join(self.case_folder,"case.log")

        self.logstack_log = os.path.join(self.case_folder,"logstack.log")
        ana_time = time.time()
        self.product_name = self.prop.getprop("ro.product.name")
        self.affectsVersion = self.prop.get_version()
        self.chipset = self.prop.getprop("ro.boot.mi.panel_size")
        self.product_model = self.prop.get_tv_model()
        self.testcaseName = self.parse.get_testcaseName()
        self.catch_reboot()    # judge
        self.parse.parse_logcat(casefolder=self.case_folder,save_bug_info=save_bug_info,grep_fc=True,grep_anr=True,grep_tb=True,grep_hdmi=True,grep_wifi=True,grep_miplayer=miplayererror,grep_deadsystem = deadsystem,grep_miracast= miracast,grep_speedui = speedui)
        if miplayererror:    # 小米播放器异常--黑屏
            self.compare_startvideo()
            self.catch_screen_display_error(rm_pictures=rm_pictures)
        print("check1:",self.ifIssue_dict)
        self.ifIssue_dict.update(self.parse.ifIssue_dict)    #
        print("check2:",self.ifIssue_dict)

        if self.ifIssue_dict["reboot_kernel"] + self.ifIssue_dict["reboot_systemserver"] + self.ifIssue_dict["reboot_hardware"] + self.ifIssue_dict["system_server_hung"] >= 1 :
            print("reboot happened,wait 1 min before pull log")
            time.sleep(60)      # 如果有重启异常，多等1min再pull日志

        for key in ["reboot_kernel","reboot_systemserver","reboot_hardware","system_server_hung","anr","fc","tombstone","wifi"]:   # 只有这些异常需要pull log，其他的异常先不pull 了
        # for key in self.ifIssue_dict.keys():     # 只在这里pull log
            if self.ifIssue_dict[key] != 0:
                pull_time = time.time()
                if self.tv.reconnect(device_id=self.device_id,timeout=5*60):
                    self.parse.pullLogs()
                    print("only pull log use time:",time.time() - pull_time)
                if self.tv.reconnect(device_id=self.device_id, timeout=5 * 60):
                    self.parse.save_bugreport()
                    print("pull log & bugreport use time:",time.time() - pull_time)
                else:
                    print("fail to pull logs cause cannot connect tv in 10 minutes")
                break    # pull一次即可
        print("ana use time:{}",time.time() - ana_time)

    def catch_reboot(self):
        """
        捕获reboot，先判断kernel reboot，再判断system server reboot
        # 先检查kernel reboot，如果发生了kernel reboot，需要重新赋予root权限，才能判断tombstone
        # catch reboot:(kernel reboot or system_server reboot)
        # 1. 测试前记录  /data/diagnosis/boot_log.tar.gz 这个文件的大小, 测试后比较下, 如果发生改变, 即发生了重启, androitv 没有diagnosis服务,故排除此项
        # 2. 接着检查 /proc/cmdline 输出中的 androidboot.reboot_mode的值，如果包含watchdog,或panic问题，否则system_server问题
        :return:
        """
        print("=============== catch Reboot ===============")
        PidSysAfter, current_version, launcher_mode_after, SupportSimpleMode, system_mode_after,system_server_start_count_after = self.get_base_info(case_folder=self.case_folder,pre_case=False)
        print("system server start count before:{}".format(self.basic_info["system_server_start_count"]))
        print("system server start count after:{}".format(system_server_start_count_after))
        print("android version:".format(self.prop.getprop("ro.build.version.release")))

        system_server_restart = False
        try:
            rebootType = self.tv.send_adb_command("adb -s {} shell 'cat /proc/cmdline'".format(self.device_id),output=True)
            reboot_flag = self.tv.send_adb_command("adb -s {} shell 'getprop mi.reboot.flag'".format(self.device_id),output=True)
            sys_boot_reason = self.tv.send_adb_command("adb -s {} shell 'getprop sys.boot.reason'".format(self.device_id),output=True)
            print("reboot_flag after : {}".format(reboot_flag))
            if not reboot_flag:     # kernel reboot
                self.tv.reconnect(self.device_id)
                self.tv.send_adb_command("adb -s {} root".format(self.device_id))
                self.tv.send_adb_command("adb -s {} shell 'setprop mi.reboot.flag True'".format(self.device_id))   # 重置reboot flag

                rebootType = self.tv.send_adb_command("adb -s {} shell 'cat /proc/cmdline'".format(self.device_id), output=True)   # 重新获取一次rebootType
                if "=watchdog" in rebootType or "=panic" in rebootType or "=kernel_panic" in rebootType:
                    print("===Kernel Reboot happened===")
                    add_log(self.caselog, "===Kernel Reboot happened===")
                    self.ifIssue_dict["reboot_kernel"] += 1
                    add_log(self.caselog, "===Kernel Reboot happened===")
                    add_log(self.caselog, "reboot_flag after : {}".format(reboot_flag), output=True)
                    add_log(self.caselog,"rebootType = {}".format(rebootType))
                    add_log(self.logstack_log,"After RebootTime : {}".format(time.ctime()),output=True)
                    self.tv.send_adb_command("adb -s {} pull /data/crashdump-1.bin {}".format(self.device_id,self.case_folder))
                    self.get_reboot_info(targetPkg="kernel")    # 生成issuefile

                elif "reboot_mode=cold_boot" in rebootType:   #mibox因断电发生重启
                    add_log(self.caselog, "===Hardware Reboot happened===")
                    print("===Hardware Reboot happened===")
                    self.ifIssue_dict["reboot_hardware"] += 1
                    add_log(self.caselog, "reboot_flag after : {}".format(reboot_flag), output=True)
                    add_log(self.caselog, "rebootType = {}".format(rebootType))
                    add_log(self.logstack_log, "After terRebootTime : {}".format(time.ctime()), output=True)
                    self.get_reboot_info(targetPkg="hardware_reboot")  # 生成issuefile

                # elif "system_server_hung" in sys_boot_reason:     # 改成根据system server start count来判断

                #     self.ifIssue_dict["system_server_hung"] += 1
                #     add_log(self.caselog,"===SystemServerHung Reboot happened===")
                #     print("===SystemServerHung Reboot happened===")
                #     add_log(self.caselog, "reboot_flag after : {}".format(reboot_flag), output=True)
                #     add_log(self.caselog,"boot_reason:{}".format(sys_boot_reason), output=True)
                #     add_log(self.logstack_log, "After terRebootTime : {}".format(time.ctime()), output=True)
                #     self.get_reboot_info(targetPkg="system_server_hung")


            if "reboot_mode=normal" in rebootType:
                system_server_restart = True
                add_log(self.caselog,"system server normal restart",output=True)

            if self.ifIssue_dict["reboot_kernel"] == False:
                PidSysBefore = self.basic_info["pid_before"]  # system server reboot
                if PidSysAfter == PidSysBefore:
                    return
                try:
                    if system_server_restart and int(PidSysAfter) - int(PidSysBefore) > 1200:
                        # todo 判断是否有发生ota智能升级
                        base_version = self.basic_info["base_version"]
                        if current_version != base_version:
                            print("Maybe happened OTA. base version: {},current version: {}".format(base_version,current_version))
                            add_log(self.caselog,
                                    "Maybe happened OTA. base version: {},current version: {}".format(base_version,current_version),output=True)
                            return

                        # todo 判断是否有发生桌面模式切换
                        launcher_mode_before = self.basic_info["launcher_mode"]
                        if launcher_mode_after != "null" and launcher_mode_before != launcher_mode_after:
                            add_log(self.caselog, "launcher mode after test : {}".format(launcher_mode_after),
                                    output=True)
                        # todo 极简模式切换判断
                        elif self.basic_info["SupportSimpleMode"] == 1:
                            add_log(self.caselog, "current system mode after:{}".format(system_mode_after), output=True)
                            if self.basic_info["system_mode"] == system_mode_after:
                                add_log(self.caselog, "===SystemServer Reboot happened===")
                                print("===SystemServer Reboot happened===")
                                self.ifIssue_dict["reboot_systemserver"] += 1
                                add_log(self.caselog, "rebootType = {}".format(rebootType))
                                add_log(self.logstack_log, "AfterRebootTime : {}".format(time.ctime()), output=True)
                                self.get_reboot_info(targetPkg="system_server")
                            else:
                                add_log(self.caselog, "System mode switched", output=True)  # 由于极简模式切换导致的reboot
                        else:
                            add_log(self.caselog, "===SystemServer Reboot happened===")
                            print("===SystemServer Reboot happened===")
                            self.ifIssue_dict["reboot_systemserver"] += 1
                            add_log(self.caselog, "rebootType = {}".format(rebootType))
                            add_log(self.logstack_log, "AfterRebootTime : {}".format(time.ctime()), output=True)
                            self.get_reboot_info(targetPkg="system_server")
                except Exception as e:
                    pass

                # todo #如果case开始前，读取不到pid，电视可能是出问题了
            elif self.basic_info["pid_before"] == 0:
                add_log(self.caselog, "Can not get the Pid before the case", output=True)
                # self.parse.save_bugreport()   # 这里没有改变ifIssue,pullLog中有控制不重复pull的设定
                # self.parse.pullLogs()

        except Exception as e:
            print("catch reboot error:{}".format(e))
        finally:
            # 如果case前后pid相等，说明没有发生reboot，清除之前的logcat缓存
            if self.basic_info["pid_before"] == PidSysAfter and PidSysAfter != 0:
                print("clear adb logcat cache...")
                self.tv.send_adb_command("adb -s {} shell 'logcat -b all -c'".format(self.device_id),output = True)  # 清除adb logcat缓存
            if self.ifIssue_dict["reboot_kernel"] or self.ifIssue_dict["reboot_systemserver"] or self.ifIssue_dict["reboot_hardware"] or self.ifIssue_dict["system_server_hung"]:
                self.set_tvhome_test_env()    # 如果有发生重启，则需要重新打开accessibility service
                # time.sleep(120)     # reboot 生成的trace文件需要等待一会儿才在/data/log目录生成，然后再pull
                # self.parse.save_bugreport()
                # self.parse.pullLogs()

    def get_reboot_info(self,targetPkg):
        """
        :param targetPkg: reboot类型  kernel / system_server
        :return:
        """
        issuefile = os.path.join(self.case_folder, "issue.reboot_kernel")
        logstackfile = os.path.join(self.case_folder,"logstack.reboot_kernel")
        if targetPkg == "system_server":
            issuefile = os.path.join(self.case_folder, "issue.reboot_systemserver")
            logstackfile = os.path.join(self.case_folder, "logstack.reboot_systemserver")
        elif targetPkg == "hardware_reboot":
            issuefile = os.path.join(self.case_folder, "issue.reboot_hardware")
            logstackfile = os.path.join(self.case_folder, "logstack.reboot_hardware")
        elif targetPkg == "system_server_hung":
            issuefile = os.path.join(self.case_folder,"issue.system_server_hung")
            logstackfile = os.path.join(self.case_folder,"logstack.system_server_hung")

        add_log(issuefile, "Project:{}".format(self.product_name),output=True)
        add_log(issuefile, "Target:{}".format(self.testcaseName),output=True)
        add_log(issuefile, "Summary:执行自动化测试时, {}发生Reboot,testcase={}".format(targetPkg,self.testcaseName),output=True)
        add_log(issuefile, "Component:{}".format(targetPkg),output=True)
        add_log(issuefile, "affectVersion:{}".format(self.affectsVersion),output=True)
        add_log(issuefile,"Description:执行自动化测试发生Reboot. 具体自动化执行过程log请看服务器上的case.log",output=True)
        add_log(issuefile, "Chipset:{}".format(self.chipset),output=True)
        if targetPkg == "kernel":
            add_log(logstackfile,"kernel reboot")
            add_log(logstackfile, self.tv.send_adb_command("adb -s {} shell 'cat /proc/cmdline'".format(self.device_id)), output=True)
        elif targetPkg == "system_server":
            add_log(logstackfile, "systemserver reboot",output=True)
        elif targetPkg == "hardware_reboot":
            add_log(logstackfile,"hardware reboot")
            add_log(logstackfile, self.tv.send_adb_command("adb -s {} shell 'cat /proc/cmdline'".format(self.device_id)), output=True)

    def get_base_info(self,case_folder,pre_case = True):
        """
        读取与reboot有关的信息，case开始之前读一次，case结束之后读一次，统统写入文件，后续解析通过文件来读取
        :param case_folder: 本次case的目录
        :param pre_case: 在case开始之前读 true，在case结束后读，false
        :return:
        """

        get_reboot_flag = self.tv.send_adb_command("adb -s {} shell 'getprop mi.reboot.flag'".format(self.device_id),output=True)
        ps_state = self.tv.send_adb_command("adb -s {} shell \"ps|grep system_server\"".format(self.device_id))
        ps_state = ps_state.split(" ")
        pid_state = [p_i for p_i in ps_state if p_i != ""]  # 去掉空格符1
        system_pid = pid_state[1]
        PidSyslog = os.path.join(case_folder,"PidSys.log")
        add_log(PidSyslog,system_pid,output=True)

        date_ = self.tv.send_adb_command('adb -s {} shell date +%s'.format(self.device_id),output=True)
        uptime_ = self.tv.send_adb_command("adb -s {} shell 'cat /proc/uptime'".format(self.device_id),output=True).split(".")[0]
        reboot_ = float(date_) - float(uptime_)
        uptime_log = os.path.join(case_folder, "uptime.log")

        self.android_version = self.prop.getprop("ro.build.version.release")
        self.android_version = int(self.android_version.split('.')[0])
        system_server_start_count = self.tv.send_adb_command("adb -s {} shell getprop sys.system_server.start_count".format(self.device_id), output=True)

        # if self.android_version >= 14:    # android U 以上的版本才有这个字段，目测只有finch能读到这个字段，其他项目都读不到
        #     for i in range(1):
        #         system_server_start_count = self.tv.send_adb_command("adb -s {} shell getprop sys.system_server.start_count".format(self.device_id),output=True)
        #         try:
        #             system_server_start_count = int(system_server_start_count)
        #             break
        #         except:
        #             # print("system server start count 异常，等待100秒后重试")
        #             # time.sleep(100)
        #             # self.tv.reconnect(self.device_id)    # root
        #             # self.tv.send_adb_command("adb -s {} root".format(self.device_id))
        #             pass
        # else:
        #     system_server_start_count = -1

        if pre_case:
            add_log(uptime_log, "uptimeBefore : {}".format(uptime_), output=True)
            add_log(uptime_log, "rebootBefore : {}".format(reboot_), output=True)  # todo 要把这个时间转成日期
            add_log(uptime_log,"system server start count Before : {}".format(system_server_start_count),output=True)
        else:
            add_log(uptime_log, "uptimeAfter : {}".format(uptime_), output=True)
            add_log(uptime_log, "rebootAfter : {}".format(reboot_), output=True)  # todo 要把这个时间转成日期
            add_log(uptime_log,"system server start count After : {}".format(system_server_start_count),output=True)


        if pre_case:
            dropbox_log = os.path.join(case_folder, "before_dumpsys_dropbox.log")
        else:
            dropbox_log = os.path.join(case_folder, "after_dumpsys_dropbox.log")
        # add_log(dropbox_log, after_dumpsys_dropbox)
        self.tv.send_adb_command("adb -s {} shell 'dumpsys dropbox --print > {}'".format(self.device_id, dropbox_log))

        build_version = self.get_build_version()

        launcher_mode = self.tv.send_adb_command(
            'adb -s {} shell "settings get system system_launcher_mode"'.format(self.device_id), output=True)
        SupportSimpleMode = self.tv.send_adb_command(
            "adb -s {} shell 'settings get system mitv.settings.support.simple_mode'".format(self.device_id), output=True)

        system_mode = self.tv.send_adb_command(
            "adb -s {} shell 'settings get system mitv.settings.simple_mode'".format(self.device_id), output=True)
        if pre_case:   # 保存在self.basic_info的dict中
            self.basic_info["pid_before"] = system_pid
            self.basic_info["base_version"] = build_version
            self.basic_info["launcher_mode"] = launcher_mode
            self.basic_info["SupportSimpleMode"] = SupportSimpleMode
            self.basic_info["system_mode"] = system_mode
            self.basic_info["system_server_start_count"] = system_server_start_count
            self.caselog = os.path.join(case_folder, "case.log")
            add_log(self.caselog, "reboot_flag before : {}\n base_version : {}\nlauncher mode before : {}\n system_mode : {}\n system server start count".format(get_reboot_flag,build_version,launcher_mode,system_mode,system_server_start_count), output=True)
        return system_pid,build_version,launcher_mode,SupportSimpleMode,system_mode,system_server_start_count

    def get_build_version(self):
        """
        读取tv的软件版本
        """
        build_software_version = self.tv.send_adb_command("adb -s {} shell 'getprop ro.build.software.version'".format(self.device_id),output=True)
        build_display_id = self.tv.send_adb_command("adb -s {} shell 'getprop ro.build.display.id'".format(self.device_id),output=True)
        build_version_incremental = self.tv.send_adb_command("adb -s {} shell 'getprop ro.build.version.incremental'".format(self.device_id),output=True)
        if len(build_version_incremental.split(".")) > 1:  # koki
            build_version = build_version_incremental
        elif len(build_software_version.split(".")) > 3:
            build_version = build_software_version
        else:
            build_version = build_display_id
        return build_version

    def set_tvhome_test_env(self):
        """
        打开accessibility service，才能定位桌面的ui控件
        """
        print("set accessibility service")
        self.tv.send_adb_command("adb -s {} shell 'setprop tvhome.test_env true'".format(self.device_id))
        time.sleep(2)
        self.tv.send_adb_command("adb -s {} shell 'pkill com.mitv.tvhome'".format(self.device_id))
        time.sleep(2)

    def compare_startvideo(self,screencapsfolder="screencaps"):
        print("=============== catch_start_video(检测启播失败) ===============")
        imgs_path = os.path.join(self.case_folder, screencapsfolder)
        if not os.path.exists(imgs_path):
            return
        targetPkg = "start_video"
        for img_name in os.listdir(imgs_path):
            if "startVideo_screencap" in img_name and "-1." in img_name:    # 启播截图前缀
                img1_dir = os.path.join(imgs_path, img_name)
                img2_dir = img1_dir.replace("-1.", "-2.")
                img3_dir = img1_dir.replace("-1.", "-3.")
                print(img1_dir)
                print(img2_dir)
                print(img3_dir)
                try:
                    if os.path.exists(img2_dir) and hamming_compare(img1_dir,img2_dir) and os.path.exists(img3_dir) and hamming_compare(img2_dir,img3_dir): # 从左到右条件逐个满足
                        # 表示3张图片一样，启播失败
                        self.ifIssue_dict["start_video"] += 1
                        start_video_index = self.ifIssue_dict["start_video"]
                        self.parse.write_issuefile(start_video_index, targetPkg=targetPkg, suffix=targetPkg)
                        logstack_file = os.path.join(self.case_folder, "logstack{}.{}".format(start_video_index, targetPkg))
                        add_log(logstack_file, "启播失败:{} & {} & {}".format(img_name, img_name.replace("-1.", "-2."),img_name.replace("-1.", "-3.")),output=True)
                except Exception as e:
                    print("检测启播失败报错",e)
        if self.ifIssue_dict["start_video"] > 0:
            add_log(self.caselog, "===Start Video fail happened===")  # caselog
            print("===Start Video fail happened===")

    def catch_screen_display_error(self,screencapsfolder="screencaps",rm_pictures = False):
        """
        屏幕显示异常（黑屏、绿屏等）
        连续的两张黑图，判断为出现黑屏，连续的前后两张图命名规则后缀带有1和2，前面所有均一致
        解析如果没有异常，是否删除截图，节省服务器内存
        """
        print("=============== catch_screen_display_error(检测播放黑屏) ===============")
        imgs_path = os.path.join(self.case_folder, screencapsfolder)
        if not os.path.exists(imgs_path):
            return
        # video_screen_index = 0
        targetPkg = "video_screen"
        for img_name in os.listdir(imgs_path):
            if "-1." in img_name and not img_name.startswith("start"):
                img1_dir = os.path.join(imgs_path, img_name)
                img2_dir = img1_dir.replace("-1.","-2.")
                print(img1_dir)
                print(img2_dir)
                try:
                    if os.path.exists(img2_dir):   # 确认第二张图存在
                        if detectImagesByModel([img1_dir,img2_dir]):
                        # if detectImageByModel(img1_dir) and detectImageByModel(img2_dir):    # 连续两张异常,true为异常
                        # if single_color(img1_dir) and single_color(img2_dir):   # 两张连续黑屏才判定为黑屏
                            self.ifIssue_dict["video_screen"] += 1
                            video_screen_index = self.ifIssue_dict["video_screen"]
                            self.parse.write_issuefile(video_screen_index,targetPkg=targetPkg,suffix=targetPkg)
                            logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(video_screen_index,targetPkg))
                            add_log(logstack_file, "屏幕显示异常:{} & {}".format(img_name,img_name.replace("-1.","-2.")), output=True)
                except Exception as e:
                    print("检测黑屏报错：",e)
        if self.ifIssue_dict["video_screen"] > 0:
            add_log(self.caselog, "===ScreenDisplay Error happened===")  # caselog
            print("===ScreenDisplay Error happened===")
        else:
            try:
                if rm_pictures and self.ifIssue_dict["start_video"] == 0:   # 并且没有启播失败，才删除整个文件夹
                    print("delete screencaps")
                    shutil.rmtree(os.path.join(self.case_folder, "screencaps"))
                shutil.rmtree(os.path.join(self.case_folder, "files"))  # 可能会case没来得及截图，没有这些文件夹
            except Exception as e:
                pass


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Parameters")
    parser.add_argument("--device_id", type=str, help="测试设备ip地址")
    parser.add_argument("--resultfolder", type=str, help="压测结果存放目录")
    parser.add_argument("--casefolder", type=str, help="本次case的目录")
    args = parser.parse_args()
    # todo 设置指定电视设备
    os.environ["ANDROID_SERIAL"]=args.device_id
    print("set environ ANDROID SERIAL = {}".format(args.device_id))

    parse = AnalysisError(device_id = args.device_id,resultFolder = args.resultfolder)
    # parse.beforecase(case_folder=args.casefolder)    # 一定每个case开始前先调用这个方法，否则会报错
    # parse.analysing(case_folder = args.casefolder,save_bug_info = True,miplayererror = True,rm_pictures = True)
    folderlist = []
    for filepath in os.listdir(args.resultfolder):    # 或者遍历resultfolder中的目录,按目录的时间顺序遍历
        temp_filepath = os.path.join(args.resultfolder,filepath)
        if os.path.isdir(temp_filepath):
            folderlist.append(temp_filepath)
    folderlist.sort(key=lambda x: os.stat(x).st_ctime)
    for temp_filepath in folderlist:
        print(temp_filepath)
        if os.path.isdir(temp_filepath):
            parse.beforecase(case_folder=temp_filepath)
            parse.analysing(case_folder=temp_filepath,save_bug_info = False,miplayererror = False,rm_pictures = False,deadsystem=False,miracast = False,speedui=False)


# python3 analysis_error.py --device_id 10.189.128.xx --resultfolder xxx --casefolder xxx --save_bug_info False --miplayererror False --rm_pictures False
