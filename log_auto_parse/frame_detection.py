#!/usr/bin/python3

import os
import torch
from ultralytics import <PERSON><PERSON><PERSON>

module_path = os.path.abspath(__file__) # 输出绝对路径
module_path = module_path.replace("frame_detection.py","best.pt")

def detectImageByModel(image):
    try:
        modelYOLO = YOLO(module_path, verbose=False)
        # modelYOLO.multi_scale = False
        #
        # input_tensor = fixed_preprocess(image, 1024).to('cpu')
        # with torch.no_grad():
        #     results = modelYOLO(input_tensor)
        results = modelYOLO(image)

        # 存储检测到的有效异常类别（去重）
        anomalies = set()

        for result in results:
            boxes = result.boxes
            if not hasattr(boxes, 'xyxy'): continue

            names = result.names  # 获取类别名称映射

            for i in range(len(boxes.xyxy)):
                # 跳过无效检测框
                if not hasattr(boxes, 'cls') or not hasattr(boxes, 'conf'):
                    continue

                cls = int(boxes.cls[i])
                conf = float(boxes.conf[i])
                category_name = names[cls]

                # 检查各类型置信度阈值
                if (
                        (category_name == 'flower' and conf >= 0.7) or
                        (category_name == 'color' and conf >= 0.4) or
                        (category_name == 'line' and conf >= 0.7)
                ):
                    anomalies.add(category_name)  # 用英文名存储便于后续映射

        # 转换为中文输出
        output_map = {
            'flower': '(花屏)',
            'color': '(绿屏)',
            'line': '(竖线屏)'
        }
        final_anomalies = [output_map[name] for name in anomalies]

        if final_anomalies:
            print(f'{image} ' + ','.join(final_anomalies))
            return True
        else:
            print(f'{image}(正常图片)')
            return False

    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return False

def detectImagesByModel(images):
    """
    images: List[str]  # 图片路径列表，长度为3
    """

    # assert len(images) == 3, "必须传入三张图片进行判断"

    try:
        modelYOLO = YOLO(module_path, verbose=False)
        output_map = {
            'flower': '花屏',
            'color': '绿屏',
            'line': '竖线屏',
             'snow': '雪花屏'
        }

        abnormal_flags = []  # 每张图是否异常（True/False）
        all_anomalies = []   # 存储每张图的异常信息（用于输出）

        for image in images:
            results = modelYOLO(image)
            anomalies = {}

            for result in results:
                boxes = result.boxes
                if not hasattr(boxes, 'xyxy'):
                    continue

                names = result.names

                for i in range(len(boxes.xyxy)):
                    if not hasattr(boxes, 'cls') or not hasattr(boxes, 'conf'):
                        continue

                    cls = int(boxes.cls[i])
                    conf = float(boxes.conf[i])
                    category_name = names[cls]

                    if (
                        (category_name == 'flower' and conf * 0.5 > 0.48) or
                        (category_name == 'color' and conf >= 0.9) or
                        (category_name == 'line' and conf >= 0.9) or
                        (category_name == 'snow' and conf >= 0.9)
                    ):
                        if category_name not in anomalies or conf > anomalies[category_name]:
                            anomalies[category_name] = conf

            if anomalies:
                # 有异常
                abnormal_flags.append(True)
                final_anomalies = [f'({output_map[k]}){round(v, 2)}' for k, v in anomalies.items()]
                all_anomalies.append(f"{image} " + ",".join(final_anomalies))
            else:
                # 正常图
                abnormal_flags.append(False)
                all_anomalies.append(f"{image}(正常图片)")


        if all(abnormal_flags):
            print("⚠️ 检测结果：屏幕异常")
            return '异常'
            return True
        else:
            print("✅ 检测结果：屏幕正常")
            # return '正常'
            return False

    except Exception as e:
        print(f"模型处理失败: {str(e)}")
        # return 'error'
        return False


if __name__ == '__main__':

    # files = os.listdir(r"files/")
    # example_images = [f for f in files if
    #                   f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
    # for image in example_images:
    #     detectImageByModel(fr'files/{image}')
    detectImagesByModel([r'/Users/<USER>/PycharmProjects/framedetect/files/mitv_20250417-172245.jpeg', r'/Users/<USER>/PycharmProjects/framedetect/files/mitv_20250417-172245.jpeg'])
