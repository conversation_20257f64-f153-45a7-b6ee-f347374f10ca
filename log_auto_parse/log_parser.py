# -*- coding:utf-8 -*-

import os
import re
import linecache
import traceback
import datetime
import time
import argparse
from tvadb.tv_adb import TvAdb

class LogParser():
    def __init__(self,device_id,resultFolder):   # 一次压测中import一次即可
        """
        自动解析logcat.log文件
        :param device_id: 设备id
        :param resultFolder: 压测执行结果目录
        :param save_bug_info: 是否需要保存issuefile和相关日志文件data/log等
        """
        self.device_id = device_id
        self.tv = TvAdb()
        google = self.tv.send_adb_command("adb -s {} shell 'pm list package | grep google'".format(self.device_id), output=True)
        self.is_domestic = False if google else True   # 是否是国内的平台
        print("check domestic:",self.is_domestic)
        self.resultfolder = resultFolder
        self.prop = Prop(self.device_id,self.resultfolder)    # 判断当前目录中是否有getprop.txt文件，如果没有，则生成一个，如果有，则直接读取
        self.issues_pid_dict = {"anr": [],    # 本次压测中的，后续不需要传参了
                                "fc": [],
                                "tombstone": []}     # 这三种类型的bug需要根据pid去重

    def parse_logcat(self,casefolder,logcat_file="logcat.log",save_bug_info=False,grep_anr=False,grep_fc=False,grep_tb=False,grep_hdmi=False,grep_wifi=False,grep_miplayer=False,grep_deadsystem = False,grep_miracast = False,grep_speedui = False,pull_log_now = False):  # 每一个casefolder执行一次
        """
        根据传参，解析logcat的异常
        :param casefolder: logcat.log的目录,必填字段
        :param logcat_file: logcat文件名，默认为logcat.log
        :param save_bug_info: 是否保存issuefile
        :param grep_fc: 是否解析fc
        :param grep_anr: 是否解析anr
        :param grep_tb:
        :param grep_hdmi:
        :param grep_wifi:
        :param grep_miplayer:
        :param pull_log_now: 如果出现异常，是否现在pull log，默认为false不pull log
        :return:
        """
        self.TV_Product_Model = self.prop.get_tv_product_model()
        self.product_name = self.prop.getprop("ro.product.name")
        self.ifIssue_dict = {"anr":0,     # 本次logcat的
                             "fc":0,
                             "tombstone":0,
                             "hdmi":0,
                             "audio_flinger":0,
                             "audio_pa":0,
                             "video_loading":0,
                             "video_decoder":0,
                             "video_output":0,
                             "video_display":0,
                             "wifi":0,
                             "deadsystem":0,
                             "p2p":0,
                             "rtsp_mtk":0,
                             "rtsp_aml":0,
                             "speedui":0,
                             "focusmode":0
                             }
        start_time = time.time()
        self.case_folder = casefolder
        self.adb_logcat = os.path.join(self.case_folder,logcat_file)
        #todo 2024.11.12 把判断播放or暂停，以及origin_pts的logcat抽出来打印到某个日志文件内
        self.video_log = os.path.join(self.case_folder, "video_logcat.log")
        if not os.path.exists(self.adb_logcat):
            print("**** Without logcat file, ignore this case ****")
            return
        decode_logcat(self.adb_logcat)    # logcat解码为utf-8格式，否则会报错
        self.ifBugreport = False
        self.isPullLogs = False
        if grep_fc:
            self.catch_fc(save_bug_info)
        if grep_anr:
            self.catch_anr(save_bug_info=save_bug_info)
        if grep_tb:
            self.catch_tb(save_bug_info=save_bug_info)
        if grep_hdmi:
            self.catch_hdmi(save_bug_info)
        if grep_wifi:
            self.catch_wifi(save_bug_info)
        if grep_deadsystem:
            self.catch_deadsystem(save_bug_info)
        if grep_miplayer:
            self.catch_audio_flinger_error(save_bug_info)
            # self.catch_audio_pa_error(save_bug_info)      # 2024.8.22 @zhangjingjing9
            self.catch_video_loading_error(save_bug_info)
            self.catch_video_decoder_error(self.is_domestic,save_bug_info)
            # self.catch_video_error(save_bug_info)      # 2024.12.18 @zhangjingjing9同意去掉该类错误

        if grep_miracast:    # miracast
            self.catch_miracast_p2p(save_bug_info)
            self.catch_miracast_RTSP(save_bug_info)

        if grep_speedui:
            self.catch_speedui(save_bug_info)
            self.catch_focusmode(save_bug_info)

        print(self.ifIssue_dict)
        print("\n")
        print("parse:",self.issues_pid_dict)
        print("\n")
        if pull_log_now:
            for key in self.ifIssue_dict.keys():
                if self.ifIssue_dict[key] != 0:
                    self.pullLogs()
                    self.save_bugreport()
                    break
        print("\n+++++++++ parse_logcat use time:{}\n".format(time.time() - start_time))

    def catch_anr(self,anr_path = None, save_bug_info=False):
        """
        解析anr的异常，
        :param save_bug_info:是否保存issuefile，默认不保存
        :return:
        """
        print("\n===================get anr info====================")
        print(anr_path)
        keyword = 'ActivityManager: ANR in'
        pid_pattern = "PID: \d+"
        linecache.clearcache()
        log = linecache.getlines(self.adb_logcat)
        for index, line in enumerate(log):  # 剔除 Monkey、tvservice和tvqs的异常
            if keyword in line and "Monkey" not in line and "xiaomi.tvservice" not in line and 'TVQS' not in line and 'tvqs' not in line and "system_server" not in line and "uiautomator" not in line and "busybox" not in line:    # ANR in pkg ,ignore uiautomator
                # todo 解析pid
                line_2 = log[index + 1]
                anr_pid_ = re.findall(pid_pattern, line_2)[0]
                anr_pid = re.findall("\d+",anr_pid_)[0]   # 匹配数字
                line_3 = log[index + 2]  # ActivityManager: Reason
                if "does not have a focused window" in line_3:
                    print("anr reason:does not have a focused window,过滤本次异常@李星2025.1.27")
                elif anr_pid in self.issues_pid_dict["anr"]:      # 去重
                    print("anr pid : {} 已上报，过滤本次异常".format(anr_pid))
                else:     # 一次压测中，同一个pid的issue只统计一次
                    self.issues_pid_dict["anr"].append(anr_pid)    # pid加入list中
                    self.ifIssue_dict["anr"] += 1    # 这也是issuefile的number，顺便统计本次logcat中anr的次数
                    print("ANR happened {}".format(anr_pid))
                    if save_bug_info:
                        logstack_num = self.ifIssue_dict["anr"]
                        logstack = log[index:index + 50]  # 保存50行文件到logstack{}.anr中
                        logstack_file = os.path.join(self.case_folder, "logstack{}.anr".format(logstack_num))
                        with open(logstack_file, "w") as p:
                            p.write("".join(logstack))

                        targetPkg = line.split('ANR in ')[1].split(' (')[0]
                        if targetPkg:
                            targetPkg = targetPkg.strip()
                            targetPkg = targetPkg.split(":")[0]
                            print("targetPkg : {}".format(targetPkg))
                            self.write_issuefile(logstack_num, targetPkg, suffix="anr")
        # todo parse anr file
        # anr 目录下，非trace开头的文件，解析cmd line 的包，用对应pid的进程号去重
        if not anr_path:
            anr_path = os.path.join(self.case_folder,"anr")
        if not os.path.exists(anr_path):
            self.tv.send_adb_command("adb -s {} pull /data/anr {}".format(self.device_id,self.case_folder))
        print("check anr file path:{}".format(anr_path))
        cmdline_pattern = "Cmd line: "
        pid_pattern = "pid \d+ at"
        if not os.path.exists(anr_path):
            print("no anr file")
            return
        for anr_file in os.listdir(anr_path):
            if "trace" in anr_file:    # 文件名中带trace的不算
                continue
            temp_file = os.path.join(anr_path,anr_file)     # 遍历每个anr文件，一个anr文件只匹配第一个Cmd line
            decode_logcat(temp_file)
            linecache.clearcache()
            temp_anrfile = linecache.getlines(temp_file)
            for index, line in enumerate(temp_anrfile):
                if cmdline_pattern in line:
                    if "Monkey" in line or "xiaomi.tvservice" in line or 'TVQS' in line or 'tvqs' in line or "system_server" in line or "busybox" in line:    # 这些都是ignore的包
                        break
                    pid_line = temp_anrfile[index - 1]
                    anr_pid_ = re.findall(pid_pattern, pid_line)[0]
                    if not anr_pid_ : return
                    anr_pid = re.findall("\d+", anr_pid_)[0]
                    if anr_pid in self.issues_pid_dict["anr"]:  # 去重
                        print("anr pid : {} 已上报，过滤本次异常".format(anr_pid))
                        break
                    self.issues_pid_dict["anr"].append(anr_pid)  # pid加入list中
                    self.ifIssue_dict["anr"] += 1  # 这也是issuefile的number，顺便统计本次logcat中anr的次数
                    print("ANR happened {}".format(anr_pid))
                    if save_bug_info:
                        logstack_num = self.ifIssue_dict["anr"]
                        logstack = temp_anrfile[index-1:index + 50]  # 保存50行文件到logstack{}.anr中
                        logstack_file = os.path.join(self.case_folder, "logstack{}.anr".format(logstack_num))
                        with open(logstack_file, "w") as p:
                            p.write("".join(logstack))
                        targetPkg = line.split(cmdline_pattern)[1]
                        if targetPkg:
                            targetPkg = targetPkg.strip()
                            targetPkg = targetPkg.split(":")[0]
                            print("targetPkg : {}".format(targetPkg))
                            self.write_issuefile(logstack_num, targetPkg, suffix="anr")
                    break

    def catch_fc(self,save_bug_info=False):
        print("\n===================get fc info====================")
        keyword = 'AndroidRuntime: FATAL EXCEPTION'
        pid_pattern = "PID: \d+"
        linecache.clearcache()
        log = linecache.getlines(self.adb_logcat)
        for index, line in enumerate(log):  # 剔除 Monkey、tvservice和tvqs的异常
            if keyword in line and "Monkey" not in line and "xiaomi.tvservice" not in line and 'TVQS' not in line and 'tvqs' not in line and "busybox" not in line:
                # 解析pid出来
                line_2 = log[index + 1]
                if "tvqs" in line_2 or "Monkey" in line_2 or "xiaomi.tvservice" in line_2 or 'TVQS' in line_2 or 'tvqs' in line_2 or "busybox" in line:
                    continue     # 过滤tvqs的异常
                fc_pid = re.findall(pid_pattern, line_2)[0]
                if fc_pid in self.issues_pid_dict["fc"]:
                    print("fc pid : {} 已上报，过滤本次异常".format(fc_pid))
                else:
                    targetPkg = ""
                    if "Process" in line_2:
                        targetPkg = line_2.split('Process: ')[1].split(',')[0]
                    elif "PROCESS" in line:
                        targetPkg = line_2.split('PROCESS: ')[1].split(',')[0]
                    elif "Package" in line_2:
                        targetPkg = line_2.split('Package: ')[1].split(',')[0]
                    if targetPkg:
                        targetPkg = targetPkg.strip()
                        targetPkg = targetPkg.split(":")[0]
                        print("targetPkg : {}".format(targetPkg))
                        # todo 过滤uiautomator等的异常
                        if "tvqs" in targetPkg or "Monkey" in targetPkg or "xiaomi.tvservice" in targetPkg or 'TVQS' in targetPkg or 'tvqs' in targetPkg or "uiautomator" in targetPkg:
                            continue  # 过滤tvqs的异常

                        self.issues_pid_dict["fc"].append(fc_pid)
                        self.ifIssue_dict["fc"] += 1
                        print("FC happened {}".format(fc_pid))
                        if save_bug_info:     # 保存issuefile等信息
                            logstack_num = self.ifIssue_dict["fc"]
                            logstack = log[index:index + 50]  # 保存50行文件到logstack{}.anr中
                            logstack_file = os.path.join(self.case_folder, "logstack{}.fc".format(logstack_num))
                            with open(logstack_file, "w") as p:
                                p.write("".join(logstack))
                            self.write_issuefile(logstack_num, targetPkg, suffix="fc")

    def catch_tb(self,tombstone_path=None,save_bug_info = False):
        print("\n===================get tombstone info====================")
        linecache.clearcache()
        # todo 根据tombstone文件写入logsatack file和issuefile
        if not tombstone_path:  # 默认tombstone文件的目录，也可以传入
            tombstone_path = os.path.join(self.case_folder, "tombstone")  # tb文件已经pull到服务器上了
        if not os.path.exists(tombstone_path):   # 如果本地没有这个目录，说明还没有pull文件，先把tombstone目录pull下来
            self.tv.send_adb_command("adb -s {} pull /data/tombstones {}/tombstone".format(self.device_id,self.case_folder))
        print("check tombstone file path:{}".format(tombstone_path))
        pid_pattern = "pid: \d+"
        if not os.path.exists(tombstone_path):
            print("tombstone path not exist")
            return
        for tb_file in os.listdir(tombstone_path):
            temp_file = os.path.join(tombstone_path, tb_file)
            print(temp_file)
            decode_logcat(temp_file)  # 解码tombstone file
            linecache.clearcache()
            temp_tbfile = linecache.getlines(temp_file)
            singnal35 = False
            for line in temp_tbfile:
                if "signal 35 (<debuggerd signal>)" in line:
                    print("ignore signal 35 (<debuggerd signal>) issue")
                    singnal35 = True
                    break
            if singnal35 == True:
                continue
            linecache.clearcache()
            temp_tbfile = linecache.getlines(temp_file)
            for line in temp_tbfile:
                if "pid" in line and 'tid' in line and 'name' in line and "uiautomator" not in line and "busybox" not in line and "tvqs" not in line:
                    tb_pid = re.findall(pid_pattern, line)[0]
                    if tb_pid in self.issues_pid_dict["tombstone"]:
                        print("tombstone pid : {} 已上报，过滤本次异常".format(tb_pid))
                        break
                    else:
                        self.issues_pid_dict["tombstone"].append(tb_pid)
                        self.ifIssue_dict["tombstone"] += 1
                        print("TOMBSTONE happened {}".format(tb_pid))
                        if save_bug_info:
                            logstack_num = self.ifIssue_dict["tombstone"]
                            targetPkg = line.split(" >>> ")[1].split(' <<<')[0]
                            logstack = temp_tbfile[:50]  # 保存50行文件到logstack{}.tombstone中
                            logstack_file = os.path.join(self.case_folder, "logstack{}.tombstone".format(logstack_num))
                            with open(logstack_file, "w") as p:
                                p.write("".join(logstack))
                                targetPkg = targetPkg.strip()
                                targetPkg = targetPkg.split(":")[0]
                                print("targetPkg : {}".format(targetPkg))
                                self.write_issuefile(logstack_num, targetPkg, suffix="tombstone")
                        break  # 一个tombstone文件写一个issue就行
            linecache.clearcache()
        # print("tb issue pid dict:",self.issues_pid_dict)

    def catch_hdmi(self,save_bug_info=False):
        """
        捕获信号源的error
        :return:
        """
        print("\n============= catch hdmi ===========")
        targetPkg = "com.android.server.hdmi"
        hdmi_error_code_1 = "NullPointerException"
        hdmi_error_code_2 = "rx22: esm init err,reboot..."
        hdmi_error_code_3 = "/vendor/bin/hw/android.hardware.tv.cec@1.0-service <<<"
        linecache.clearcache()
        log = linecache.getlines(self.adb_logcat)  #
        for index, line in enumerate(log):
            get_hdmi_error = False       # 本行logcat有无异常
            error_code = ""
            if hdmi_error_code_1 in line and targetPkg in line:
                get_hdmi_error = True
                error_code= "{} & {}".format(targetPkg,hdmi_error_code_1)

            if hdmi_error_code_2 in line and "maverick" not in self.case_folder:   # 963s maverick平台不解析这个字段的issue
                get_hdmi_error = True
                error_code = hdmi_error_code_2

            if hdmi_error_code_3 in line:
                get_hdmi_error = True
                error_code = hdmi_error_code_3

            if get_hdmi_error == True:
                self.ifIssue_dict["hdmi"] += 1
                print("===HDMI Error happened===")
                print("error code: {}".format(error_code))
                if save_bug_info:
                    logstack_num = self.ifIssue_dict["hdmi"]
                    logstack = log[index:index + 50]       # todo logstackfile
                    logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(logstack_num, "hdmi"))  # 保存50行到文件logstack{}.hdmi中
                    with open(logstack_file, "w") as p:
                        p.write("".join(logstack))
                    self.write_issuefile(logstack_num, targetPkg, "hdmi")   # todo 写入issuefile

    def catch_wifi(self,save_bug_info=False):
        """
        捕获wifi子系统重启异常
        :param save_bug_info:
        :return:
        """
        print("\n============= catch wifi reset ===========")
        # mtk_keyword = "Trigger reset in .+ line \d+ reason"

        hard_rst = "RST_FLAG_DO_WHOLE_RESET"   #  hard reset 2024.12.3@钮玥更新

        mtk_hard = "glResetTriggerCommon"     
        mtk_soft = "RST_FLAG_DO_L0P5_RESET"    # mtk_soft + mtk_hard = soft reset

        rtl_keyword = "sreset_reset"
        log = linecache.getlines(self.adb_logcat)
        for index,line in enumerate(log):
            package = False
            # if re.findall(mtk_keyword,line):
            #     self.ifIssue_dict["wifi"] += 1
            #     print("===MTK Reboot Wifi happened===")
            #     package = "mtk_wifi"

            if hard_rst in line:
                self.ifIssue_dict["wifi"] += 1
                print("===MTK Wifi Hard Reset happened===")
                package = "wifi_hard_reset"

            if mtk_hard in line and mtk_soft in line:   # soft reset
                self.ifIssue_dict["wifi"] += 1
                print("===MTK Wifi Soft Resst happened===")
                package = "wifi_soft_reset"

            if re.findall(rtl_keyword,line) and "sreset_reset done" not in line:   # 打印sreset_reset 和打印 sreset_reset done是同一次，只统计一次即可
                self.ifIssue_dict["wifi"] += 1
                print("===RTL Wifi Reset happened===")
                package = "rtl_wifi"

            if package and save_bug_info:
                wifi_index = self.ifIssue_dict["wifi"]
                logstack = log[index:index+50]       # todo logstackfile
                logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(wifi_index, "wifi"))  # 保存50行到文件logstack{}.wifi中
                with open(logstack_file, "w") as p:
                    p.write("".join(logstack))
                self.write_issuefile(wifi_index, package, "wifi")   # todo 写入issuefile

    def catch_deadsystem(self,save_bug_info = False):
        print("\n============= catch deadsystem ===========")
        keyword = "DeadSystemException: The system died; earlier logs will point to the root cause"
        log = linecache.getlines(self.adb_logcat)
        targetPkg = "DeadSystemException"
        for index, line in enumerate(log):
            if keyword in line:
                self.ifIssue_dict["deadsystem"] += 1
                print("===DeadSystemException happened===")
                print("error code: {}".format(keyword))
                if save_bug_info:
                    logstack_file = os.path.join(self.case_folder, "logstack.{}".format("dead_system"))
                    add_log(logstack_file,"DeadSystemException")
                    self.write_issuefile("",targetPkg, "dead_system")
                break    # 出现一次说明系统已经死机了，后续压测不再往下

    def catch_audio_flinger_error(self,save_bug_info=False):
        print("\n=======catch_video_flinger_error(检测audio起播无声)=======")
        keyword = "AudioFlinger could not create track, status:-38 output 0"
        targetPkg = "audio_flinger"
        log = linecache.getlines(self.adb_logcat)
        for index, line in enumerate(log):
            if keyword in line:
                self.ifIssue_dict["audio_flinger"] += 1
                print("===Audio Flinger happened===")
                print("error code: {}".format(keyword))
                if save_bug_info:
                    audio_flinger_index = self.ifIssue_dict["audio_flinger"]
                    logstack = log[index:index + 50]
                    logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(audio_flinger_index, targetPkg))
                    with open(logstack_file, "w") as p:
                        p.write("".join(logstack))
                    self.write_issuefile(audio_flinger_index, targetPkg, targetPkg)

    def catch_audio_pa_error(self,save_bug_info=False):
        """audio 播放视频无声/杂音"""
        print("\n=======catch_video_pa_error(检测audio播放视频无声/杂音)=======")
        targetPkg = "audio_pa"
        audio_pa_error_codes = ["acm8625_work_handler error","tas5805_work_handler error",
                                "fs21xx_work_handler error","pa error found"]
        log = linecache.getlines(self.adb_logcat)
        for index,line in enumerate(log):
            for error_code in audio_pa_error_codes:
                if error_code in line:
                    self.ifIssue_dict["audio_pa"] += 1
                    print("===Audio Pa happened===")
                    print("error code: {}".format(error_code))
                    if save_bug_info:
                        audio_pa_index = self.ifIssue_dict["audio_pa"]
                        logstack = log[index:index+50]
                        logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(audio_pa_index,targetPkg))
                        with open(logstack_file,"w") as p:
                            p.write("".join(logstack))
                        self.write_issuefile(audio_pa_index,targetPkg,targetPkg)

    def catch_video_loading_error(self,save_bug_info=False):
        """video下载失败"""
        """video 视频解码器失败"""
        print("\n=======catch_video_loading_error(检测video下载失败)=======")
        targetPkg = "video_loading"
        video_loading_error_code = ["http_get_line failed, err:-110",
                                    "ffurl_open failed: ret:-110", ]  # 无法下载ts流，播放网络环境差,video下载失败
        log = linecache.getlines(self.adb_logcat)
        for index,line in enumerate(log):
            for error_code in video_loading_error_code:
                if error_code in line:
                    self.ifIssue_dict["video_loading"] += 1
                    print("===Video Loading Error happened===")
                    print("error code: {}".format(error_code))
                    if save_bug_info:
                        video_loading_index = self.ifIssue_dict["video_loading"]
                        logstack = log[index:index+50]
                        logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(video_loading_index,targetPkg))
                        with open(logstack_file,'w') as p:
                            p.write("".join(logstack))
                        self.write_issuefile(video_loading_index,targetPkg,targetPkg)

    def catch_video_decoder_error(self,domestic = True,save_bug_info=False):
        """video 视频解码器失败
        解析逻辑：出现一次解码器失败，后面没有not supportcode与之配对，则判定为解码器异常
        """
        print("\n=======catch_video_decoder_error(检测video播放失败)=======")
        targetPkg = "video_decoder"
        keyword1 = "libvlc video decoder"
        keyword2 = "Failed to create mediacodec decoder component i_ret"
        keyword3 = "StartMediaCodec failed"   # 海外项目，章一帆提供
        not_supportcodes = ["-10001","-10002","-10003","-10004"]
        is_not_support_video = False     # 是否为不支持的视频，默认为false，也就是默认为支持播放的
        logcat_start_index = 0
        log = linecache.getlines(self.adb_logcat)

        if domestic:
            for index,line in enumerate(log):
                if "not_supportcode" in line:
                    print("not support index:",index)
                    not_supportcode = line.split("not_supportcode:")[1].strip()
                    if not_supportcode in not_supportcodes:   # 解码器失败，配对了
                        is_not_support_video = True    # 是不支持的视频

                if keyword1 in line and keyword2 in line:
                    print("find index:",index)
                    if is_not_support_video == False and logcat_start_index != 0:     # 上一次的解码器失败没有配对
                        self.ifIssue_dict["video_decoder"] += 1
                        print("===Video Decoder Error happened===")
                        print("error code: {}".format(keyword2))
                        if save_bug_info:
                            video_decoder_index = self.ifIssue_dict["video_decoder"]
                            logstack = log[logcat_start_index-1:logcat_start_index+50]
                            logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(video_decoder_index,targetPkg))
                            with open(logstack_file,"w") as p:
                                p.write("".join(logstack))
                            self.write_issuefile(video_decoder_index,targetPkg,targetPkg)
                    # 如果上一次的解码器配对了，则上一次的就不是异常，不需要记录，但是依然要更新这一次的记录
                    is_not_support_video = False
                    logcat_start_index = index    # 更新这一次解码器失败的logcat index
                    print("renew index:",index)

            # todo 解析到最后了还有一次
            if is_not_support_video == False and logcat_start_index != 0:
                self.ifIssue_dict["video_decoder"] += 1
                print("===Video Decoder Error happened===")
                print("error code: {}".format(keyword2))
                if save_bug_info:
                    video_decoder_index = self.ifIssue_dict["video_decoder"]
                    logstack = log[logcat_start_index - 1:logcat_start_index + 50]
                    logstack_file = os.path.join(self.case_folder,
                                                 "logstack{}.{}".format(video_decoder_index, targetPkg))
                    with open(logstack_file, "w") as p:
                        p.write("".join(logstack))
                    self.write_issuefile(video_decoder_index, targetPkg, targetPkg)

        # 海外项目
        else:
            for index, line in enumerate(log):
                if keyword1 in line and keyword3 in line:    # 海外项目
                    self.ifIssue_dict["video_decoder"] += 1
                    print("===Video Decoder Error happened===")
                    print("error code: {}".format(keyword3))

                    if save_bug_info:
                        video_decoder_index = self.ifIssue_dict["video_decoder"]
                        logstack = log[index:index+50]
                        logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(video_decoder_index,targetPkg))
                        with open(logstack_file,"w") as p:
                            p.write("".join(logstack))
                        self.write_issuefile(video_decoder_index,targetPkg,targetPkg)

    def judge_playing(self,logcat_line1,playing):
        """
        判断是否在播放状态（剔除暂停、缓冲部分）
        :param logcat_line: 传入当前分析的logcat的一行日志
        :param playing: 传入当前是否在播放状态
        :return: playing 返回该行是否在播放状态
        """
        pause_start1 = "MiPlayer: pause_l: ("  # 暂停开始
        pause_start2 = "MiPlayer: pause_l: )"
        pause_start3 = "MediaPlayerService:"   # + "pause"

        pause_start5 = "MiPlayer: vlc_event_callback is posted type= libvlc_MediaPlayerPaused"
        pause_start6 = "MiPlayer: event(Paused) is posted"

        pause_start7 = "TvUniPlayer/SdkMediaPlayer"      # + native_pause
        pause_start8 = "TVUNIPLAYER: gala_player_SdkMediaPlayer"   # + native_pause
        pause_start9 = "UNIPLAYERSDK: MediaPlayerImpl"   # + "pause"

        # 2024.7.31 @zhaopingping
        pause_start10 = "TVME-LBaseVideoUICtr: pause"
        pause_start11 = "TVME-LControlEventReceiver: onReceive, action = com.xiaomi.mitv.player.control, operate:pause"
        pause_start12 = "TVME-LControlEventReceiver: PLAY_CONTROL_PAUSE: voice button down"
        pause_start13 = "BaseVideoUIController: pause"          # 小爱同学调起的暂停
        pause_start14 = "BaseVoiceControlReceiver: onReceive operate: pause, cause: voicecontrol"
        pause_start15 = "BaseVoiceControlReceiver: voice control operate set to PAUSE_BY_CONTROL"
        pause_start16 = "BaseVoiceControlReceiver: voice control pause"

        pause_end1 = "MiPlayer: start: ("    # 2024.11.14 @赵平平 只有以下关键字出现才算播放状态
        pause_end2 = "MiPlayer: processing kWhatStart..."
        pause_end3 = "MiPlayer: start_l: ("

        buffer_start = "MiPlayer::notifyListener msg 200 ext1 704 ext2 98 obj  0x0"  # 缓冲开始
        buffer_end = "MiPlayer: notify buffering end"  # 缓冲结束 # 2024.11.14 @赵平平 缓冲结束不代表播放开始结束那就当他不在播放状态
        listener_701 = "notifyListener msg 200 ext1 701 ext2 0 obj  0x0"
        listener_702 = "notifyListener msg 200 ext1 702 ext2 100 obj  0x0"    # 2024.11.14 @赵平平 缓冲结束不代表播放开始结束
        if pause_start1 in logcat_line1 or pause_start2 in logcat_line1:   # 暂停
            add_log(self.video_log,"playing False: {}".format(logcat_line1))
            playing = False

        elif pause_start3 in logcat_line1 and "pause" in logcat_line1:  # 暂停
            add_log(self.video_log, "playing False: {}".format(logcat_line1))
            playing = False

        elif pause_start5 in logcat_line1 or pause_start6 in logcat_line1:
            add_log(self.video_log, "playing False: {}".format(logcat_line1))
            playing = False

        elif pause_start7 in logcat_line1 and "native_pause" in logcat_line1:
            add_log(self.video_log, "playing False: {}".format(logcat_line1))
            playing = False

        elif pause_start8 in logcat_line1 and "native_pause" in logcat_line1:
            add_log(self.video_log, "playing False: {}".format(logcat_line1))
            playing = False

        elif pause_start9 in logcat_line1 and "pause" in logcat_line1:  # 暂停
            add_log(self.video_log, "playing False: {}".format(logcat_line1))
            playing = False

        elif (pause_start10 in logcat_line1 or pause_start11 in logcat_line1 or pause_start12 in logcat_line1
              or pause_start13 in logcat_line1 or pause_start14 in logcat_line1 or pause_start15 in logcat_line1 or pause_start16 in logcat_line1 ):
            add_log(self.video_log, "playing False: {}".format(logcat_line1))
            playing = False

        elif buffer_start in logcat_line1:   # 缓冲
            add_log(self.video_log, "buffer start , playing False: {}".format(logcat_line1))
            playing = False

        elif buffer_end in logcat_line1:   # 缓冲
            add_log(self.video_log, "buffer end , playing False: {}".format(logcat_line1))
            playing = False

        elif pause_end1 in logcat_line1 or pause_end2 in logcat_line1 or pause_end3 in logcat_line1 :   # 起播
            add_log(self.video_log, "playing True: {}".format(logcat_line1))
            playing = True

        elif listener_701 in logcat_line1:
            add_log(self.video_log, "701 playing False: {}".format(logcat_line1))
            playing = False

        elif listener_702 in logcat_line1:
            add_log(self.video_log, "702 playing False: {}".format(logcat_line1))
            playing = False

        return playing

    def catch_video_error(self,save_bug_info = False):
        """
        捕获播放错误（视频卡顿）
        判断video output和video display两种异常，都需要先判断当前是否在播放状态
        :return:
        """
        print("\n=======catch_video_output&display_error(检测video播放卡顿、播放卡住/失败)=======")

        # todo 播放卡顿异常，音视频不同步    # 连续10次
        output_pattern = "libvlc video output: picture is too late to be displayed(orig_pts:\d+)"
        output_orig_dict = {}
        targetPkg_output = "video_output"

        # todo 播放卡住/失败，画面不流畅   # 连续3次
        display_pattern = "libvlc vout display: picture .*(orig_pts:\d+)"
        display_orig_dict = {}
        targetPkg_display = "video_display"

        linecache.clearcache()
        log = linecache.getlines(self.adb_logcat)  #
        start_index = 0

        playing = True  # 是否在播放状态（非暂停or缓冲）
        for index, line in enumerate(log[:-1]):
            playing =self.judge_playing(line,playing)
            if playing == False:    # 如果当前在暂停，则跳过分析这一行
                continue
            output_pts = re.findall(output_pattern, line)     # 播放卡顿
            if output_pts:
                add_log(self.video_log,line)
                line_split = line.split()
                time_stand = line_split[0] + " " + line_split[1]
                pid = line_split[3]
                if pid not in output_orig_dict.keys():
                    output_orig_dict[pid] = {"orig_pts":output_pts,"time_stand":[time_stand]}
                elif output_pts == output_orig_dict[pid]["orig_pts"]:
                    output_orig_dict[pid]["time_stand"].append(time_stand)
                else:   # 同一个线程，同一个orig_pts值
                    # pts值变了，统计是否有异常
                    if len(output_orig_dict[pid]["time_stand"]) >= 10:
                        self.ifIssue_dict["video_output"] += 1
                        print("===Video Output Error happened===")
                        print("error code: {}".format(output_pattern))
                        if save_bug_info:
                            output_index = self.ifIssue_dict["video_output"]
                            logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(output_index, targetPkg_output))
                            add_log(logstack_file, "display error {} {}".format(output_orig_dict[pid]["orig_pts"], output_orig_dict[pid]["time_stand"]),output=True)  # issuefile
                            self.write_issuefile(output_index, targetPkg_output, targetPkg_output)
                    # todo 更新该线程的pts值
                    output_orig_dict[pid] = {"orig_pts":output_pts,"time_stand":[time_stand]}

            display_pts = re.findall(display_pattern,line)   # 播放卡住/失败
            if display_pts:
                add_log(self.video_log,line)
                if self.product_name in ["mulan", "maverick"] and display_pts[0] == 'orig_pts:1':
                    # print("ignore {} orig_pts:1,continue", self.product_name)
                    continue  # 过滤mulan和maverick的pts:1不判断
                line_split = line.split()
                time_stand = line_split[0] + " " + line_split[1]
                pid = line_split[3]
                if pid not in display_orig_dict.keys():
                    display_orig_dict[pid] = {"orig_pts":display_pts,"time_stand":[time_stand]}
                    start_index = index    # 新的orig_pts开始的index
                elif display_orig_dict[pid]["orig_pts"] == display_pts:
                    display_orig_dict[pid]["time_stand"].append(time_stand)
                else:
                    if len(display_orig_dict[pid]["time_stand"]) >= 3:   # 如果是mtk，则判定mtk的display条件
                        if self.TV_Product_Model == "MTK":
                            print("judge mtk display error start end index: {} to {}".format(start_index,index))
                            judge_res = self.judge_mtk_display_flip(log[start_index:index])   # 当前的index就是前一个orig_pts的日志的最后的index
                            if judge_res:
                                self.ifIssue_dict["video_display"] += 1
                                print("===Video Display Error(MTK) happened===")
                                print("error code: {}".format(display_pattern))
                                if save_bug_info:
                                    display_index = self.ifIssue_dict["video_display"]
                                    logstack_file = os.path.join(self.case_folder, "logstack{}.{}".format(display_index,
                                                                                                          targetPkg_display))
                                    add_log(logstack_file,
                                            "display error {} {}".format(display_orig_dict[pid]["orig_pts"],
                                                                         display_orig_dict[pid]["time_stand"]),
                                            output=True)  # issuefile
                                    add_log(logstack_file,"mtk display code {}".format(judge_res),output=True)
                                    self.write_issuefile(display_index, targetPkg_display, targetPkg_display)
                        else:
                            # 如果不是mtk，则直接判定卡主异常
                            self.ifIssue_dict["video_display"] += 1
                            print("===Video Display Error happened===")
                            print("error code: {}".format(display_pattern))
                            if save_bug_info:
                                display_index = self.ifIssue_dict["video_display"]
                                logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(display_index, targetPkg_display))
                                add_log(logstack_file, "display error {} {}".format(display_orig_dict[pid]["orig_pts"], display_orig_dict[pid]["time_stand"]),output=True)  # issuefile
                                self.write_issuefile(display_index, targetPkg_display, targetPkg_display)
                    # todo 更新该线程的pts值
                    display_orig_dict[pid] = {"orig_pts":display_pts,"time_stand":[time_stand]}
                    start_index = index  # 新的orig_pts开始的index

        # 解析一遍logcat之后，最后还要把dict中的统计一遍
        for pid in output_orig_dict.keys():
            if len(display_orig_dict[pid]["time_stand"]) >= 10:
                self.ifIssue_dict["video_output"] += 1
                print("===Video Output Error happened===")
                print("error code: {}".format(output_pattern))
                if save_bug_info:
                    output_index = self.ifIssue_dict["video_output"]
                    logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(output_index, targetPkg_output))
                    add_log(logstack_file, "display error {} {}".format(output_orig_dict[pid]["orig_pts"],
                                                                        output_orig_dict[pid]["time_stand"]),output=True)  # issuefile
                    self.write_issuefile(output_index, targetPkg_output, targetPkg_output)

        for pid in display_orig_dict.keys():
            if len(display_orig_dict[pid]["time_stand"]) >= 3:
                if self.TV_Product_Model == "MTK":
                    print("jedge mtk display error start end index: {} to end".format(start_index))
                    judge_res = self.judge_mtk_display_flip(log[start_index:])  # 当前的index就是前一个orig_pts的日志的最后的index
                    if judge_res:
                        self.ifIssue_dict["video_display"] += 1
                        print("===Video Display Error(MTK) happened===")
                        print("error code: {}".format(display_pattern))
                        if save_bug_info:
                            display_index = self.ifIssue_dict["video_display"]
                            logstack_file = os.path.join(self.case_folder, "logstack{}.{}".format(display_index,
                                                                                                  targetPkg_display))
                            add_log(logstack_file,
                                    "display error {} {}".format(display_orig_dict[pid]["orig_pts"],
                                                                 display_orig_dict[pid]["time_stand"]),output=True)  # issuefile
                            add_log(logstack_file, "mtk display code {}".format(judge_res), output=True)
                            self.write_issuefile(display_index, targetPkg_display, targetPkg_display)
                else:
                    self.ifIssue_dict["video_display"] += 1
                    print("===Video Display Error happened===")
                    print("error code: {}".format(display_pattern))
                    if save_bug_info:
                        display_index = self.ifIssue_dict["video_display"]
                        logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(display_index, targetPkg_display))
                        add_log(logstack_file, "display error {} {}".format(display_orig_dict[pid]["orig_pts"],
                                                                            display_orig_dict[pid]["time_stand"]),output=True)  # issuefile
                        self.write_issuefile(display_index, targetPkg_display, targetPkg_display)

    def judge_mtk_display_flip(self,mtk_log):
        # 如果已经满足条件一了，mtk平台再判断条件二 播放卡住/失败
        mtk_display_flip = "VideoSetWrapper: MI_DISP_Flip \[0\]\[max 0x0\].*?pts = \d+,"
        mtk_display_dict= {"MI_DISP_Flip_pts": None, "time_stand": []}

        for index,line in enumerate(mtk_log):
            mtk_display_pts = re.findall(mtk_display_flip, line)  # mtk平台播放卡住/失败
            if mtk_display_pts:
                mtk_pid = mtk_display_pts[0].split(" ")[-1]
                line_split = line.split()
                time_stand = line_split[0] + " " + line_split[1]
                if mtk_pid == mtk_display_dict["MI_DISP_Flip_pts"]:    # 连续出现
                    mtk_display_dict["time_stand"].append(time_stand)
                    if len(mtk_display_dict["time_stand"]) >= 3:
                        return mtk_display_dict
                else:
                    mtk_display_dict["MI_DISP_Flip_pts"] = mtk_pid     # 新出现的一个pts
                    mtk_display_dict["time_stand"] = [time_stand]
        if len(mtk_display_dict["time_stand"]) >= 3:
            return mtk_display_dict
        else:
            return False

    def write_issuefile(self,logstack_num,targetPkg,suffix,issue_type = None):
        """
        anr、fc、nativecrash写入issue{}.xx文件
        :param logstack_num:
        :param targetPkg:
        :param suffix: issuefile的尾缀，fc、anr、tombstone、nativecrash的尾缀等于issue_type
        :param issue_type: 异常描述，异常类型
        :return:
        """
        s_time = time.time()
        self.product_name = self.prop.getprop("ro.product.name")
        self.affectsVersion = self.prop.get_version()
        self.chipset = self.prop.getprop("ro.boot.mi.panel_size")
        self.product_model = self.prop.get_tv_model()
        self.caselog = os.path.join(self.case_folder, "case.log")
        self.testcaseName = self.get_testcaseName()
        if suffix == "hdmi":
            issue_type = "信号源错误"
        elif suffix == "video_output":
            issue_type = "video播放卡顿，音频不同步、丢帧"
        elif suffix == "video_display":
            issue_type = "video播放卡住/失败，视频画面不流畅"
        elif suffix == "video_loading":
            issue_type = "video下载失败，无法下载ts流"
        elif suffix == "video_decoder":
            issue_type = 'video播放失败，创建解码器失败'
        elif suffix == "video_screen":
            issue_type = "video播放黑屏"
        elif suffix == "start_video":
            issue_type == "video启播失败"
        elif suffix == "audio_flinger":
            issue_type = "audio起播无声"
        elif suffix == "audio_pa":
            issue_type = "audio播放视频无声/杂音"
        elif suffix == "wifi":
            issue_type = "wifi子系统重启"
        elif suffix == "dead_system":
            issue_type = "DeadSystemException"
        elif suffix == "p2p":
            issue_type = "p2p连接失败"
        elif suffix == "rtsp_mtk" or suffix == "rtsp_aml":
            issue_type = "p2p连接成功，RTSP协商失败"
        elif issue_type == None:    # 如果没有传入issue_type，则走suffix，如果传入了issue_type，则按传入的来
            issue_type = suffix
        issuefile = os.path.join(self.case_folder, "issue{}.{}".format(logstack_num, suffix))
        add_log(issuefile, "Project:{}".format(self.product_name))
        add_log(issuefile, "Target:{}".format(self.testcaseName))
        add_log(issuefile, "Summary:执行自动化测试时, {}发生{},testcase={}".format(targetPkg, issue_type, self.testcaseName))
        add_log(issuefile, "Component:{}".format(targetPkg))
        add_log(issuefile, "affectVersion:{}".format(self.affectsVersion))
        add_log(issuefile,
                "Description:执行自动化测试{}发生{}. 具体自动化执行过程log请看服务器上的case.log".format(targetPkg,issue_type))
        add_log(issuefile, "Chipset:{}".format(self.chipset))

        # todo check apk exist then grep app version
        app_exist = self.tv.send_adb_command("adb -s {} shell 'pm list package {}'".format(self.device_id,targetPkg))
        if app_exist:
            app_version = self.tv.send_adb_command("adb -s {} shell \"dumpsys package {} |grep versionName |head -1| sed -e 's/^[ ]*//g'\"".format(self.device_id,targetPkg))
            if "versionName" in app_version:
                app_version = app_version.split("=")[1]
            add_log(issuefile, "packageVersion:{}".format(app_version))
        else:
            add_log(issuefile, "packageVersion:None")
        print("\nwrite issue file use time:",time.time()-s_time)

    def get_testcaseName(self):
        """
        根据case.log读取testcase的名字，在issuefile里面写入testcaseName
        :return:
        """
        testcaseName = ""
        keyword = "INSTRUMENTATION_STATUS: class= "
        try:
            with open(self.caselog,"r") as caselog:
                for line in caselog:
                    if keyword in line:
                        testcaseName = line.split(keyword)[1]
                        testcaseName = testcaseName.strip()
                        return testcaseName
        except Exception as e:
            return testcaseName

    def save_bugreport(self):
        """
        保存bug report文件,一个case里面save一次就可以了
        :return:
        """
        if self.ifBugreport == False:
            print("save bugreport")
            now_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            bugreport_name = "bugreport_{}".format(now_time)
            self.tv.send_adb_command("adb -s {} bugreport {}/{}".format(self.device_id,self.case_folder,bugreport_name),timeout=10*60)
            dvb_server_pid = self.tv.send_adb_command("adb -s {} shell pidof dvb_server".format(self.device_id))
            print("save dvb server debuggerd ,pid dvb server:{}".format(dvb_server_pid))
            self.tv.send_adb_command("adb -s {} shell debuggerd -b {} > {}/dvb_server_debuggerd_1.txt".format(self.device_id,dvb_server_pid,self.case_folder),timeout=10*60)
            time.sleep(5)
            self.tv.send_adb_command("adb -s {} shell debuggerd -b {} > {}/dvb_server_debuggerd_2.txt".format(self.device_id,dvb_server_pid,self.case_folder),timeout=10*60)
            self.ifBugreport = True

    def pullLogs(self):
        """
        pull 电视上的data/log、/data/system/dropbox、/data/tombstones、/data/anr
        一个case只pull一次就可以了（讲道理）
        :return:
        """
        if self.isPullLogs == False:
            print("Pull Logs... ")
            self.tv.send_adb_command("adb -s {} pull /data/log {}/log".format(self.device_id,self.case_folder))   # 国内电视
            self.tv.send_adb_command("adb -s {} pull /data/misc/logd {}/log".format(self.device_id,self.case_folder))  # 海外电视
            self.tv.send_adb_command("adb -s {} pull /data/system/dropbox {}".format(self.device_id,self.case_folder))
            if not os.path.exists(os.path.join(self.case_folder,"tombstone")):    # 可能前面tombstone解析的时候已经pull过了
                self.tv.send_adb_command("adb -s {} pull /data/tombstones {}/tombstone".format(self.device_id,self.case_folder))
            self.tv.send_adb_command("adb -s {} pull /data/anr {}".format(self.device_id,self.case_folder))    # 同名目录不需要指定
            self.tv.send_adb_command("adb -s {} pull /data/miuilog {}".format(self.device_id,self.case_folder))    # 同名目录不需要指定
            self.isPullLogs = True
            print("pull logs done.")

    def catch_miracast_p2p(self,save_bug_info=False):
        print("\n=======catch_miracast_p2p (p2p连接失败)=======")
        p2p_keywords = ["Group Formation timed out",
                        "P2P-GROUP-FORMATION-FAILURE"]
        targetPkg = "p2p"
        log = linecache.getlines(self.adb_logcat)
        for index, line in enumerate(log):
            for keyword in p2p_keywords:
                if keyword in line:
                    print("===miracast P2P error happened===")
                    print("error code:",keyword)
                    self.ifIssue_dict["p2p"] += 1
                    if save_bug_info:
                        p2p_index = self.ifIssue_dict["p2p"]
                        logstack = log[index:index + 50]
                        logstack_file = os.path.join(self.case_folder, "logstack{}.{}".format(p2p_index, targetPkg))
                        with open(logstack_file, "w") as p:
                            p.write("".join(logstack))
                        self.write_issuefile(p2p_index, targetPkg, suffix=targetPkg)

    def catch_miracast_RTSP(self,save_bug_info=False):
        print("\n=======catch_miracast_RTSP (p2p连接成功，RTSP协商失败)=======")
        RTSP_keywords_MTK = ["buildTcpConnection tcp connect failed",
                         "TCP connection Failed!",
                         "Fail to start RTSP",
                         "Error WfdRtspClient::connect PARAMETER failed",
                         "Error WfdRtspClient get media info failed",
                         "Error WfdRtspClient SetMediaInfo(media) failed",
                         "Error WfdRtspClient SetMediaInfo() failed",
                         "Error WfdRtspClient::connect SETUP failed",
                         "Error WfdRtspClient::play  failed",
                         "Error WfdRtspClient::connect PLAY failed",
                         "Error ::waitForPlayerPlayDone failed",
                         "Error WfdRtspPlayer::play PushPlayer play failed",   # MTK
                         ]
        RTSP_keywords_Amlogic = ["NU-WifiDisplaySource: An error occurred in session 1 (-104, 'Recv failed./Connection reset by peer')"]
        log = linecache.getlines(self.adb_logcat)
        for index,line in enumerate(log):
            if self.TV_Product_Model == "MTK":
                targetPkg = "rtsp_mtk"
                for keyword in RTSP_keywords_MTK:
                    if keyword in line:
                        print("===miracast RTSP error(MTK) happened===")
                        print("error code:",keyword)
                        self.ifIssue_dict["rtsp_mtk"] += 1
                        if save_bug_info:
                            rtsp_mtk_index = self.ifIssue_dict["rtsp_mtk"]
                            logstack = log[index:index + 50]
                            logstack_file = os.path.join(self.case_folder,
                                                         "logstack{}.{}".format(rtsp_mtk_index, targetPkg))
                            with open(logstack_file, "w") as p:
                                p.write("".join(logstack))
                            self.write_issuefile(rtsp_mtk_index, targetPkg, suffix=targetPkg)
                            # break    # 一个case上报一个就够了
            else:    # aml平台
                targetPkg = "rtsp_aml"
                for keyword in RTSP_keywords_Amlogic:
                    if keyword in line:
                        print("===miracast RTSP error(Amlogic) happened===")
                        print("error code:", keyword)
                        self.ifIssue_dict["rtsp_aml"] += 1
                        if save_bug_info:
                            rtsp_aml_index = self.ifIssue_dict["rtsp_aml"]
                            logstack = log[index:index + 50]
                            logstack_file = os.path.join(self.case_folder,
                                                         "logstack{}.{}".format(rtsp_aml_index, targetPkg))
                            with open(logstack_file, "w") as p:
                                p.write("".join(logstack))
                            self.write_issuefile(rtsp_aml_index, targetPkg, suffix=targetPkg)

    def pullBTlog(self):
        """
        finch项目stability测试需要pull蓝牙log
        """
        print("Pull BlueTooth logs...")
        self.tv.send_adb_command("adb -s {} shell dumpsys --proto bluetooth_manager > {}/bluetooth_manager.txt".format(self.device_id, self.case_folder))
        self.tv.send_adb_command("adb -s {} pull /data/misc/bluedroid/ > {}".format(self.device_id, self.case_folder))
        self.tv.send_adb_command("adb -s {} pull /data/misc/bluetooth/ > {}".format(self.device_id, self.case_folder))

    # todo speedui检测
    def catch_speedui(self,save_bug_info):
        """如果未检测到，则需要上报jira"""
        print("\n=======catch speedui=======")
        keyword = "TVQSService-speedui: handleSchedThreads begin"
        is_speedui = False
        log = linecache.getlines(self.adb_logcat)
        for index, line in enumerate(log):
            if keyword in line:
                print("get speed ui keyword:{}".format(keyword))
                is_speedui = True
                break
        if is_speedui == False:
            print("===Speed UI does not work===")   # speed ui 没有生效
            targetPkg = "speedui"
            self.ifIssue_dict["speedui"] += 1
            if save_bug_info:
                speedui_index = self.ifIssue_dict["speedui"]
                logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(speedui_index, targetPkg))
                add_log(logstack_file,"未检测到{}".format(keyword))
                self.write_issuefile(speedui_index,targetPkg=targetPkg,suffix=targetPkg,issue_type="未检测到TVQSService-speedui")

    def catch_focusmode(self,save_bug_info):
        """专注模式检测，针对进程的时间戳进行计算"""
        print("\n=======catch focus mode=======")
        create_keyword = "Create RemoveResumeBarrierRunnable for token:"
        remove_keyword = "removeSyncBarrier for token:"
        log = linecache.getlines(self.adb_logcat)
        activity_dict = {}
        targetPkg = "focusmode"
        is_focusmode = False
        for index, line in enumerate(log):
            if create_keyword in line:
                is_focusmode = True      # 专注模式生效
                line_split = line.split()
                time_stand = line_split[0] + " " + line_split[1]
                activity_pid = line_split[-1]
                activity_dict[activity_pid] = str(time_stand)
            elif remove_keyword in line:
                is_focusmode = True      # 专注模式生效
                line_split = line.split()
                time_stand = str(line_split[0] + " " + line_split[1])
                activity_pid = line_split[-1]
                if activity_pid in activity_dict.keys():
                    # todo 计算差值
                    s1_t1 = datetime.datetime.strptime(time_stand, '%m-%d %H:%M:%S.%f')
                    s1_t2 = datetime.datetime.strptime(activity_dict[activity_pid], '%m-%d %H:%M:%S.%f')
                    # temp_days = (s1_t1 - s1_t2).days     # 隔了一天，一般不会有超过1天的
                    temp_seconds = (s1_t1 - s1_t2).seconds   # 隔了秒
                    temp_microseconds = (s1_t1 - s1_t2).microseconds  # 微秒级别
                    # print("temp:{},s:{},micros:{}".format(activity_pid,temp_seconds,temp_microseconds),s1_t1,s1_t2)
                    del activity_dict[activity_pid]  # todo 删除这个已经配对的pid
                    diff_miliseconds = temp_seconds *1000 + temp_microseconds // 1000
                    # print("差距毫秒：{}".format(diff_miliseconds))
                    if diff_miliseconds > 500:   # 差值大于300ms，说明有异常
                        self.ifIssue_dict["focusmode"] += 1
                        print("ResumeBarrierRunnable for token:{}, removeSyncBarrier超时 {}毫秒".format(activity_pid,diff_miliseconds))
                        if save_bug_info:
                            focusmode_index = self.ifIssue_dict["focusmode"]
                            logstack = log[index-1:index-1 + 50]                # 日志成对出现的，上下行
                            logstack_file = os.path.join(self.case_folder,"logstack{}.{}".format(focusmode_index, targetPkg))
                            with open(logstack_file, "w") as p:
                                p.write("".join(logstack))
                            self.write_issuefile(focusmode_index, targetPkg=targetPkg, suffix=targetPkg,issue_type="removeSyncBarrier超时{}毫秒".format(diff_miliseconds))
                            issuefile = os.path.join(self.case_folder, "issue{}.{}".format(focusmode_index, targetPkg))
                            if diff_miliseconds > 3000:
                                add_log(issuefile,"priority:Blocker")
                            elif diff_miliseconds > 1000:
                                add_log(issuefile,"priority:Critical")
                            else:
                                add_log(issuefile,"priority:Major")

        if is_focusmode == False:
            print("没有检测到相关关键字，专注模式未生效")
            self.ifIssue_dict["focusmode"] += 1
            if save_bug_info:
                focusmode_index = self.ifIssue_dict["focusmode"]
                logstack_file = os.path.join(self.case_folder, "logstack{}.{}".format(focusmode_index, targetPkg))
                add_log(logstack_file,"没有检测到相关关键字，专注模式未生效")
                self.write_issuefile(focusmode_index, targetPkg=targetPkg, suffix=targetPkg, issue_type="专注模式未生效")
                issuefile = os.path.join(self.case_folder, "issue{}.{}".format(focusmode_index, targetPkg))
                add_log(issuefile, "priority:Major")

        for pid in activity_dict.keys():
            print("Create ResumeBarrierRunnable for token:{}未配对".format(pid))
            self.ifIssue_dict["focusmode"] += 1
            if save_bug_info:
                focusmode_index = self.ifIssue_dict["focusmode"]
                logstack_file = os.path.join(self.case_folder, "logstack{}.{}".format(focusmode_index, targetPkg))
                add_log(logstack_file,"Create ResumeBarrierRunnable for token:{}未配对".format(pid))
                self.write_issuefile(focusmode_index, targetPkg=targetPkg, suffix=targetPkg, issue_type="Create ResumeBarrierRunnable for token:{}未配对".format(pid))
                issuefile = os.path.join(self.case_folder, "issue{}.{}".format(focusmode_index, targetPkg))
                add_log(issuefile, "priority:Critical")

class Prop(object):
    def __init__(self,deviceId,resultfolder):
        self.deviceId = deviceId
        self.resultfolder = resultfolder
        self.__prop = self.prop2dict()

    def prop2dict(self):
        print("getprop infos")
        propfile = os.path.join(self.resultfolder,"getprop.txt")
        s_time = time.time()
        if not os.path.exists(propfile):
            self.tv = TvAdb()
            props = self.tv.send_adb_command("adb -s {} shell 'getprop'".format(self.deviceId))
            add_log(propfile,props)

        # todo 根据个getprop.txt文件读取prop信息
        props = linecache.getlines(propfile)
        props_dict = {}
        for __line in props:
            __line = __line.strip()
            __line = __line.replace("[", "").replace("]", "").split(": ")
            if len(__line) == 2:
                props_dict[__line[0]] = __line[1]
            else:
                pass
        self.getprop_infos = True
        print("getprop using time:",time.time() - s_time)
        return props_dict

    def getprop(self, prop):
        try:
            return self.__prop[prop]
        except:
            print("e" * 10 + "  prop:%s not exists  " % prop + "e" * 10)
            return ""

    def get_tv_model(self):
        """
        读取tv的机型信息
        根据 @刘佳子 提供信息，获取机型字段按照以下优先级获取
        ro.boot.model_name > tv.model_name > ro.boot.platform_name > ro.product.model > ro.boot.assm_mn
        """
        if self.getprop("ro.boot.model_name"):
            return self.getprop("ro.boot.model_name")
        elif self.getprop("tv.model_name"):
            return self.getprop("tv.model_name")
        elif self.getprop("ro.boot.platform_name"):
            return self.getprop("ro.boot.platform_name")
        elif self.getprop("ro.product.model"):
            return self.getprop("ro.product.model")
        else:
            if self.getprop("ro.boot.assm_mn") and len(self.getprop("ro.boot.assm_mn")[:4]) >= 4:
                return self.getprop("ro.boot.assm_mn")[:4]

    def get_version(self):
        """
        读取tv的软件版本
        """
        build_software_version = self.getprop("ro.build.software.version")
        build_display_id = self.getprop("ro.build.display.id")
        build_version_incremental = self.getprop("ro.build.version.incremental")
        if len(build_version_incremental.split(".")) > 1:  # koki
            build_version = build_version_incremental
        elif len(build_software_version.split(".")) > 3:
            build_version = build_software_version
        else:
            build_version = build_display_id
        return build_version

    def get_tv_product_model(self):
        product_model = self.getprop("ro.product.model")
        if product_model and product_model.startswith("MiTV-"):
            if product_model[5] == "A":
                print("product model is Amlogic")
                return "Amlogic"
            elif product_model[5] == "M":
                print("product model is MTK")
                return "MTK"
        else:
            print("produce name:{},unknown TV Model Name".format(product_model))
            return "Unknown"

def decode_logcat(logcat_file):
    """
    log解码成utf8格式
    解码后的logcat file路径记为logcat_file
    :param logcat_path:
    :return:logcat_file
    """
    print("decode logcat to utf-8")
    logcat_size = os.stat(logcat_file).st_size
    print("logcat 文件大小：{} MB".format(logcat_size//(1000*1000)))
    if int(logcat_size//(1000*1000)) > 2000:
        print("日志文件过大，不执行decode操作")
        return logcat_file
    allline = []
    with open(logcat_file, "rb") as log:
        for index, line in enumerate(log):
            try:
                line = line.decode('utf-8', 'ignore')
                # print("index:{};line:{}".format(index,line))
                allline.append(line)
            except Exception as e:
                print("decode error:{}".format(line))
                print(traceback.print_exc())
        log.close()     # 读取初版logcat，先关闭logcat
    print("finish read logcat")
    print(type(allline))
    try:
        with open(logcat_file, "w") as p:
            for line in allline:
                p.write(line)
            p.close()     # todo 可能是文件没有正确的关闭导致压测异常被Killed
    except Exception as e:
        print("rewrite logcat error:{}".format(line))
        print(traceback.print_exc())
    print("finish decode logcat")
    return logcat_file

def add_log(logname,writeline,output = False):
    if output == True:
        # print(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'),logname,writeline)
        print(logname,writeline)
    log_file = open(logname,"a+",encoding='utf-8')
    log_file.write(writeline)
    log_file.write("\n")
    log_file.close()


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Parameters")
    parser.add_argument("--device_id", type=str, help="测试设备ip地址")
    parser.add_argument("--resultfolder", type=str, help="压测结果存放目录")
    parser.add_argument("--casefolder", type=str, help="本次case的目录")
    args = parser.parse_args()
    # todo 设置指定电视设备
    os.environ["ANDROID_SERIAL"]=args.device_id
    print("set environ ANDROID SERIAL = {}".format(args.device_id))

    parse = LogParser(device_id = args.device_id, resultFolder = args.resultfolder)
    # 如果传入了casefolder，casefolder和resultfolder可以是同一个
    temp_case = os.path.join(args.resultfolder,args.casefolder)
    # temp_case = "/home/<USER>/workspaces/projects/log_auto_parse/testBilibili"
    print(temp_case)
    parse.parse_logcat(casefolder = temp_case,save_bug_info=True,grep_anr=True,grep_fc=True,grep_tb=True,grep_hdmi=True,grep_wifi=True,grep_miplayer=True,grep_deadsystem=True,grep_miracast=True,grep_speedui=True,pull_log_now=False)
    # parse.parse_logcat(casefolder = "freeguy_test/fenxi",save_bug_info=True,grep_anr=True,grep_fc=True,grep_tb=True,grep_hdmi=True,grep_wifi=True,grep_miplayer=True,grep_deadsystem=True,pull_log_now=False)
    # folderlist = []
    # for filepath in os.listdir(args.resultfolder):    # 或者遍历resultfolder中的目录,按目录的时间顺序遍历
    #     temp_filepath = os.path.join(args.resultfolder,filepath)
    #     if os.path.isdir(temp_filepath):
    #         folderlist.append(temp_filepath)
    # folderlist.sort(key=lambda x: os.stat(x).st_ctime)
    # for temp_filepath in folderlist:
    #     print(temp_filepath)
    #     if os.path.isdir(temp_filepath):
    #         parse.parse_logcat(casefolder=temp_filepath,save_bug_info=True,grep_anr=True,grep_fc=True,grep_tb=True,grep_hdmi=True,grep_wifi=True,grep_miplayer=True)


# python3 log_parser.py --device_id 10.189.128.xx --resultfolder xxx --casefolder xxx