# -*- coding: utf-8 -*-

# 对比两张图像的相似性

import cv2
import numpy as np

def calculate_image_hash(image):
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    resized = cv2.resize(gray, (8, 8), interpolation=cv2.INTER_AREA)
    avg = np.mean(resized)
    hash_value = np.where(resized > avg, 1, 0)
    return hash_value.flatten()

def hamming_distance(hash1, hash2):
    return np.count_nonzero(hash1 != hash2)

def hamming_compare(image1_file,image2_file):
    # 读取两张图片
    image1 = cv2.imread(image1_file)
    image2 = cv2.imread(image2_file)

    hash1 = calculate_image_hash(image1)
    hash2 = calculate_image_hash(image2)

    distance = hamming_distance(hash1, hash2)
    print(f"汉明距离: {distance}")
    if distance == 0:
        print("两张图片一样")
        return True
    else:
        print(f"两张图片有差异，差异程度根据汉明距离评估为: {distance}")
        return False


if __name__ == '__main__':
    image1 = 'files/mitv_20250416-170111-1.jpg'
    image2 = 'files/mitv_20250416-170111-2.jpg'
    image3 = 'files/mitv_20250416-170111-2.jpg'
    res1 = hamming_compare(image1,image2)
    # todo 如果 图1 2不一致，就不用比较1和3了       # 启播的判断，用3张图判断
    if res1 == True:
        res2 = hamming_compare(image1,image3)
        print(res2)
    else:
        print("False")