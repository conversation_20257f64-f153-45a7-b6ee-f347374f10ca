# 自动解析logcat中的错误：fc、anr、native crash、tombstone、音视频异常、reboot、黑屏

运行环境

    python3

依赖库    

    tv_common
    
    cv2



### 自动解析logcat和tombstone相关的异常

log_parse.py文件可以单独执行
```python
# 直接执行
python3 log_parser.py --device_id 10.189.128.xx --resultfolder xxx --casefolder xxx 
#--device_id:设备id
#--resultfolder:压测结果目录
#--casefolder:case结果目录

# 调用方法
from log_parser import *
device_id = "10.189.129.xx"
resultfolder = "moderntimes_23.11.1.371_testResult_47_20231101_195703"
parse = LogParser(device_id = device_id, resultFolder = resultfolder)
casefolder = "moderntimes_23.11.1.371_testResult_47_20231101_195703/apps-switch_20231101_222425/"
logcatfilename = "logcat.log"
parse.parse_logcat(casefolder = casefolder,logcat_file = logcatfilename,save_bug_info=False,grep_anr=True,grep_fc=True,grep_tb=True,grep_hdmi=True,grep_wifi=True,grep_miplayer=True,grep_deadsystem = False,pull_log_now=False)


# casefolder :case结果目录
# logcat_file: logcat文件名，默认为logcat.log
# save_bug_info: 是否保存issuefile，默认为false
# grep_fc: 是否解析fc，默认为false
# grep_anr: 是否解析anr，默认为false
# grep_tb: 是否解析tombstone，默认为false
# grep_hdmi: 是否解析hdmi信号源异常，默认为false
# grep_wifi: 是否解析wifi信号源异常，默认为false
# grep_miplayer: 是否解析miplayer小米播放器相关异常，默认为false
# grep_deadsystem: 是否解析系统死机，默认为false
# pull_log_now: 如果出现异常，是否现在pull log，默认为false不pull log
```


### 自动解析电视异常，包含fc、anr、fc、anr、native crash、tombstone、音视频异常、reboot、黑屏，并保存buginfo、data/log等日志

analysis_error.py依赖于log_parse.py和Display_Detection.py文件
```python
# 直接执行
python3 analysis_error.py --device_id 10.189.128.xx --resultfolder xxx --casefolder xxx 
#--device_id:设备id
#--resultfolder:压测结果目录
#--casefolder:case结果目录

# 调用方法
from analysis_error import *
device_id = "10.189.129.xx"
resultfolder = "moderntimes_23.11.1.371_testResult_47_20231101_195703"
parse = AnalysisError(device_id = device_id,resultFolder = resultfolder)
casefolder = "moderntimes_23.11.1.371_testResult_47_20231101_195703/apps-switch_20231101_222425/"
parse.analysing(case_folder = casefolder,save_bug_info = True,miplayererror = True,rm_pictures = True,deadsystem=False,miracast = False,speedui=False)
# case_folder: 本次case的case folder
# save_bug_info: 是否保存issuefile、data/log等信息，默认为不存
# miplayererror: 是否解析小米播放器异常（音视频异常、黑屏），默认为不解析
# rm_pictures: 判断黑屏，如果没有异常，是否删除截图，默认为不删除
# deadsystem: 判断系统死机，默认为不检测，海外项目才需要检测这一项
```

