# -*- coding: utf-8 -*-

# todo 检测屏幕显示异常： 如果黑屏、绿屏、蓝屏等
import os
import cv2
import numpy as np
import time
from PIL import Image

def single_color(image):
    """
    延展：如果输入的只是图像的一个部分，是否也能检测出来，即屏幕花屏，但是要遍历整个图像，会比较费时间
    判断电视绿屏、蓝屏等（出现某个通道的数值变换）      # 不行，比如只有红色，其他蓝绿通道也是全0的，检测不了蓝屏绿屏了
    :param image:图片路径
    :return:true：屏幕异常；false：屏幕正常
    """
    img_arr = cv2.imread(image)
    b, g, r = cv2.split(img_arr)  # 分离各图像通道
    x, y = b.shape
    x3, y3 = x // 3, y // 3
    judge_single_color(b[0:x//5,y//5:])
    if judge_single_color(b) or judge_single_color(g) or judge_single_color(r):   # 大图任意通道
        if judge_single_color(b[x3:2*x3,y3:2*y3]) and judge_single_color(b[0:x//5,y//5:]) and judge_single_color(g[x3:2 * x3, y3:2 * y3]) and judge_single_color(g[0:x // 5, y // 5:]) and judge_single_color(r[x3:2 * x3, y3:2 * y3]) and judge_single_color(r[0:x // 5, y // 5:]):
            return True
        else:
            print("大图黑屏，小图非黑屏")
    return False
    # if judge_single_color(b):   # 3个通道，只要其中一个满足，即判定为黑屏
    #     print("judge b")
    #     if judge_single_color(b[x3:2*x3,y3:2*y3]) and judge_single_color(b[0:x//5,y//5:]):
    #         return True
    #     else:
    #         print("大图黑屏，小图非黑屏")
    # if judge_single_color(g):
    #     print("judge g")
    #     if judge_single_color(g[x3:2*x3,y3:2*y3]) and judge_single_color(g[0:x//5,y//5:]):
    #         return True
    #     else:
    #         print("大图黑屏，小图非黑屏")
    # if judge_single_color(r):
    #     print("judge r")
    #     if judge_single_color(r[x3:2*x3,y3:2*y3]) and judge_single_color(r[0:x//5,y//5:]):
    #         return True
    #     else:
    #         print("大图黑屏，小图非黑屏")
    # return False


def judge_single_color(single_arr,threshold=10,percent_th=0.999):
    """
    输入单通道数组，计算每个像素点和均值相差小于threshold的像素点的占比
    :param single_arr:图片单个通道的array
    :param threshold:像素点之间的差异阈值
    :param percent_th:相似的像素占比阈值
    :return:
    """
    single_arr = np.array(single_arr)
    single_arr = single_arr.flatten()    # 拉伸至一维
    arr_avg = np.average(single_arr)
    pixel_sum = len(single_arr)

    temp_arr = abs(single_arr - arr_avg)
    com = np.sum(temp_arr <= threshold)
    diff = np.sum(temp_arr > threshold)
    com_per = com / pixel_sum
    diff_per = diff / pixel_sum
    if com_per >= percent_th:
        print("单通道相似的像素点占比", com_per)
        print("单通道不同的像素点占比", diff_per)
        return True
    else:
        return False

def pixel_compare(image1,image2,threshold=50,similar_rate=0.9):
    """
    判断两张图像对应像素点的值是否相似，从而得出两张图片是否相似(也可以检测颜色上的不同)
    :param image1:
    :param image2:
    :return:Ture ：相似 ；false：不相似
    """
    img1_arr = cv2.imread(image1)
    b1, g1, r1 = cv2.split(img1_arr)  # 分离各图像通道

    img2_arr = cv2.imread(image2)
    b2, g2, r2 = cv2.split(img2_arr)  # 分离各图像通道

    equal_b = single_pixel_equal(b1,b2,threshold)
    equal_g = single_pixel_equal(g1,g2,threshold)
    equal_r = single_pixel_equal(r1,r2,threshold)

    equal_matrix = equal_r * equal_g * equal_b
    equal_sum = np.sum(equal_matrix)
    pixel_sum = np.size(equal_matrix)
    same_rate = equal_sum / pixel_sum
    print("比较两张图片相似度same_rate:",same_rate)
    if same_rate >= similar_rate:
        return True
    else:
        return False

def single_pixel_equal(img1_arr,img2_arr,threshold=50):
    """
    输入单通道的数据，计算对应像素点的至是否相似
    :param img1_arr:
    :param img2_arr:
    :param threshold:
    :return: 相似的对应像素点置位1的矩阵
    """
    # todo 先转换为整型，否则可能溢出影响计算结果，如果需要重置回图片，需转换dtype="uint8"
    img1_arr = np.array(img1_arr,dtype="int")
    img2_arr = np.array(img2_arr,dtype="int")
    temp_arr = abs(img1_arr - img2_arr)
    equal_pixel = np.where(temp_arr < threshold,1,0)   # 将大于threshold的值置位0，小于threshold的置位1
    return equal_pixel

def checkBlack(screencap):
    """
    检查一张图片是否为全黑
    :param screencap: 图片路径
    :return: True:黑屏
    """
    image = Image.open(screencap)
    gray_img = cv2.cvtColor(np.asarray(image), cv2.COLOR_BGR2GRAY)
    r, c = gray_img.shape[:2]
    piexs_sum = r * c
    dark_points = (gray_img < 20)
    target_array = gray_img[dark_points]
    dark_sum = target_array.size
    dark_prop = dark_sum / (piexs_sum)
    print("DARK DROP:{}".format(dark_prop))
    if dark_prop >= 0.85:
        print("black screencap")
        return True
    else:
        print("play successfully")
        return False

if __name__ == '__main__':
    # img_dir = "pictures/smartshare_2.png"
    # path_dir = "pictures"
    # for img_name in os.listdir(path_dir):
    #     print(img_name)
    #     img_dir = os.path.join(path_dir,img_name)
    #     s_time = time.time()
    #     single_color(img_dir)  # 单张图片检测显示异常（蓝屏、黑屏、绿屏等）
    #     e_time = time.time()
    #     print("use time:{}s".format(e_time-s_time))
    #     print("")

    t1 = time.time()
    img1_dir = "just_debug/screencaps/screencap_20240718-001440-2.jpg"
    img2_dir = "just_debug/screencaps/screencap_20240718-001440-2.jpg"
    pixel_compare(img1_dir, img2_dir)    # 比较两张图片是否相似（颜色不相似也可以比较出来）
    print("check result:",single_color(img1_dir))
    # checkBlack(img1_dir)
    # img_dir = "/home/<USER>/submit_codes/MiPlayerTest/freeguy_result/testAiQiYi/screencap_20230909-171249-2.jpg"
    # print(single_color(img_dir))
    # print(checkBlack(img_dir))
    t2 = time.time()
    print("t=", t2 - t1)  # 0.065s
    print("")
