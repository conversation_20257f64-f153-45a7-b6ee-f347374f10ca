# -*- coding:utf-8 -*-
import os
import shutil
import sys
import time,datetime
import argparse
from logger import create_logger,add_log
import datetime,linecache,traceback
from subprocess import PIPE,Popen,STDOUT
from basic_adb import Basic_ADB
from update_3_apps import UpdateApps
from importlib import import_module
from log_auto_parse.analysis_error import AnalysisError
WORK_FOLDER = os.getcwd()
print(WORK_FOLDER)
from shutil import copyfile
from PIL import Image
import threading
import unittest
from configparser import ConfigParser
from adb_guard import ADB_Guard
from mapping_caselist import *
from mitv_autotest_parse.UpdateIssue import LogParser
from mitv_autotest_parse.Reporter import Generate
from mitv_autotest_parse.sendEmails import SendEmail
from mitv_autotest_parse.guard_reporter import running as guard_report
from mitv_autotest_parse.make_parselist import *
from upload_info_ptf import UploadTestInfos
from tvadb.tv_adb import TvAdb
from feishu.feishu import FeiShu

class Mitv_Test():
    def __init__(self,device_id,testtype,casetype,LOOP_O,LOOP_I,address,build_url="unknown",build_user="unknown",upload_data=0,build_number=0,plan_hours=0,phone_id = "None",is_board=0):
        """
        Args:
            device_id:
            testtype:
            casetype:
            LOOP_O:
            LOOP_I:
            address:
            build_url:
            build_user:
            upload_data:
            build_number:
            plan_hours:
            phone_id:
            is_board: 是否使用主板压测，0：整机，1：主板，默认为0使用整机压测
        """
        self.device_id = device_id
        self.phone_id = phone_id
        self.basic_adb = Basic_ADB(self.device_id)
        self.tv = TvAdb()
        self.testtype = testtype    #
        self.LOOP_O = LOOP_O
        self.LOOP_I = LOOP_I
        self.address = address
        self.build_url = build_url
        self.build_user = build_user
        self.build_number = build_number
        self.upload_data = upload_data

        self.plan_hours = plan_hours     # 计划执行时长
        self.case_running_time = 0     # 当前已执行时case耗时（秒）
        self.running_test_flag = True
        self.board = "主板" if is_board == 1 else "整机"

        self.guard = ADB_Guard(device_id,build_url,build_user)
        self.casetype = casetype
        self.casemap = Get_Caselist(self.testtype)
        self.testtype_map = {
            1: "monkey",
            2: "stability",
            14: "miplayer",  # 小米播放器测试
            17: "HDMI-stability",  # 信号源稳定性测试
            18:"Display-stability",  # display 稳定性测试
            19:"connectivity",   # connectivity 稳定性测试
            20:"stability-overseas",  # 海外稳定性测试
            21:"LiveTV",   # LiveTV 海外
            22:"smartshare",  # 投屏专项测试
            23: "photonengine",  # 光子引擎测试
            24:"SpeedUI" ,   # speed UI 测试
        }
        self.feishu_user = self.build_user
        if self.build_user  in ["unknown","ROBOT-MITV","robot-mitv","jenkins"]:
            self.feishu_user ='heyingmei'  # 飞书预警
        self.feishu = FeiShu()

    def run_test(self):
        if self.tv.reconnect(device_id= self.device_id,timeout=5*60):    # 测试开始连接设备
            print("connect to device :{}".format(self.device_id))
        else:
            # todo upload 一次start data然后接着upload result data  & 飞书预警
            if self.upload_data == 1 or self.upload_data == 2:
                if self.upload_data == 1:
                    run_mode = "prod"
                else:
                    run_mode = "dev"
                print("check run mode:", run_mode)
                self.upload_abnormal_infos = UploadTestInfos(device_id = self.device_id,testtype= self.testtype,result_folder= None,Address= self.address,build_url=self.build_url,build_user= self.build_user,run_mode= run_mode)
                self.upload_abnormal_infos.uploadnow_start_info()
                self.upload_abnormal_infos.uploadnow_result_info()
            self.feishu.send_message_interactive(receiver=self.feishu_user,
                                                 subject="自动化测试异常通知",
                                                 message_items={"执行人": self.build_user,
                                                                "电视ip":self.device_id,
                                                                "任务地址": self.build_url,
                                                                "备注": "电视adb连接失败，请检查电视设备状态，测试结束"},
                                                 subject_background_color="yellow")


            raise ValueError("Before start test,fail to connect to devices {} in 5 minutes,please check tv status,test exit.".format(self.device_id))

        self.project_name = self.basic_adb.get_product_name()
        self.brand_tv = self.get_tv_manufacturer()      # 电视品牌
        print("brand_tv:", self.brand_tv)
        if self.testtype == 20:   # 海外稳定性
            # 判断是atv还是gtv
            self.basic_adb.adb_root()    # root 之后才能读到这个字段
            vendor_mitv = self.basic_adb.gtv_or_atv()
            if "jaws" in self.project_name:
                vendor_mitv = "GTV"
            print("check TV is {}".format(vendor_mitv))
            if vendor_mitv == "GTV":   # gtv只有一个caselist
                # if "jaws" in self.project_name:    # m25
                #     self.casetype = 7
                # else:                        # 其他的是电视和海外的盒子twilight
                self.casetype = 0    # jaws也用GTV的caselist
            else:    # ATV
                if "oneday" in self.project_name or "aquaman" in self.project_name:    # caselist=m19  盒子  oneday >> m19, aquaman >> m21
                    self.casetype = 8
                elif "soul" in self.project_name:    # 盒子 m24
                    self.casetype = 9
                elif "twilight" in self.project_name or "adastra" in self.project_name or "coco" in self.project_name:  # 盒子twilight，是GTV，执行GTV的caselist，但是读不到vendor.mitv.gtv字段，会被判为ATV
                    self.casetype = 0
                else:
                    # atv电视安装mitvcase
                    print("Android TV ,install mitvcase.apk")
                    self.tv.send_adb_command("adb -s {} install -r -t mitv_apks/app-debug.apk".format(self.device_id),output=True)
                    self.tv.send_adb_command("adb -s {} install -r -t mitv_apks/app-debug-androidTest.apk".format(self.device_id),output=True)
                    self.case_loopi = self.LOOP_I   # mitvcase的小loop直接在case内循环
                    self.LOOP_I = 1
                    install_request = self.tv.send_adb_command("adb -s {} shell pm list package | grep com.xiaomi.mitvcase".format(self.device_id))
                    if not install_request:
                        #todo 安装mitvcase失败，可能是有弹窗，发飞书通知给测试执行人
                        self.feishu.send_message_interactive(receiver=self.feishu_user,
                                        subject="自动化测试异常通知",
                                        message_items={"执行人":self.build_user,
                                                     "任务地址":self.build_url,
                                                     "备注":"海外MTBF测试，{}项目安装mitvcase apk失败，请检查是否有弹窗点击安装确认。本次压测中止，请重起压测任务。".format(self.project_name)},
                                        subject_background_color = "yellow")
                        raise ValueError(
                            "Fail to install mitvcase to devices {}, please check tv ,test exit.".format(self.device_id))
                    if "dangal" in self.project_name or "rango" in self.project_name or "dune" in self.project_name:
                        self.casetype = 1   # dangalP_
                    elif "tarzan" in self.project_name:
                        self.casetype = 2  # Tarzan_
                    elif "hermano_la" == self.project_name:
                        self.casetype = 4  # hermano_la
                    elif "hermano_eu" == self.project_name or "hermano" in self.project_name:
                        self.casetype = 3   # hermano_eu
                    elif "croods" in self.project_name or "machuca" in self.project_name or "lalaland" in self.project_name or "stepup" in self.project_name or "furiosa" in self.project_name:
                        self.casetype = 5   # machuca
                    elif "martian" in self.project_name:
                        self.casetype = 6   # martian

                    else:   # atv 无法匹配一个caselist，发个飞书通知给我
                        self.feishu.send_message_interactive(receiver="heyingmei",
                                        subject="自动化测试异常通知",
                                        message_items={"执行人":self.build_user,
                                                     "任务地址":self.build_url,
                                                     "备注":"海外mtbf测试，ATV {}项目无法匹配caselist".format(self.project_name)},
                                        subject_background_color = "yellow")
        test_categorie,test_chname,caselist,to_be_installed_apks = self.casemap.mapping_testtype(self.casetype)     # 获取caselist和install app list

        if test_categorie == "stability":
            print("set persist.mtbf.running true & reboot")
            self.basic_adb.adb_root()
            self.basic_adb.send_adb_command("shell setprop persist.mtbf.running true",output = True)
            time.sleep(3)
            print("check mtbf running before reboot:",self.basic_adb.send_adb_command("shell getprop persist.mtbf.running"))    # todo 结束后要记得设回false
            self.basic_adb.adb_reboot()
            self.basic_adb.adb_root()
            print("check mtbf running after reboot:",self.basic_adb.send_adb_command("shell getprop persist.mtbf.running"))    # todo 结束后要记得设回false
        
        self.basic_adb.adb_root()
        print("disable saber")
        self.basic_adb.send_adb_command("shell pm disable com.xiaomi.saber",output=True)
        
        self.caselist = linecache.getlines(caselist)

        guard_thread = threading.Thread(target=self.guard.run_guard)  # todo ADB GUARD
        guard_thread.start()

        jenkinsLink_txt = "config/jenkinsLink.txt"
        if os.path.exists(jenkinsLink_txt):
            os.remove(jenkinsLink_txt)
        try:
            # if self.build_number != 0:   # 传入了build number的说明是要存档到master的（并发构建都要存档，否则找不到日志）
            #     jenkinsLink = os.getenv("JOB_URL") + str(self.build_number) + "/artifact/" # jenkins存档
            # else:
            jenkinsLink = os.getenv("JOB_URL") + "ws/"  # jenkins    # todo 归档往后放
        except Exception:
            jenkinsLink = "."  # 本地
        add_log(jenkinsLink_txt, jenkinsLink, output=True)

        with open(jenkinsLink_txt) as j:
            self.jenkinsLink = j.read().rstrip()
        print("jenkins Link:{}".format(self.jenkinsLink))

        self.build_version = self.basic_adb.get_build_version()
        self.TV_Product_Model = self.get_tv_product_model()
        print("tv product model:",self.TV_Product_Model)
        now_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        if self.build_number != 0:
            self.result_folder = '{}_{}_testResult_{}_{}'.format(self.project_name, self.build_version,self.build_number, now_time)
        else:
            self.result_folder = '{}_{}_testResult_{}'.format(self.project_name, self.build_version, now_time)
        if not os.path.exists(self.result_folder):
            os.mkdir(self.result_folder)
        copyfile(jenkinsLink_txt,os.path.join(self.result_folder,"jenkinsLink.txt"))    # 存一份到当前result目录
        # self.exe_log = create_logger("{}/execute.log".format(self.result_folder))    # 执行日志
        network_type = self.basic_adb.judge_inet()
        if network_type:
            network_ = "network:有线"
        else:
            network_ = "network:Wifi"
        print(network_)
        add_log(os.path.join(self.result_folder,"test_info"),test_categorie+"\n"+test_chname+"\n"+self.board+"\n"+network_)

        # todo reboot mibox,make /proc/cmdline=normal
        print("rebootType before:")
        rebootType = self.tv.send_adb_command("adb -s {} shell 'cat /proc/cmdline'".format(self.device_id), output=True)
        if isinstance(rebootType,str) and "reboot_mode=cold_boot" in rebootType:
            print("===== reboot mibox ,cat /proc/cmdline =====")
            self.basic_adb.adb_reboot()
            self.basic_adb.adb_root()
            print("rebootType after:")
            rebootType = self.tv.send_adb_command("adb -s {} shell 'cat /proc/cmdline'".format(self.device_id),
                                                  output=True)

        self.test_schedule = os.path.join(self.result_folder, "test_schedule")     # 测试进度
        self.plan_testcase = os.path.join(self.result_folder,"plan_testcase")   # 测试计划testcase
        add_log(self.plan_testcase,"{} {}".format(self.LOOP_O,self.LOOP_I))

        # todo 专注模式/SpeedUI压测，开启专注模式log打印
        if self.testtype == 24: self.focusmode()

        self.plan_case = []
        # self.exe_log.info("plan case:")
        print("plan case:")
        for index, line in enumerate(self.caselist):
            line = line.strip()
            if line[:1] == "#" or len(line) == 0:  # 空行
                continue
            else:
                self.plan_case.append(line.strip())
                # self.exe_log.info(line.strip())  # 打印case的名称
                print(line.strip())
                add_log(self.plan_testcase, line)  # 写入plan_testcase文件
        # self.exe_log.info("plan case sum = {}".format(len(self.plan_case)))
        print("plan case sum = {}".format(len(self.plan_case)))

        if self.tv.reconnect(device_id= self.device_id,timeout=5*60):    # 测试开始连接设备
            print("connect to device :{}".format(self.device_id))
        else:
            # todo upload 一次start data然后接着upload result data  & 飞书预警
            if self.upload_data == 1 or self.upload_data == 2:
                if self.upload_data == 1:
                    run_mode = "prod"
                else:
                    run_mode = "dev"
                print("check run mode:", run_mode)
                self.upload_abnormal_infos = UploadTestInfos(device_id = self.device_id,testtype= self.testtype,result_folder= None,Address= self.address,build_url=self.build_url,build_user= self.build_user,run_mode= run_mode)
                self.upload_abnormal_infos.uploadnow_start_info()
                self.upload_abnormal_infos.uploadnow_result_info()
            self.feishu.send_message_interactive(receiver=self.feishu_user,
                                                 subject="自动化测试异常通知",
                                                 message_items={"执行人": self.build_user,
                                                                "电视ip":self.device_id,
                                                                "任务地址": self.build_url,
                                                                "备注": "电视adb连接失败，请检查电视设备状态，测试结束"},
                                                 subject_background_color="yellow")


            raise ValueError("Before start test(环境初始化完成),fail to connect to devices {} in 5 minutes,please check tv status,test exit.".format(self.device_id))


        # todo upload data

        if self.upload_data == 1 or self.upload_data == 2:
            if self.upload_data == 1: run_mode = "prod"
            else: run_mode = "dev"
            print("check run mode:",run_mode)
            self.upload_infos = UploadTestInfos(self.device_id, self.testtype,self.result_folder, self.address, self.build_url,self.build_user,run_mode,self.plan_hours)
            thread_upload_infos = threading.Thread(target=self.upload_infos.run)
            # todo 放后台上传
            thread_upload_infos.start()
        try:
            self.basic_adb.send_adb_command("push ./shell/catch_logs.sh /data/local/tmp")
            self.basic_adb.send_adb_command("shell 'chmod 0777 /data/local/tmp/catch_logs.sh'")

            self.basic_adb.set_tvhome_test_env()    # 打开accessibility service

            deviceinfo = self.get_deviceInfo()
            getprop_txt = os.path.join(self.result_folder,"getprop.txt")
            self.basic_adb.send_adb_command("shell 'getprop' > {}".format(getprop_txt))    # 保存getprop信息到文本文件中

            self.ana_err = AnalysisError(self.device_id, self.result_folder)  # 每调用一次，都会把basic_info初始化，全都置为0

            self.dateBefore = self.basic_adb.send_adb_command("shell 'date +%s'")
            self.uptimeBefore = self.basic_adb.get_uptime()
            self.rebootBefore = float(self.dateBefore) - float(self.uptimeBefore)

            # todo 更新第三方app前，要把原有的uiautomator卸载掉，否则可能会dump失败
            self.brand_tv = self.get_tv_manufacturer()
            print("brand_tv:", self.brand_tv)
            self.basic_adb.send_adb_command("shell 'pm uninstall com.github.uiautomator'",output=True)
            self.basic_adb.send_adb_command("shell 'pm uninstall com.github.uiautomator.test'",output=True)
            self.basic_adb.send_adb_command("shell 'pm uninstall mitv.tvmiddleware.demo'",output=True)
            self.basic_adb.send_adb_command("shell 'pm uninstall mitv.tvmiddleware.demo.test'",output=True)

            # todo 根据caselist，安装第三方app，并进入应用商店把三方app更新至最新
            if not self.tv.reconnect(device_id=self.device_id):   # 默认timeout是5min, check tv state
                # todo upload result data
                add_log(self.test_schedule,"ABNORMAL EXIT")
                self.feishu.send_message_interactive(receiver=self.feishu_user,
                                                     subject="自动化测试异常通知",
                                                     message_items={"执行人": self.build_user,
                                                                    "电视ip": self.device_id,
                                                                    "任务地址": self.build_url,
                                                                    "备注": "电视adb连接失败，请检查电视设备状态，测试结束"},
                                                     subject_background_color="yellow")

                if self.upload_data == 1 or self.upload_data == 2:
                    self.upload_infos.running = False
                    print("set upload info running to False")
                    if thread_upload_infos.is_alive():
                        thread_upload_infos.join()  # 等待upload data的进程结束
                raise ValueError("Before install applications,fail to connect to devices {} in 5 minutes,please check tv status,test exit.".format(self.device_id))
            appstore_exist = self.basic_adb.send_adb_command("shell 'pm list package | grep com.xiaomi.mitv.appstore'", output=True)
            if to_be_installed_apks and appstore_exist:
                self.install_apks(to_be_installed_apks)
                update_apps = UpdateApps(self.device_id)
                # self.exe_log.info("update 3 apps")
                print("update 3 apps")
                update_apps.update_3_apps()
                # todo 更新了三方app，读一次三方的版本
                apks = linecache.getlines(to_be_installed_apks)
                for index, apk_name in enumerate(apks):
                    apk_name = apk_name.strip()
                    if not apk_name: continue
                    self.basic_adb.get_app_version(apk_name)

            self.basic_adb.adb_root()

            self.basic_adb.send_adb_command("shell 'setprop mi.reboot.flag True'")
            self.basic_adb.send_adb_command("shell 'logcat -b all -c'",output = True)    # 压测环境初始化，清除adb logcat缓存

            # 系统是否支持极简模式
            self.SupportSimpleMode = self.basic_adb.send_adb_command("shell 'settings get system mitv.settings.support.simple_mode'",output=True)

            self.basic_adb.send_adb_command("shell 'pm uninstall com.xiaomi.tvqs'", output=True)  # 先卸载了tvqs，后面截图之前会重新安装
            self.basic_adb.adb_root()

            for loop_o in range(self.LOOP_O):
                # self.exe_log.info("LOOP_O>>>>>>>>>>>>>>>>>>>>>>>>>: {}".format(loop_o+1))
                if self.running_test_flag == False:
                    print("停止执行testcase 大loop")
                    break
                print("LOOP_O>>>>>>>>>>>>>>>>>>>>>>>>>: {}".format(loop_o+1))
                for line in self.plan_case:
                    if self.running_test_flag == False:
                        print("停止执行testcase 小loop")
                        break
                    for loop_i in range(self.LOOP_I):
                        if self.running_test_flag == False:
                            print("停止执行testcase 小loop")
                            break
                        # self.exe_log.info("LOOP_I>>>>>>>>>>>>>>>>>>>>>>>>>: {}".format(loop_i+1))
                        print("LOOP_I>>>>>>>>>>>>>>>>>>>>>>>>>: {}".format(loop_i+1))
                        now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        add_log(self.test_schedule,"{} loop_o:{} loop_i:{} {}".format(now_time,loop_o+1,loop_i+1,line))

                        # 启动case之前，check adb
                        check_device = self.check_tv_state("执行测试用例{}前check adb".format(line),timeout=60*30)
                        if not check_device:
                            raise ValueError(
                                "Before start case,fail to connect to devices {} in 30 minutes,please check tv status,test exit.".format(
                                    self.device_id))
                        try:
                            print("start case before: {}".format(line))
                            self.start_case(line)     # todo 启动单个case ,包含analysisError和GetIssueInfo
                            print("start case after: {}".format(line))
                        except Exception as e:
                            # self.exe_log.info("start case error:{}".format(e))
                            print("start case error:{}".format(e))
                            # self.exe_log.info(traceback.print_exc())
                            print(traceback.print_exc())
            # 所有case执行结束
            add_log(deviceinfo,"testEndTime==={}".format(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))),output=True)

            now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            add_log(self.test_schedule, "{} END TESTCASE".format(now_time))

            # todo 所有case都执行完毕，mtbf标志位设回false
            if test_categorie == "stability":
                self.basic_adb.adb_root()
                self.basic_adb.send_adb_command("shell setprop persist.mtbf.running fasle")
                time.sleep(3)
                print("check mtbf running after:",
                      self.basic_adb.send_adb_command("shell getprop persist.mtbf.running"))

            # todo mitv_autotest_parse
            print("Log Parser parameters:")
            print("result_folder : {}".format(self.result_folder))
            print("device_id : {}".format(self.device_id))
            print("testType : {}".format("miplayer"))
            print("build_url : {}".format(self.build_url))
            print("build_user : {}".format(self.build_user))
            print("address : {}".format(self.address))
            # 一开始adb连不上可能获取不到brand_tv
            self.brand_tv = self.get_tv_manufacturer()

            if self.brand_tv and ("xiaomi" in self.brand_tv or "redmi" in self.brand_tv):     # 小米自研的电视才需要上报jira
                self.parser = LogParser(self.device_id,self.result_folder,self.testtype_map[self.testtype],self.build_url,self.build_user)
                self.parser.run_parse()
            else:
                run_parselist(self.result_folder)      # 竟品机不上报jira
            now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            add_log(self.test_schedule, "{} Finished UpdateIssue".format(now_time))
            self.gen_report = Generate(self.device_id,self.result_folder,self.testtype_map[self.testtype],self.build_url,self.build_user,self.address)
            self.gen_report.generating()
            now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            add_log(self.test_schedule, "{} Finished Report".format(now_time))
            # todo sendEmail
            print("send report Email")
            sendEmail = SendEmail(self.device_id, self.result_folder, self.testtype_map[self.testtype], self.build_user)     # sendemail
            sendEmail.mail_sender()
        except Exception as e:
            print("error:{}".format(e))
            print(traceback.print_exc())
        finally:
            print("try 1 finally")
            try:
                if test_categorie == "stability":
                    self.basic_adb.adb_root()      # 如果adb异常，这里会报错，导致thread进程不能正常退出
                    self.basic_adb.send_adb_command("shell setprop persist.mtbf.running fasle")
                    time.sleep(3)
                    print("check mtbf running after:",
                          self.basic_adb.send_adb_command("shell getprop persist.mtbf.running"))
            except Exception as e:
                print("error in reset persist.mtbf.running ")

            if self.upload_data == 1 or self.upload_data == 2:
                self.upload_infos.running = False
                print("set upload info running to False")
                if thread_upload_infos.is_alive():
                    thread_upload_infos.join()     # 等待upload data的进程结束

            print("update result info finish,stop adb guard ")
            self.guard.running = False    # 结束adb guard了
            guard_thread.join()    #等待adb guard 结束
            self.basic_adb.adb_disconnect()          # disconnect the tv
            print("test finished,exit.")
            sys.exit("test finished,exit.")

    def get_tv_manufacturer(self):
        """获取电视厂商"""
        try:
            manufacturer = self.basic_adb.send_adb_command('shell getprop ro.product.manufacturer',output=True).strip().lower()
            print(f"电视厂商: {manufacturer}")
            return manufacturer
        except:
            return 'unknown'

    def write_configini(self,save_path):
        print("write config.ini")
        config = ConfigParser()
        config['devices_info'] = {
            "phone_id":self.phone_id,
            "tv_id" :self.device_id
        }
        print(config)
        with open(save_path, "w") as confile:   # 写入文件到工程的根目录，会覆盖写入
        # with open("script/smartshare/config.ini", "w") as confile:   # 写入文件到工程的根目录，会覆盖写入
            config.write(confile)

    def guard_install_uiautomator(self,device_id, timeout=300, guard_time=600, stop=lambda: False):
        """
        case会卡在初始化uiautomator，一般是install uiautomator进程卡住，导致后续case无法执行
        :param timeout: 安装uiautomator进程超时时间
        :param guard_time: 监控uiautomator进程超时时间，install进程正常结束，case就能正常跑了
        :param stop: 外部控制
        """
        start_time = time.time()
        pids_time = {}
        print("case timeout:{};guard timeout:{}".format(timeout, guard_time))
        running = True
        while running:
            if stop():
                running = False
                print("stop guard install uiautomator")
            if time.time() - start_time > guard_time:
                running = False
                print("guard uiautomator timeout")
            output_ = Popen("ps -ef |grep {} | grep uiautomator | grep install | grep -v grep".format(device_id),
                            shell=True, stdout=PIPE, stderr=STDOUT).stdout.read().decode()
            output_lines = output_.split('\n')
            now_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))

            for line in output_lines:
                if not line: break
                line_elements = line.split()
                pid = line_elements[1]
                if pid not in pids_time.keys():
                    pids_time[pid] = now_time
                else:  # 如果这个pids已经存在，计算该pid存在的时长，超时就kill了
                    s1_t1 = datetime.datetime.strptime(now_time, '%Y-%m-%d %H:%M:%S')
                    s1_t2 = datetime.datetime.strptime(pids_time[pid], '%Y-%m-%d %H:%M:%S')
                    temp_seconds = (s1_t2 - s1_t1).seconds
                    if temp_seconds > timeout:
                        print("kill pid :{}".format(pid))
                        Popen("kill {}".format(pid), shell=True, stdout=PIPE, stderr=STDOUT)
            time.sleep(30)

    def start_case(self,line):
        """
        执行单个case
        :param line: config/caselist/中逐行读取
        :return:
        """
        print("============================== CASE TEST START ... ======================================")
        self.clear_files()   # 在case开始之前，清理电视data文件夹内的相关文件
        fileName = line.split("#")[-1] if "#" in line else line.split(".")[-1]
        now_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        curren_case_folder= "{}_{}".format(fileName,now_time)
        print("case folder: {}".format(curren_case_folder))
        self.case_folder = os.path.join(self.result_folder,curren_case_folder)
        if not os.path.exists(self.case_folder):
            os.mkdir(self.case_folder)
        jenkins_res_url = self.jenkinsLink + self.case_folder
        print("Save log to: {}".format(jenkins_res_url))   # 执行日志中跳转

        self.caselog = os.path.join(self.case_folder,"case.log")
        self.basic_adb.send_adb_command("shell 'mkdir -p /sdcard/MiTVTest/{}'".format(curren_case_folder))
        add_log(self.caselog,"INSTRUMENTATION_STATUS: class= {}".format(line),output=True)

        # todo 保存adb日志
        # self.basic_adb.send_adb_command("shell 'logcat -b all -c'")  # todo 清除adb logcat缓存,后面要放在analysisError里面
        p = Popen("adb -s {} logcat -b all -v threadtime > {}/logcat.log 2>&1".format(self.device_id, self.case_folder),
                  shell=True,stdout=PIPE, stderr=STDOUT)  # 放到后台执行
        p_catch_logs = Popen("adb -s {} shell /system/bin/sh /data/local/tmp/catch_logs.sh {} &".format(self.device_id, self.case_folder),
                  shell=True, stdout=PIPE, stderr=STDOUT)

        # todo before running case ,read base info for judge reboot
        self.ana_err.beforecase(case_folder=self.case_folder)

        # self.basic_adb.send_adb_command("shell 'pm uninstall com.github.uiautomator'",output=True)    # todo 2024.1.26 不卸载uiautomator了
        # self.basic_adb.send_adb_command("shell 'pm uninstall com.github.uiautomator.test'",output=True)

        stop_screenshot = False  # 如果是播放器压测的case，还需要截图
        self.platform_ = self.basic_adb.domestic_or_overseas()
        if self.TV_Product_Model == "Amlogic":    # aml 平台用screencatch截图
            thread_screenshot = threading.Thread(target=self.screencatch_command,args=(0, 3, 60, lambda: stop_screenshot))
        elif self.platform_ == "domestic":   # 国内项目
            thread_screenshot = threading.Thread(target=self.get_video_screencap, args=(0, 3, 60, lambda: stop_screenshot))
        else:     # 海外项目
            thread_screenshot = threading.Thread(target=self.screencap_command, args=(0, 3, 60, lambda: stop_screenshot))

        if "#" not in line:    # python版的case
            module_info, case_info = line.strip().split(':')
            class_name, case_name = case_info.split(".")
            if class_name== "DLNAshare" or class_name == "Miplayshare" or class_name == "Miracastshare" or class_name == "RandomPlayerTest" or class_name == "ATVSource":
                # todo 国内投屏的case需要把传参写入config.ini文件
                self.write_configini("script/smartshare/config.ini")    # 保留一个传参的方法
            # 导入了具体的模块
            module = import_module(module_info[:-3].replace('/', '.'))
            # 从模块中获取测试类
            test_class = getattr(module, class_name)
            print('class name: {}'.format(class_name))
            if class_name== "DLNAshare" or class_name == "Miplayshare" or class_name == "Miracastshare" or class_name == "RandomPlayerTest" or class_name == "ATVSource":
                # todo 国内投屏的case需要把传参写入config.ini文件
                self.write_configini("script/smartshare/config.ini")    # 保留一个传参的方法

            suite = unittest.TestSuite()
            suite.addTest(test_class(case_name))

        self.basic_adb.send_adb_command("shell 'rm -rf storage/emulated/0/Android/data/com.xiaomi.tvqs/files/*'")    # case开始之前先清空电视上原有的截屏
        # self.basic_adb.send_adb_command("shell 'rm -rf /sdcard/screencaps/*'")
        if self.platform_ == "overseas":   # 海外testtype=17、18，海外的mtbf目前也不判断黑屏,海外接入播放相关的case则需要判断黑屏
            # if "MediaPlayer" in class_name:
            #     print("海外media player case，截屏判断黑屏")
            #     stop_screenshot = False
            #     thread_screenshot.start()
            # else:
            print("海外项目，不从框架黑屏，如需要判断黑屏，会在case中根据逻辑截屏存放至电视的sdcard/screencaps目录下")
        else:     # 国内项目
            if self.testtype == 14 or self.testtype == 17 or self.testtype == 18:
                if "ATVSource" in class_name or "RandomPlayerTest" in class_name:
                    print("ATVSource(与源有关)的case 或者 RandomPlayerTest中的的case，在case内根据执行逻辑截图判断黑屏，截图保存在/sdcard/screencaps路径下")
                    self.basic_adb.send_adb_command("install -r -d -t mitv_apks/TvQuaSys.apk", timeout=300, output=True)   # 先把tvqs装上
                else:
                    stop_screenshot = False  # 其他不是源有关的case，则在框架内截图还需要截图
                    thread_screenshot.start()
                    # self.basic_adb.send_adb_command("install -r -d -t mitv_apks/TvQuaSys.apk", timeout=300, output=True)
                    # print("get screenshot by tvqs")
                    # if not os.path.exists(os.path.join(self.case_folder, "screencaps")):
                    #     os.mkdir(os.path.join(self.case_folder, "screencaps"))
            elif self.testtype == 22:   # 国内投屏测试
                print("国内投屏相关压测，不判断黑屏")
            elif self.testtype == 2:
                print("国内稳定性测试，不判断黑屏")
            else:
                print("其他压测项，不需要判断黑屏")

        testCaseStartTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        add_log(self.caselog,"testCaseStartTime=={}".format(testCaseStartTime),output=True)

        if "#" not in line:
            guardui_stop = False
            thread_guardUI = threading.Thread(target=self.guard_install_uiautomator,args=(self.device_id, 300, 600, lambda: guardui_stop))
            thread_guardUI.start()

            with open(self.caselog,"a+",encoding="utf-8") as caselog:
                runner = unittest.TextTestRunner(stream=caselog,verbosity=2,failfast=True)    # case执行结果写入文件
                case_result = runner.run(suite)
                print("=+" * 30)
                print(case_result)
                print("=+" * 30)

            if thread_guardUI.is_alive():
                guardui_stop = True
                thread_guardUI.join()  # 让监控uiautomator进程结束
            self.basic_adb.kill_uiautomator()

        else:      # 海外的电视小loop在这里
            print("run mitv case {}".format(line))
            mitvcase_command = "adb -s {} shell am instrument -w -r -e Loop 50 -e caseFolder {} -e class {} com.xiaomi.mitvcase.test/android.support.test.runner.AndroidJUnitRunner".format(
                    self.device_id,curren_case_folder,line)     # case内循环，让一条case跑得久一点
            print("mitvcase command:",mitvcase_command)
            case_result = self.tv.send_adb_command(mitvcase_command, timeout=1800)
            add_log(self.caselog,case_result)
        print('after test time:{}'.format(time.ctime()))
        testCaseEndTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        s1_t1 = datetime.datetime.strptime(testCaseStartTime, '%Y-%m-%d %H:%M:%S')
        s1_t2 = datetime.datetime.strptime(testCaseEndTime, '%Y-%m-%d %H:%M:%S')
        testTime = (s1_t2 - s1_t1).seconds
        add_log(self.caselog,"testCaseEndTime=={}".format(testCaseEndTime),output=True)
        add_log(self.caselog,"testTime=={}".format(testTime),output=True)

        self.case_running_time += int(testTime)
        case_running_hours = self.case_running_time // 3600    # 换算成小时
        print("当前testcase已执行时长：{}//3600={}小时".format(self.case_running_time,case_running_hours))
        if self.plan_hours > 0 and case_running_hours >= self.plan_hours:
            self.running_test_flag = False
            print("当前testcase已完成计划执行时长，停止压测，解析报告。")

        if thread_screenshot.is_alive():
            stop_screenshot = True
            thread_screenshot.join()     # 停止截图
        # todo 打印caselog
        linecache.clearcache()
        print_caselog = linecache.getlines(self.caselog)
        for print_line in print_caselog: print(print_line.split("\n")[0])

        # todo pull到当前轮结果路径中
        if not self.check_tv_state("执行测试用例{}后pull log前check adb，timeout=5min".format(line),timeout=5*60):
            raise ValueError(
                "Before pull log,fail to connect to devices {} in 5 minutes,please check tv status,test exit.".format(self.device_id))
        self.basic_adb.send_adb_command("pull /sdcard/MiTVTest/{}/ {}/".format(curren_case_folder,self.result_folder),output=True)
        self.basic_adb.send_adb_command("pull /data/local/tmp/dumpsys_cpuinfo.log {}".format(self.case_folder),output=True)
        self.basic_adb.send_adb_command("pull /data/local/tmp/dumpsys_meminfo.log {}".format(self.case_folder),output=True)
        # 停止抓adb logcat
        self.basic_adb.send_adb_command("shell 'pkill logcat'")
        p.terminate()

        # todo 海外电视项目，截屏放在sdcard/screencaps 目录
        if self.TV_Product_Model == "Amlogic":
            print("mv temp to screencaps")
            if os.path.exists(os.path.join(self.case_folder,"temp")):
                os.rename(os.path.join(self.case_folder,"temp"),os.path.join(self.case_folder,"screencaps"))
                #todo bmp 格式转 png
                bmp_path = os.path.join(self.case_folder,"screencaps")
                for bmp_file in os.listdir(bmp_path):
                    bmp_temp = os.path.join(bmp_path,bmp_file)
                    self.bmp_to_png(bmp_temp,bmp_path)

        if self.platform_ == "overseas":
            print("pull overseas screencap from sdcrad/screencaps folder")
            self.basic_adb.send_adb_command("pull sdcard/screencaps {}".format(self.case_folder),timeout=10 * 60)   # pull到本地result folder
            self.basic_adb.send_adb_command("shell 'rm -rf sdcard/screencaps/*'")    # 删掉电视上的截图

            if os.path.exists(os.path.join(self.case_folder,'screencaps')):    # 查看pull下来的截图名称
                for sc in os.listdir(os.path.join(self.case_folder,'screencaps')):
                    print("check screencaps:{}".format(sc))

        else:  # 国内项目
            # todo ATVsourc/RandomPlayerTest 中的case，需要在这里同一pull电视截屏，pull完成之后删掉电视上的截屏
            if self.testtype == 14 or self.testtype == 17 or self.testtype == 18:
                if "ATVSource" in class_name or "RandomPlayerTest" in class_name:
                    if self.project_name == "moderntimes" or self.project_name == "dofus" or self.project_name == "blueplanet":  # 这几个项目不判断黑屏
                        # todo 直接删掉电视上的截图了，不pull了
                        print("{}项目不判断源内截屏黑屏问题，直接删除电视上的截图".format(self.project_name))
                        self.basic_adb.send_adb_command(
                            "shell 'rm -rf storage/emulated/0/Android/data/com.xiaomi.tvqs/files/*'")
                    else:
                        print("pull tvqs screencap by ATVsource case or RandomPlayerTest case from sdcard/screencaps path")
                        if not os.path.exists(os.path.join(self.case_folder, "screencaps")):
                            os.mkdir(os.path.join(self.case_folder, "screencaps"))
                        self.basic_adb.send_adb_command("pull sdcard/screencaps {}".format(os.path.join(self.case_folder)),timeout=10 * 60)  # pull截图到本地
                        self.basic_adb.send_adb_command("shell 'rm -rf storage/emulated/0/Android/data/com.xiaomi.tvqs/files/*'")
                        self.basic_adb.send_adb_command("shell 'rm -rf sdcard/screencaps/*'")  # 删掉电视上的截图
                     # 剩下的由框架截图的用例，截一次就pull一次到服务器上，不需要在这里pull了

        # todo analysisError & GetIssueInfo
        if self.testtype == 20:   # 海外mtbf测试
            self.ana_err.analysing(case_folder=self.case_folder, save_bug_info=True, miplayererror=False,deadsystem=True)
        elif self.platform_ == "overseas":     # 其他海外项目相关测试需要检测deadsystem
            self.ana_err.analysing(case_folder=self.case_folder,save_bug_info=True,miplayererror=True,rm_pictures=True,deadsystem=True)
        elif self.testtype == 2:  # 国内稳定性测试
            self.ana_err.analysing(case_folder=self.case_folder, save_bug_info=True, miplayererror=False)
            # todo 稳定性测试，finch项目，如果有异常需要pull蓝牙日志
            if self.project_name == "finch":
                for key in self.ana_err.ifIssue_dict.keys():
                    if self.ana_err.ifIssue_dict[key] != 0:
                        if self.tv.reconnect(device_id=self.device_id, timeout=5 * 60):
                            self.ana_err.parse.pullBTlog()
                        else:
                            print("fail to pull logs cause cannot connect tv in 5 minutes")
                        break  # pull一次就够了
        elif self.testtype == 22:  # 国内投屏测试
            if self.casetype == 2:    # miracast 投屏解析
                self.ana_err.analysing(case_folder=self.case_folder, save_bug_info=True, miplayererror=False,miracast = True)
            else:
                self.ana_err.analysing(case_folder=self.case_folder, save_bug_info=True, miplayererror=False)
        elif self.testtype == 24: # speedui
            self.ana_err.analysing(case_folder=self.case_folder,save_bug_info=True,miplayererror=False,miracast=False,speedui=True)
        else:
            self.ana_err.analysing(case_folder=self.case_folder, save_bug_info=True, miplayererror=True,rm_pictures=True)
        self.basic_adb.send_adb_command("shell 'rm -rf /sdcard/MiTVTest/*'",output=True)   # 删除电视上MiTVTest文件夹中生成的文件

        print("\n")
        print("============================== CASE TEST END ... ========================================")
        print("\n")

    def check_tv_state(self,test_progress,timeout = 5 * 60):   # todo debug 等待30min太久了，debug先等5min
        print("check tv state,", test_progress)
        tv_adb = self.tv.reconnect(device_id=self.device_id, timeout=timeout)
        if tv_adb:
            return True
        else:
            timeout_min = timeout // 60
            self.feishu.send_message_interactive(receiver=self.feishu_user,
                                                 subject="自动化测试异常通知",
                                                 message_items={"执行人": self.build_user,
                                                                "电视ip": self.device_id,
                                                                "任务地址": self.build_url,
                                                                "备注": "电视adb状态异常，重连{}min仍失败，压测中止".format(timeout_min)},
                                                 subject_background_color="yellow")

            return False

    def clear_files(self):
        """
        在case开始之前，清理电视data文件夹内的相关文件
        :return:
        """
        print("clear TV data files")
        self.basic_adb.send_adb_command("shell 'rm -rf /data/anr/*'")
        self.basic_adb.send_adb_command("shell 'rm -rf /data/tombstones/*'")
        self.basic_adb.send_adb_command("shell 'rm -rf /data/log/*'")
        self.basic_adb.send_adb_command("shell 'rm -rf /data/miuilog/*'")
        self.basic_adb.send_adb_command("shell 'rm -rf /data/system/dropbox/*'")
        self.basic_adb.send_adb_command("shell 'rm -rf /data/user_de/0/com.android.shell/files/bugreports/*'",output=True)
        self.basic_adb.send_adb_command("shell 'rm -rf /sdcard/MiTVTest/*'")  # 删除电视上MiTVTest文件夹中生成的文件

    def get_deviceInfo(self):
        deviceinfo = os.path.join(self.result_folder,"deviceInfo.txt")
        deviceType = self.basic_adb.send_adb_command("shell 'getprop ro.vendor.product.name'",output=True)
        print("deviceType=={}".format(deviceType))
        print("buildVersion=={}".format(self.build_version))
        buildDate = self.basic_adb.send_adb_command("shell 'getprop ro.build.date'")
        print(("getprop ro.build.date==={}".format(buildDate)))
        add_log(deviceinfo,"deviceType=={}".format(deviceType),output=True)
        add_log(deviceinfo,"buildVersion=={}".format(self.build_version),output=True)
        add_log(deviceinfo,"buildDate=={}".format(buildDate),output=True)
        add_log(deviceinfo,"testStartTime=={}".format(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))),output=True)
        return deviceinfo

    def install_apks(self,to_be_installed_apks,apk_path="mitv_apks"):
        """
        预安装apk到电视上
        :param apk_path: to_be_installed_apks
        :param apk_path: apk的路径——默认为mitv_apks
        :return:
        """
        apks = linecache.getlines(to_be_installed_apks)
        for index,apk_name in enumerate(apks):
            apk_name = apk_name.strip()
            if not apk_name: continue
            if apk_name == "com.xiaomi.tweather" and self.project_name == "topgun":
                print("P803 do not install com.xiaomi.tweather")
                continue
            # todo 先判断这个包在电视里有没有
            for i in range(3):
                apk_exist = self.basic_adb.send_adb_command("shell 'pm list package | grep {}'".format(apk_name),output=True)
                if apk_exist:
                    print("apk {} exist".format(apk_name))
                    break
                else:
                    print("install apk {}".format(apk_name))
                    # self.basic_adb.install_apk(os.path.join(apk_path,apk_name+".apk"))
                    self.basic_adb.send_adb_command("install -r -d -t {}".format(os.path.join(apk_path,apk_name+".apk")),timeout=300,output=True)
            else:
                apk_exist = self.basic_adb.send_adb_command("shell 'pm list package | grep {}'".format(apk_name),output=True)
                if not apk_exist:
                    print("Fail to install apk {} 3 times".format(apk_name))
                    self.feishu.send_message_interactive(receiver=self.feishu_user,
                                                         subject="自动化测试异常通知",
                                                         message_items={"执行人": self.build_user,
                                                                        "电视ip": self.device_id,
                                                                        "任务地址": self.build_url,
                                                                        "备注": "电视安装apk: {} 失败".format(apk_name)},
                                                         subject_background_color="yellow")
    def get_tv_product_model(self):
        # product_model = self.getprop("ro.product.model")
        product_model = self.basic_adb.send_adb_command("shell 'getprop ro.product.model'")
        if product_model and product_model.startswith("MiTV-"):
            if product_model[5] == "A":
                return "Amlogic"
            elif product_model[5] == "M":
                return "MTK"
        else:
            print("produce name:{},unknown TV Model Name".format(product_model))
            return "Unknown"

    def bmp_to_png(self,bmp_path, output_dir):
        try:
            # 打开 BMP 图片
            image = Image.open(bmp_path)
            # 获取 BMP 图片的文件名
            file_name = os.path.splitext(os.path.basename(bmp_path))[0]
            # 构建输出的 PNG 图片路径
            output_path = os.path.join(output_dir, f"{file_name}.png")
            # 保存为 PNG 格式
            image.save(output_path, "PNG")
            print(f"成功将 {bmp_path} 转换为 {output_path}")
        except FileNotFoundError:
            print(f"错误：未找到文件 {bmp_path}")
        except Exception as e:
            print(f"发生未知错误：{e}")

    def screencatch_command(self,delay_start=60,screenshot_2 = 3,sleep_time=60, stop=lambda: False):
        """
        AML项目用screencatch截图
        Args:
            delay_start: 延迟开始
            screenshot_2: 截屏的次数
            sleep_time:截屏间隔
            stop:
        Returns:
        """
        self.basic_adb.adb_root()
        self.basic_adb.send_adb_command("shell rm -rf /data/temp")
        self.basic_adb.send_adb_command("shell mkdir /data/temp")
        time.sleep(delay_start)
        self.basic_adb.send_adb_command("shell screencatch -m -t 1")  # 截图
        # todo 看下截图的名字
        screencatch_name = self.basic_adb.send_adb_command("shell ls /data/temp")
        print("screencatch name:", screencatch_name)  # 后续重命名要用到
        self.running = True
        tv_adb_status = time.time()
        while self.running:
            if stop():
                self.running = False
                print("stop screenshot")
                # todo 判断screen_on
            self.basic_adb.send_adb_command("shell rm -rf /data/temp/*")  # 删掉上一轮的截图文件
            screen_on = self.basic_adb.send_adb_command("shell 'getprop sys.screen.turn_on'")
            print("screen_on:{}".format(screen_on))
            if screen_on == "true":  # 亮屏时才截图，息屏时不截图
                temp_time = time.strftime("%Y%m%d-%H%M%S")
                self.basic_adb.send_adb_command("shell screencatch -m -t 1")   #截图
                self.basic_adb.send_adb_command(
                    "shell mv /data/temp/{} /data/temp/{}-1.bmp".format(screencatch_name, temp_time))  # 重命名

                time.sleep(screenshot_2)  # 两张图之间的间隔时间
                self.basic_adb.send_adb_command("shell screencatch -m -t 1")  # 截图
                self.basic_adb.send_adb_command(
                    "shell mv /data/temp/{} /data/temp/{}-2.bmp".format(screencatch_name,temp_time))  # 重命名
                time.sleep(5)
                self.basic_adb.send_adb_command("pull /data/temp {}".format(self.case_folder))   # pull到服务器

            sleep_start = time.time()
            while time.time() - sleep_start < sleep_time:
                if stop():
                    self.running = False
                    print("stop screenshot,break!")
                    break
                else:
                    time.sleep(5)  # todo 5秒检查一次case是否结束，否则等太久了

                # todo 判断电视是否还在
                tv_adb = self.tv.reconnect(device_id=self.device_id, timeout=10)
                if tv_adb:
                    tv_adb_status = time.time()
                else:
                    if time.time() - tv_adb_status > 10 * 60:  # 超过10min就kill
                        self.running = False
                        print("fail to connect tv in 10 min,stop screencap")
                        break

    def screencap_command(self,delay_start=60,screenshot_2 = 3,sleep_time=60, stop=lambda: False):
        """
        海外项目直接用screencap命令截图，源内禁止截图就不判断黑屏了
        :return:
        """
        self.basic_adb.adb_root()
        self.basic_adb.send_adb_command("shell rm -rf /data/screencaps")     # 删掉上一个case的截图文件
        self.basic_adb.send_adb_command("shell mkdir /data/screencaps")     # 截图保存的电视上的位置
        time.sleep(delay_start)
        self.running = True
        tv_adb_status = time.time()
        while self.running:
            if stop():
                self.running = False
                print("stop screenshot")
                # todo 判断screen_on
            self.basic_adb.send_adb_command("shell rm -rf /data/screencaps/*")  # 删掉上一轮的截图文件
            screen_on = self.basic_adb.send_adb_command("shell 'getprop sys.screen.turn_on'")
            print("screen_on:{}".format(screen_on))
            if screen_on == "true":  # 亮屏时才截图，息屏时不截图
                temp_time = time.strftime("%Y%m%d-%H%M%S")
                self.basic_adb.send_adb_command("shell screencap -p /data/screencaps/{}-1.png".format(temp_time))   #截图+命名
                time.sleep(screenshot_2)  # 两张图之间的间隔时间
                self.basic_adb.send_adb_command("shell screencap -p /data/screencaps/{}-2.png".format(temp_time))
                time.sleep(5)
                self.basic_adb.send_adb_command("pull /data/screencaps {}".format(self.case_folder))   # pull到服务器

            sleep_start = time.time()
            while time.time() - sleep_start < sleep_time:
                if stop():
                    self.running = False
                    print("stop screenshot,break!")
                    break
                else:
                    time.sleep(5)  # todo 5秒检查一次case是否结束，否则等太久了

                # todo 判断电视是否还在
                tv_adb = self.tv.reconnect(device_id=self.device_id, timeout=10)
                if tv_adb:
                    tv_adb_status = time.time()
                else:
                    if time.time() - tv_adb_status > 10 * 60:  # 超过10min就kill
                        self.running = False
                        print("fail to connect tv in 10 min,stop screencap")
                        break


    def judge_screencap_size(self):
        # todo 判断截图的大小，如果小于50k，则用pngtest接入存一张
        filesize = self.basic_adb.send_adb_command("shell ls -l /storage/emulated/0/Android/data/com.xiaomi.tvqs/files/*")
        try:
            filesize_ = filesize.split("\n")
            for f in filesize_:
                try:
                    temp_file_size_list = f.split(" ")
                    temp_file_size_list = list(filter(None, temp_file_size_list))
                    temp_file_size = int(temp_file_size_list[4])
                    if temp_file_size < 50000:  # 小于50k
                        print("screencap size is {},get pngtest 1".format(temp_file_size))
                        self.basic_adb.send_adb_command("shell pngtest 1")
                        time.sleep(2)
                        print("pull data temp")
                        self.basic_adb.send_adb_command("pull /data/temp {}".format(self.case_folder),timeout=5*60)     # pull到本地 case folder 目录
                        break   # 一张图就行
                except Exception as e:
                    print("error in filesize_")
                    print(f)
                    print(traceback.print_exc())  # 定位出错语句
                    print("pngtest exception1")
                    self.basic_adb.send_adb_command("shell pngtest 1")
                    time.sleep(2)
                    print("pull data temp")
                    self.basic_adb.send_adb_command("pull /data/temp {}".format(self.case_folder),
                                                    timeout=5 * 60)  # pull到本地 case folder 目录
        except Exception as e:
            print("judge screencap size error")
            print("check ls -l:{}".format(filesize))  # 定位出错语句
            print(traceback.print_exc())  # 定位出错语句
            print("pngtest exception2")
            self.basic_adb.send_adb_command("shell pngtest 1")
            time.sleep(2)
            print("pull data temp")
            self.basic_adb.send_adb_command("pull /data/temp {}".format(self.case_folder),
                                            timeout=5 * 60)  # pull到本地 case folder 目录

    def get_video_screencap(self,delay_start=60,screenshot_2 = 3,sleep_time=60, stop=lambda: False):
        """
        播放视频时的截图，需要用到tvqs
        """
        # self.basic_adb.install_apk("mitv_apks/TvQuaSys.apk")  # 安装这个apk
        self.basic_adb.send_adb_command("install -r -d -t mitv_apks/TvQuaSys.apk",timeout=300,output=True)
        print("get screenshot by tvqs")
        # todo 先清理电视中上一轮的files
        self.basic_adb.send_adb_command("shell 'rm -rf storage/emulated/0/Android/data/com.xiaomi.tvqs/files/*'")
        if not os.path.exists(os.path.join(self.case_folder,"screencaps")):
            os.mkdir(os.path.join(self.case_folder,"screencaps"))
        time.sleep(delay_start)
        self.running = True
        tv_adb_status = time.time()
        while self.running:
            if stop():
                self.running = False
                print("stop screenshot")
            self.basic_adb.send_adb_command("shell 'rm -rf storage/emulated/0/Android/data/com.xiaomi.tvqs/files/*'")    # 删掉上一轮的图
            time.sleep(2)

            # todo 判断screen_on
            screen_on = self.basic_adb.send_adb_command("shell 'getprop sys.screen.turn_on'")
            if screen_on == "true":   # 亮屏时才截图，息屏时不截图
                self.basic_adb.send_adb_command('shell am broadcast -a mitv.action.debug.capture --es cmd "capture"')
                time.sleep(screenshot_2)    # 两张图之间的间隔时间
                self.basic_adb.send_adb_command('shell am broadcast -a mitv.action.debug.capture --es cmd "capture"')
                time.sleep(5)
                # judge screencap size
                self.judge_screencap_size()
                # todo pull到电脑上
                self.basic_adb.send_adb_command("pull storage/emulated/0/Android/data/com.xiaomi.tvqs/files {}".format(self.case_folder),timeout=5*60)  # pull截图到本地
                # todo rename & mv to screencaps dir
                index = 1
                temp_time = time.strftime("%Y%m%d-%H%M%S")
                for capture in os.listdir(self.case_folder+"/files"):
                    print("check capture:{}".format(capture))    # 黑屏被怀疑只截了一张图，查看两张图的原命名是否是同一张
                    if capture.endswith(".jpg") or capture.endswith(".png"):
                        os.rename(os.path.join(self.case_folder,"files",capture),os.path.join(self.case_folder,"screencaps","screencap_{}-{}.jpg".format(temp_time,index)))
                        index += 1
                # time.sleep(sleep_time)    # 这里会等待太久
                sleep_start = time.time()
                while time.time() - sleep_start < sleep_time:
                    if stop():
                        self.running = False
                        print("stop screenshot,break!")
                        break
                    else:
                        time.sleep(5)    # todo 5秒检查一次case是否结束，否则等太久了

                    # todo 判断电视是否还在
                    tv_adb = self.tv.reconnect(device_id=self.device_id, timeout=10)
                    if tv_adb:
                        tv_adb_status = time.time()
                    else:
                        if time.time() - tv_adb_status > 10*60:   # 超过10min就kill
                            self.running = False
                            print("fail to connect tv in 10 min,stop screencap")
                            break

    def focusmode(self):
        "speed UI 压测，开启专注模式log打印，需要重启生效"
        print("setprop focusmode log")
        self.basic_adb.adb_root()
        self.basic_adb.send_adb_command("shell setprop persist.mitv.focusmode.log true")
        time.sleep(3)
        self.basic_adb.adb_reboot()    # 重启电视生效
        self.basic_adb.adb_root()


if __name__ == '__main__':

    parser = argparse.ArgumentParser(description="Parameters")
    parser.add_argument("--device_id", type=str, help="测试设备ip地址")
    parser.add_argument("--phone_id", type=str, default="None",help="手机ip地址")
    parser.add_argument("--testtype", type=int, help="测试类型列表映射：14：小米播放器;17:信号源稳定性测试;18:Display稳定性测试")
    parser.add_argument("--casetype", type=int, help="测试列表映射")   # 先根据testtype再去找到对应的映射表
    parser.add_argument("--LOOP_I", type=int, help="小循环，每条case循环次数", default=1)
    parser.add_argument("--LOOP_O", type=int, help="大循环，caselist列表循环次数", default=1)
    parser.add_argument("--address", type=str, help="测试地点", default="unknown")
    parser.add_argument("--build_number",type=int,help="Jenkins压测任务的BUILD NUMBER",default=0)
    parser.add_argument("--job_name",type=str,help="Jenkins压测任务的JOB NAME,如果有@需要带上",default="")    # 这两个参数可以拼出来build_url
    parser.add_argument("--build_user", type=str, help="测试执行人", default="unknown")
    parser.add_argument("--upload_data", type=int, help="是否上报云平台，0：不上传，1：正式环境上传，2，测试环境上传", default=0)
    parser.add_argument("--plan_hours", type=int, help="计划执行时长", default=0)   # 默认为0，完成所有case后再结束，如果非零正整数，则当case执行时长超过计划执行时长，就停止测试
    parser.add_argument("--is_motherboard", type=int, help="是否是主板压测", default=0)  # 0：否（使用电视整机压测）；1：是（使用主板压测）
    parser.add_argument("--scan_type", type=str, help='扫台类型', default="None")  # liveTV扫台类型
    parser.add_argument("--switch_type", type=str, help="扫台方式", default="None")  # liveTV扫台方式

    args = parser.parse_args()

    # todo 设置指定电视设备
    os.environ["ANDROID_SERIAL"] = args.device_id
    print("set environ ANDROID SERIAL = {}".format(args.device_id))
    if args.scan_type != "None":    # 如果是liveTV扫台压测并且填入了这两个参数，则写入环境变量
        os.environ["SCAN_TYPE"] = args.scan_type
        os.environ["SWITCH_TYPE"] = args.switch_type
    # print(type(args.device_id),type(args.caselist),type(args.LOOP_O),type(args.LOOP_I),type(args.address),type(args.upload_data))
    job_build_url = "http://jenkins.tv.xiaomi.srv/job/{}/{}/console".format(args.job_name,args.build_number)  # 拼接执行日志build_url
    try:
        mitv_test = Mitv_Test(args.device_id, args.testtype, args.casetype, args.LOOP_O, args.LOOP_I, args.address,
                              job_build_url, args.build_user, args.upload_data, args.build_number,args.plan_hours,args.phone_id,args.is_motherboard)
        mitv_test.run_test()
    except Exception as e:
        print("执行测试出现异常，压测中止")
        print(traceback.print_exc())  # 定位出错语句
        print(e)
        print("重新解析报告")  # guard report
    finally:     # 每次压测都guard是否生成了报告
        testtype_map = {
            1: "monkey",
            2: "stability",
            14: "miplayer",  # 小米播放器测试
            17: "HDMI-stability",  # 信号源稳定性测试
            18:"Display-stability",  # display 稳定性测试
            19:"connectivity",   # connectivity 稳定性测试
            20:"stability-overseas",  # 海外稳定性测试
            21:"LiveTV",   # LiveTV 海外
            22:"smartshare",  # 投屏专项测试
            23: "photonengine",  # 光子引擎测试
            24: "SpeedUI",  # speed UI 测试
        }
        print("guard report parameters:")
        print(args.device_id, args.job_name, args.build_number, testtype_map[args.testtype], job_build_url, args.build_user, args.address)
        guard_report(args.device_id, args.job_name, args.build_number, testtype_map[args.testtype], job_build_url, args.build_user, args.address)
        exit()

#python3.8 -u main.py --device_id ${DEVICEID} --testtype ${TESTTYPE} --casetype ${CASELIST} --LOOP_I ${LOOP_I} --LOOP_O ${LOOP_O} --address $Address --build_number ${BUILD_NUMBER} --job_name ${JOB_NAME} --build_user $BUILD_USER --upload_data ${UPLOAD_DATA}
#python3 -u main.py --device_id 10.241.137.xxx --testtype 14 --casetype 0 --LOOP_I 3 --LOOP_O 4 --address 北京科技园 --build_number 15 --job_name MiPlayer_Test_08 --build_user v-yuyang8 --upload_data 0


# python3 -u main.py --device_id ************** --testtype 22 --casetype 1 --LOOP_I 10 --LOOP_O 10 --address 北京科技园 --build_number 2 --job_name QA_Smartshare_Test_03 --build_user ROBOT-MITV --upload_data 0 --plan_hours 24 --phone_id af915268
# venv/bin/python -u main.py --device_id 48065272610000085 --testtype 14 --casetype 5 --LOOP_I 5 --LOOP_O 1000 --address 南京 --build_number 853 --job_name Bigfish_MiPlayerTest --build_user v-fuzhili --upload_data 0 --plan_hours 48
