#!/bin/bash

# 高级screencaps文件提取脚本
# 支持多种选项和文件类型过滤

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认配置
CURRENT_DIR=$(pwd)
EXTRACT_DIR="$CURRENT_DIR/extracted_screencaps"
KEEP_STRUCTURE=false
FILE_TYPES=""
DRY_RUN=false
VERBOSE=false
COPY_FOLDERS=false

# 显示帮助信息
show_help() {
    echo -e "${BLUE}screencaps文件提取脚本${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  $0 [选项]"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo "  -h, --help          显示此帮助信息"
    echo "  -o, --output DIR    指定输出目录 (默认: ./extracted_screencaps)"
    echo "  -s, --structure     保持原始目录结构"
    echo "  -t, --types TYPES   只提取指定类型的文件 (如: png,jpg,mp4)"
    echo "  -d, --dry-run       预览模式，不实际复制文件"
    echo "  -v, --verbose       详细输出模式"
    echo "  -f, --folders       直接复制整个screencaps文件夹，而不是单独复制文件"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  $0                                    # 提取所有文件"
    echo "  $0 -o /tmp/screenshots               # 指定输出目录"
    echo "  $0 -t png,jpg                       # 只提取图片文件"
    echo "  $0 -s -v                            # 保持结构并详细输出"
    echo "  $0 -f                               # 直接复制整个screencaps文件夹"
    echo "  $0 -f -o /tmp/folders               # 复制文件夹到指定目录"
    echo "  $0 -d                               # 预览模式"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output)
            EXTRACT_DIR="$2"
            shift 2
            ;;
        -s|--structure)
            KEEP_STRUCTURE=true
            shift
            ;;
        -t|--types)
            FILE_TYPES="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -f|--folders)
            COPY_FOLDERS=true
            shift
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 检查文件类型
check_file_type() {
    local file="$1"
    local filename=$(basename "$file")
    local extension="${filename##*.}"
    
    if [ -z "$FILE_TYPES" ]; then
        return 0  # 接受所有文件类型
    fi
    
    # 将FILE_TYPES转换为数组
    IFS=',' read -ra TYPES <<< "$FILE_TYPES"
    for type in "${TYPES[@]}"; do
        if [ "${extension,,}" = "${type,,}" ]; then
            return 0  # 匹配的文件类型
        fi
    done
    
    return 1  # 不匹配的文件类型
}

# 获取相对路径
get_relative_path() {
    local full_path="$1"
    local base_path="$2"
    echo "${full_path#$base_path/}"
}

# 创建目录结构
create_dir_structure() {
    local file_path="$1"
    local base_dir="$2"
    local dir_path=$(dirname "$file_path")
    
    if [ "$dir_path" != "." ]; then
        mkdir -p "$base_dir/$dir_path"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}=== screencaps文件提取脚本 ===${NC}"
    echo -e "${YELLOW}当前目录: $CURRENT_DIR${NC}"
    echo -e "${YELLOW}输出目录: $EXTRACT_DIR${NC}"
    
    if [ "$COPY_FOLDERS" = true ]; then
        echo -e "${YELLOW}模式: 复制整个文件夹${NC}"
    elif [ "$KEEP_STRUCTURE" = true ]; then
        echo -e "${YELLOW}模式: 保持目录结构${NC}"
    else
        echo -e "${YELLOW}模式: 扁平化文件${NC}"
    fi
    
    if [ -n "$FILE_TYPES" ]; then
        echo -e "${YELLOW}文件类型过滤: $FILE_TYPES${NC}"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        echo -e "${PURPLE}*** 预览模式 - 不会实际复制文件 ***${NC}"
    fi
    
    echo ""
    
    # 创建输出目录
    if [ "$DRY_RUN" = false ]; then
        mkdir -p "$EXTRACT_DIR"
    fi
    
    # 计数器
    total_files=0
    total_folders=0
    folder_counter=1
    skipped_files=0
    
    # 查找所有screencaps文件夹
    echo -e "${BLUE}正在搜索screencaps文件夹...${NC}"
    
    while IFS= read -r -d '' screencaps_dir; do
        if [ -d "$screencaps_dir" ]; then
            total_folders=$((total_folders + 1))
            echo -e "${GREEN}找到文件夹 $total_folders: $screencaps_dir${NC}"

            # 检查文件夹是否为空
            if [ -z "$(ls -A "$screencaps_dir")" ]; then
                echo -e "${YELLOW}  -> 文件夹为空，跳过${NC}"
                continue
            fi

            # 计算该文件夹中的文件数量
            file_count=$(find "$screencaps_dir" -type f | wc -l)
            echo -e "${BLUE}  -> 包含 $file_count 个文件${NC}"

            # 如果选择复制整个文件夹
            if [ "$COPY_FOLDERS" = true ]; then
                # 生成目标文件夹名称
                if [ $total_folders -eq 1 ] && [ $(find "$CURRENT_DIR" -name "screencaps" -type d | wc -l) -eq 1 ]; then
                    target_folder_name="screencaps"
                else
                    target_folder_name="screencaps${total_folders}"
                fi

                target_path="$EXTRACT_DIR/$target_folder_name"

                if [ "$DRY_RUN" = false ]; then
                    # 复制整个文件夹
                    cp -r "$screencaps_dir" "$target_path"
                    if [ $? -eq 0 ]; then
                        echo -e "  ${GREEN}✓${NC} 文件夹已复制到: $target_folder_name"
                        total_files=$((total_files + file_count))
                    else
                        echo -e "  ${RED}✗${NC} 文件夹复制失败: $screencaps_dir"
                    fi
                else
                    echo -e "  ${GREEN}✓${NC} [预览] 将复制文件夹到: $target_folder_name"
                    total_files=$((total_files + file_count))
                fi

                echo ""
                continue
            fi
            
            # 处理文件
            file_counter=1
            while IFS= read -r -d '' file; do
                if [ -f "$file" ]; then
                    # 检查文件类型
                    if ! check_file_type "$file"; then
                        if [ "$VERBOSE" = true ]; then
                            echo -e "  ${YELLOW}跳过${NC} $(basename "$file") (文件类型不匹配)"
                        fi
                        skipped_files=$((skipped_files + 1))
                        continue
                    fi
                    
                    filename=$(basename "$file")
                    extension="${filename##*.}"
                    
                    if [ "$KEEP_STRUCTURE" = true ]; then
                        # 保持目录结构
                        rel_path=$(get_relative_path "$file" "$CURRENT_DIR")
                        new_path="$EXTRACT_DIR/$rel_path"
                        new_dir=$(dirname "$new_path")
                        
                        if [ "$DRY_RUN" = false ]; then
                            mkdir -p "$new_dir"
                            cp "$file" "$new_path"
                        fi
                        
                        if [ "$DRY_RUN" = true ] || [ $? -eq 0 ]; then
                            echo -e "  ${GREEN}✓${NC} $rel_path"
                            total_files=$((total_files + 1))
                        else
                            echo -e "  ${RED}✗${NC} 复制失败: $rel_path"
                        fi
                    else
                        # 扁平化文件
                        if [ "$extension" = "$filename" ]; then
                            new_filename="screencaps${folder_counter}_${file_counter}_${filename}"
                        else
                            new_filename="screencaps${folder_counter}_${file_counter}.${extension}"
                        fi
                        
                        if [ "$DRY_RUN" = false ]; then
                            cp "$file" "$EXTRACT_DIR/$new_filename"
                        fi
                        
                        if [ "$DRY_RUN" = true ] || [ $? -eq 0 ]; then
                            echo -e "  ${GREEN}✓${NC} $filename -> $new_filename"
                            total_files=$((total_files + 1))
                            file_counter=$((file_counter + 1))
                        else
                            echo -e "  ${RED}✗${NC} 复制失败: $filename"
                        fi
                    fi
                fi
            done < <(find "$screencaps_dir" -type f -print0)
            
            folder_counter=$((folder_counter + 1))
            echo ""
        fi
    done < <(find "$CURRENT_DIR" -name "screencaps" -type d -print0)
    
    # 输出统计信息
    echo -e "${BLUE}=== 提取完成 ===${NC}"
    echo -e "${GREEN}总共找到 $total_folders 个screencaps文件夹${NC}"
    echo -e "${GREEN}总共处理 $total_files 个文件${NC}"
    
    if [ $skipped_files -gt 0 ]; then
        echo -e "${YELLOW}跳过 $skipped_files 个文件 (类型不匹配)${NC}"
    fi
    
    if [ "$DRY_RUN" = false ]; then
        echo -e "${YELLOW}所有文件已保存到: $EXTRACT_DIR${NC}"
        
        # 显示文件统计
        if [ $total_files -gt 0 ] && [ -d "$EXTRACT_DIR" ]; then
            echo ""
            echo -e "${BLUE}文件类型统计:${NC}"
            cd "$EXTRACT_DIR"
            for ext in $(find . -name "*.*" -type f | sed 's/.*\.//' | sort | uniq); do
                count=$(find . -name "*.${ext}" -type f | wc -l)
                echo -e "  ${CYAN}.$ext 文件: $count 个${NC}"
            done
            cd "$CURRENT_DIR"
        fi
    else
        echo -e "${PURPLE}*** 这是预览模式的结果 ***${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}脚本执行完成！${NC}"
}

# 执行主函数
main
