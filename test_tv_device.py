#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
测试TV设备ID读取功能
"""

import sys
import os
sys.path.append('script/testcases')

try:
    from adb_command import TV_DEVICE_ID, get_tv_manufacturer
    print(f"成功导入adb_command模块")
    print(f"TV_DEVICE_ID: {TV_DEVICE_ID}")
    
    if TV_DEVICE_ID:
        print(f"设备ID读取成功: {TV_DEVICE_ID}")
        print("现在所有adb_command中的函数都会自动使用这个设备ID")
        print("例如:")
        print("  adb_home()  # 会自动使用TV_DEVICE_ID")
        print("  adb_back()  # 会自动使用TV_DEVICE_ID")
        print("  get_tv_manufacturer()  # 会自动使用TV_DEVICE_ID")
    else:
        print("设备ID读取失败，请检查配置文件")
        
except Exception as e:
    print(f"导入失败: {e}")
    import traceback
    traceback.print_exc()
