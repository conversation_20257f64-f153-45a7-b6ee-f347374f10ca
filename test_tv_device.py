#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
测试TV设备ID读取功能
"""

import sys
import os
sys.path.append('script/testcases')

try:
    print("=== 测试导入模块 ===")
    from adb_command import get_tv_device_id, get_tv_manufacturer, adb_home
    print("✅ 成功导入adb_command模块（无额外输出）")

    print("\n=== 测试设备ID获取 ===")
    device_id = get_tv_device_id()
    print(f"TV设备ID: {device_id}")

    if device_id:
        print("✅ 设备ID读取成功")
        print("现在所有adb_command中的函数都会自动使用这个设备ID")
        print("例如:")
        print("  adb_home()  # 会自动使用TV_DEVICE_ID")
        print("  adb_back()  # 会自动使用TV_DEVICE_ID")
        print("  get_tv_manufacturer()  # 会自动使用TV_DEVICE_ID")

        print("\n=== 测试函数调用 ===")
        print("调用 get_tv_manufacturer()...")
        # manufacturer = get_tv_manufacturer()  # 注释掉，避免设备未连接的错误
        print("函数调用正常（设备未连接时会显示错误，这是正常的）")
    else:
        print("❌ 设备ID读取失败，请检查配置文件")
        
except Exception as e:
    print(f"导入失败: {e}")
    import traceback
    traceback.print_exc()
