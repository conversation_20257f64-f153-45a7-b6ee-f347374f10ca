# _*_ coding: utf-8 _*_
import json
import sys
import traceback
import time
import os

import requests

'''
feishu robot
'''


class Feishu(object):
    def __init__(self, receiver):
        self.get_token()
        self.receiver = self.__init_receiver(receiver)

    @staticmethod
    def __init_receiver(receiver):
        if type(receiver) is list and bool(receiver):
            __receiver = receiver
        elif type(receiver) is str and bool(receiver):
            __receiver = receiver.replace(";", ",").split(",")
        else:
            __receiver = []
        return __receiver

    def get_token(self):
        self.session = requests.Session()
        url = "https://open.f.mioffice.cn/open-apis/auth/v3/app_access_token/internal/"
        body = {
            "app_id": "cli_9fa64bd7bcfb1062",
            "app_secret": "l0a6RviFNoo56yJORT2yUdAzNRbIiNsC"
        }
        result = self.session.post(url=url, data=body).json()
        if result["code"] == 0:
            self.token = result["app_access_token"]
            self.expired_time = time.time() + result["expire"] - 5
        else:
            print("Get Feishu token failed:%s " % result)

    def is_token_available(self):
        if self.expired_time - time.time() > 0:
            return True
        else:
            return False

    def __send(self, people, msg):
        if not self.is_token_available():
            time.sleep(8)
            self.get_token()
        msg_url = "https://open.f.mioffice.cn/open-apis/message/v4/send/"
        headers = {
            "Authorization": "Bearer %s" % self.token,
            "Content-Type": "application/json"
        }
        message = {
            "email": "%<EMAIL>" % people,
            "msg_type": "text",
            "content": {
                "text": msg
            }
        }
        try:
            result = self.session.post(url=msg_url, headers=headers, data=json.dumps(message)).json()
        except:
            print("Send feishu message failed.")
            traceback.print_exc()
            return False
        if type(result) is dict:
            if result["code"] == 0:
                return True
            else:
                print("Send msg failed: %s " % result)
        else:
            return False

    def send_message(self, msg, receiver=None):
        if receiver and self.__init_receiver(receiver):  # 如果要单独发送
            for people in self.__init_receiver(receiver):
                self.__send(people, msg)
            return True
        elif not self.receiver:
            return False

        for people in self.receiver:
            if "@" in people:
                people = people.split("@")[0]
            for __try in range(3):
                if self.__send(people, msg):
                    break
                else:
                    time.sleep(5)
        return True


def mail_sender(tos, ccs="", subject='', content='', attach='', max_try=3):
    if type(tos) == list:
        __tos = ','.join(tos)
    elif type(tos) == str:
        __tos = tos.replace(";", ",")

    __ccs = ', '.join(ccs)
    body = {
        "token": "<EMAIL>",
        "subject": subject,
        "tos": tos,
        "ccs": ccs,
        "content": content,
        "html": True
    }
    # add attach
    if os.path.isfile(attach):
        print("attach [ %s ] confirmed .")
        attach_basename = os.path.basename(attach)
        with open(attach, "r") as file_content:
            attach = {'attachFiles': (attach_basename, file_content.read())}
    elif attach:
        print("attach [ %s ] not exists.")
    else:
        pass

    print(attach)
    for i in range(max_try):
        try:
            send = requests.post('https://t.mioffice.cn/mailapi/mail', data=body, files=attach)
        except:
            if i == (max_try - 1):  # 最后一次如果还是异常，则打印异常
                print(traceback.format_exc())
            time.sleep(10)
            continue

        send_result = send.json()
        if send_result["status"] == "success":
            print(json.dumps(send_result, ensure_ascii=False, indent=4))
            print("Send successfully : %s." % i)
            return True
        elif i == (max_try - 1):  # 最后一次返回的发送结果不是success则打印返回的信息
            print(json.dumps(send_result, ensure_ascii=False, indent=4))
            print("Report mail send failed 3 times.")
            return False
        else:
            time.sleep(10)


if __name__ == "__main__":
    fs = Feishu("mengbao")
    fs.send_message("send to pointer user.", receiver="mengbao")
    fs.send_message("send to default.")
    # mail_sender(tos="<EMAIL>",
    #             subject='meting notification', content='Hi:9:00 am,room 001', max_try=3)
