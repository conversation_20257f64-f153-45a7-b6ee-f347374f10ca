#!/usr/bin/python
# -*- coding:utf-8 -*-

import sys
import os
import time

# 添加项目路径
sys.path.append('/home/<USER>/code/MiPlayerTest')

from script.smartshare.DLNA import DLNAshare
from subprocess import PIPE, STDOUT, Popen

def timeout_command(command):
    """执行命令"""
    try:
        state = Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
        return out
    except Exception as e:
        print(f"命令执行失败: {e}")
        return ""

def get_screen_status():
    """获取TCL电视屏幕状态"""
    # 设置TCL电视为目标设备
    os.environ['ANDROID_SERIAL'] = '4C04902902004A0B4'
    status = timeout_command('adb shell getprop sys.screen.turn_on')
    return status.strip()

def test_dlna_remote_control():
    """测试DLNA遥控器控制TCL电视"""
    print("=" * 50)
    print("测试DLNA遥控器控制TCL电视")
    print("=" * 50)
    
    try:
        # 1. 初始化DLNA
        print("1. 初始化DLNA...")
        dlna = DLNAshare()
        dlna.setUp()
        print(f"✅ DLNA初始化成功")
        print(f"手机ID: {dlna.phone_id}")
        print(f"电视ID: {dlna.tv_id}")
        
        # 2. 检查TCL电视当前状态
        print("\n2. 检查TCL电视当前状态...")
        current_status = get_screen_status()
        print(f"TCL电视屏幕状态: {current_status}")
        
        # 3. 使用遥控器控制电视
        print("\n3. 使用手机遥控器控制TCL电视...")
        
        # 假设遥控器界面有"客厅电视"或"TCL电视"等文本
        # 你需要根据实际界面调整这个文本
        target_texts = ["TCL"]
        
        success = False
        for target_text in target_texts:
            print(f"尝试查找文本: {target_text}")
            result = dlna.remote_control_operation(target_text)
            if result:
                print(f"✅ 遥控器操作成功，使用文本: {target_text}")
                success = True
                break
            else:
                print(f"❌ 遥控器操作失败，文本: {target_text}")
        
        if not success:
            print("❌ 所有文本都未找到，尝试直接点击电源键...")
            # 直接启动遥控器应用并点击电源键
            timeout_command(f'adb -s {dlna.phone_id} shell am start -S com.duokan.phone.remotecontroller/com.xiaomi.mitv.phone.remotecontroller.HoriWidgetMainActivityV2')
            time.sleep(5)
            
            if dlna.d_phone(resourceId="com.duokan.phone.remotecontroller:id/btn_power").exists:
                dlna.d_phone(resourceId="com.duokan.phone.remotecontroller:id/btn_power").click()
                print("✅ 直接点击电源键成功")
                success = True
            else:
                print("❌ 未找到电源键按钮")
        time.sleep(10)
        
        # 4. 检查操作结果
        if success:
            print("\n4. 检查操作结果...")
            time.sleep(5)  # 等待电视响应
            new_status = get_screen_status()
            print(f"操作后TCL电视屏幕状态: {new_status}")
            
            if current_status != new_status:
                print("✅ 电视状态已改变，遥控器控制成功！")
                return True
            else:
                print("⚠️ 电视状态未改变，可能需要更长等待时间")
                return False
        else:
            print("❌ 遥控器操作失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False

def test_multiple_remote_operations(count=3):
    """测试多次遥控器操作"""
    print("=" * 50)
    print(f"测试{count}次遥控器操作")
    print("=" * 50)
    
    success_count = 0
    
    for i in range(count):
        print(f"\n🔄 第{i+1}次遥控器操作")
        print("-" * 30)
        
        result = test_dlna_remote_control()
        if result:
            success_count += 1
            print(f"✅ 第{i+1}次操作成功")
        else:
            print(f"❌ 第{i+1}次操作失败")
        
        if i < count - 1:  # 不是最后一次
            print("等待10秒后进行下一次操作...")
            time.sleep(10)
    
    print("\n" + "=" * 50)
    print("遥控器操作测试结果")
    print("=" * 50)
    print(f"总操作次数: {count}")
    print(f"成功次数: {success_count}")
    print(f"失败次数: {count - success_count}")
    print(f"成功率: {(success_count/count)*100:.1f}%")

if __name__ == "__main__":
    print("DLNA遥控器控制TCL电视测试脚本")
    print("手机: 711d9be")
    print("TCL电视: 4C04902902004A0B4")
    print()
    
    # 检查设备连接
    print("检查设备连接...")
    devices = timeout_command('adb devices')
    print("当前连接设备:")
    print(devices)
    
    if "711d9be" not in devices:
        print("❌ 手机设备未连接")
        exit(1)
    
    if "4C04902902004A0B4" not in devices:
        print("❌ TCL电视设备未连接")
        exit(1)
    
    print("✅ 所有设备连接正常")
    print()
    
    if len(sys.argv) > 1:
        try:
            count = int(sys.argv[1])
            test_multiple_remote_operations(count)
        except ValueError:
            print("参数错误，请输入数字")
            print("用法: python test_dlna_remote.py [操作次数]")
    else:
        test_dlna_remote_control()
