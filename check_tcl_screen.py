#!/usr/bin/python
# -*- coding:utf-8 -*-

import os
import time
from subprocess import PIPE, STDOUT, Popen

# 设置TCL电视为目标设备
os.environ['ANDROID_SERIAL'] = '4C04902902004A0B4'

def run_command(command):
    """执行命令并返回结果"""
    try:
        state = Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
        return out
    except Exception as e:
        return f"错误: {e}"

def check_screen_methods():
    """使用多种方法检测TCL屏幕状态"""
    print("=" * 50)
    print("TCL电视屏幕状态检测")
    print("设备: 4C04902902004A0B4")
    print("=" * 50)
    
    results = []
    
    # 方法1: 检查原始属性
    print("1. 检查 sys.screen.turn_on 属性:")
    screen_prop = run_command('adb shell getprop sys.screen.turn_on')
    print(f"   结果: '{screen_prop}'")
    if screen_prop and screen_prop != '':
        results.append(('screen_prop', 'true' in screen_prop.lower()))
    else:
        print("   ❌ 该属性为空或不存在")
    
    # 方法2: 检查显示服务状态
    print("\n2. 检查显示服务状态:")
    display_state = run_command('adb shell dumpsys display | grep -i "state"')
    print(f"   结果: {display_state}")
    if display_state:
        is_on = 'ON' in display_state.upper()
        results.append(('display_state', is_on))
        print(f"   判断: {'亮屏' if is_on else '熄屏'}")
    
    # 方法3: 检查电源管理
    print("\n3. 检查电源管理状态:")
    power_state = run_command('adb shell dumpsys power | grep -i "display"')
    print(f"   结果: {power_state}")
    if power_state:
        is_on = 'ON' in power_state.upper()
        results.append(('power_state', is_on))
        print(f"   判断: {'亮屏' if is_on else '熄屏'}")
    
    # 方法4: 检查当前Activity
    print("\n4. 检查当前前台Activity:")
    activity = run_command('adb shell dumpsys activity | grep "mResumedActivity"')
    print(f"   结果: {activity}")
    if activity:
        is_on = 'null' not in activity.lower() and activity.strip() != ''
        results.append(('activity', is_on))
        print(f"   判断: {'亮屏' if is_on else '熄屏'}")
    
    # 方法5: 尝试截图
    print("\n5. 尝试截图测试:")
    screenshot_cmd = 'adb shell screencap -p /sdcard/screen_test.png'
    screenshot_result = run_command(screenshot_cmd)
    print(f"   截图命令结果: {screenshot_result}")
    
    # 检查截图文件是否生成
    file_check = run_command('adb shell ls /sdcard/screen_test.png 2>/dev/null')
    print(f"   文件检查: {file_check}")
    
    if 'screen_test.png' in file_check:
        results.append(('screenshot', True))
        print("   判断: 亮屏 (截图成功)")
        # 清理文件
        run_command('adb shell rm /sdcard/screen_test.png')
    else:
        results.append(('screenshot', False))
        print("   判断: 熄屏 (截图失败)")
    
    # 方法6: 检查屏幕亮度
    print("\n6. 检查屏幕亮度:")
    brightness = run_command('adb shell settings get system screen_brightness')
    print(f"   亮度值: {brightness}")
    if brightness and brightness.isdigit():
        is_on = int(brightness) > 0
        results.append(('brightness', is_on))
        print(f"   判断: {'亮屏' if is_on else '熄屏'}")
    
    # 方法7: 检查窗口管理器
    print("\n7. 检查窗口管理器:")
    window_state = run_command('adb shell dumpsys window | grep -i "screen"')
    print(f"   结果: {window_state}")
    
    # 方法8: 检查输入管理器
    print("\n8. 检查输入管理器状态:")
    input_state = run_command('adb shell dumpsys input | grep -i "screen"')
    print(f"   结果: {input_state}")
    
    # 综合判断
    print("\n" + "=" * 50)
    print("综合判断结果:")
    print("=" * 50)
    
    if results:
        on_count = sum(1 for _, is_on in results if is_on)
        total_count = len(results)
        
        print(f"有效检测方法数: {total_count}")
        print(f"认为亮屏的方法数: {on_count}")
        print(f"认为熄屏的方法数: {total_count - on_count}")
        
        for method, is_on in results:
            status = "✅ 亮屏" if is_on else "❌ 熄屏"
            print(f"  {method}: {status}")
        
        # 最终判断
        final_result = on_count > total_count / 2
        confidence = (on_count / total_count) * 100 if total_count > 0 else 0
        
        print(f"\n🎯 最终判断: {'亮屏' if final_result else '熄屏'}")
        print(f"📊 置信度: {confidence:.1f}%")
        
        return final_result
    else:
        print("❌ 所有检测方法都失败了")
        return None

def simple_screen_check():
    """简化的屏幕检测"""
    print("\n" + "=" * 30)
    print("简化检测 (推荐方法)")
    print("=" * 30)
    
    # 使用截图方法作为主要检测手段
    print("使用截图方法检测...")
    screenshot_result = run_command('adb shell screencap -p /sdcard/quick_test.png 2>&1')
    file_check = run_command('adb shell ls /sdcard/quick_test.png 2>/dev/null')
    
    if 'quick_test.png' in file_check:
        print("✅ 截图成功 -> 屏幕是亮的")
        run_command('adb shell rm /sdcard/quick_test.png')
        return True
    else:
        print("❌ 截图失败 -> 屏幕可能是熄灭的")
        return False

if __name__ == "__main__":
    # 检查设备连接
    devices = run_command('adb devices')
    if "4C04902902004A0B4" not in devices:
        print("❌ TCL电视设备未连接")
        print("当前连接的设备:")
        print(devices)
        exit(1)
    
    print("✅ TCL电视设备连接正常")
    
    # 执行详细检测
    result = check_screen_methods()
    
    # 执行简化检测
    simple_result = simple_screen_check()
    
    print("\n" + "=" * 50)
    print("最终建议:")
    if simple_result:
        print("🌟 建议使用截图方法检测屏幕状态")
        print("💡 命令: adb shell screencap -p /sdcard/test.png && adb shell ls /sdcard/test.png")
    else:
        print("🔍 需要进一步调试，可能需要其他检测方法")
