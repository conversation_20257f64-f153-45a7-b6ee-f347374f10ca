# -*- coding:utf-8 -*-
import time,os,sys,datetime
from subprocess import call, PIPE, Popen, STDOUT
import socket
from tvadb.tv_adb import TvAdb
tv = TvAdb()    # 调用tv_common库执行adb命令
WORK_FOLDER = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))    # 引用自己的模块
sys.path.append(WORK_FOLDER)

class Basic_ADB():
    """
    与adb有关的方法,大部分与basic_methods.py中的class Basic重合
    """
    def __init__(self,device_id):
        self.device_id = device_id
        self.adb_connect()   # 先connect上电视

    def adb_connect(self):
        __connect = Popen("adb connect %s " % self.device_id, shell=True, stdout=PIPE, stderr=PIPE)
        if b"connected to" in __connect.stdout.read():
            return True
        else:
            print(__connect.stderr.read().strip())

    def adb_disconnect(self):
        call("adb disconnect %s" % self.device_id, shell=True, stdout=PIPE, stderr=PIPE)

    def adb_diconnect_offline(self):  # disconnect offline
        print("adb disconnect offline")
        call("adb disconnect offline",shell=True, stdout=PIPE, stderr=PIPE)

    def check_device(self,timeout=5):
        cmd = "adb -s {} shell 'ls -d'".format(self.device_id)
        state1 = self.timeout_command(cmd, timeout=timeout)
        print("check device state:", state1)
        if state1 == '.':
            return True
        else:
            return False

    def quick_connect(self,timeout=5):
        """
        从adb状态，判断电视是否发生断连、是否回链
        为了防止adb断连后hang住，每条指令只执行3秒，超时不等
        adb连接：return True
        adb断连：return False
        """
        cmd = "adb -s {} shell 'ls -d'".format(self.device_id)
        state1 = self.timeout_command(cmd,timeout=timeout)
        print("quick_connect state1:", state1)
        if state1 == '.':
            return True
        else:
            Popen("adb disconnect {}".format(self.device_id), shell=True, stdout=PIPE, stderr=STDOUT)
            time.sleep(3)
            Popen("adb connect {}".format(self.device_id), shell=True, stdout=PIPE, stderr=STDOUT).stdout.read().decode()
            time.sleep(3)
            cmd = "adb -s {} shell 'ls -d'".format(self.device_id)
            state2 = self.timeout_command(cmd,timeout=timeout)
            print("quick_connect state2:", state2)
            return True if state2 == "." else False

    def check_root(self):
        """
        检查是否root了，root会导致ota升级失败
        """
        print("check root ...")
        check_root = self.send_adb_command("shell 'id'", output=True)
        try:
            if isinstance(check_root,str) and "root" in check_root:
                print("root already.")
                return True
        except Exception:
            return False

    def adb_root(self):
        """adb root权限"""
        if self.check_root():
            return True
        print("try root device")
        for i in range(3):
            print("try to root times:{}".format(i))
            self.adb_connect()
            root_command = self.send_adb_command("root", output=True)
            if isinstance(root_command,str) and "adbd cannot run as root in production builds" in root_command:
                print("adbd cannot run as root in production builds")
                return
            time.sleep(4)
            check_root = self.send_adb_command("shell 'id'", output=True)
            try:
                if isinstance(check_root,str) and "root" in check_root:
                    print("root successfully.")
                    return True
                # todo 如果是其他情况都可以执行adb disconnect offline
                else:
                # if "offline" in check_root or "close" in check_root:
                    # todo disconnect offline
                    self.adb_diconnect_offline()
                    time.sleep(5)
            except Exception:
                pass
            time.sleep(5)
        print("try to root 3 times but still fail")
    def adb_remount(self):
        """adb amount 权限"""
        self.send_adb_command("shell 'remount'")

    def ping_ip(self,ip="", timeout=600):
        """
        ping 设备网络
        """
        print("start ping ip : {}".format(ip))
        ip = ip.split(":")[0]
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        start_time = time.time()
        while (time.time() - start_time) < timeout:
            try:
                s.connect((ip, 5555))
                time.sleep(1)
                s.shutdown(2)
                time.sleep(0.2)
                s.close()
                time.sleep(2)
                print("net device %s connected" % ip)
                return True
            except:
                time.sleep(3)
                continue
        else:
            print("wait %s seconds but can not connect to ip %s" % (timeout, ip))
            return False

    def ping_ip2(self,ip, timeout=300):
        print("start timeout 3 ping ip : {}".format(ip))
        start_time = time.time()
        while (time.time() - start_time) < timeout:
            cmd = "ping {}".format(ip)
            output = self.timeout_command(cmd,timeout=3)
            if len(output) >= 100:
                print("ping {} success.".format(ip))
                return True
        else:
            print("wait %s seconds but can not connect to ip %s" % (timeout, ip))
            return False

    def send_adb_command(self, command, timeout = (60*5), output=False):
        """
        执行adb命令，并获取返回值，一定会等待该命令执行结束
        注意：adb的命令不一定带关键字 shell
        如过需要打印返回值到log中，output传入参数为True
        """
        # p = Popen("adb -s {} {}".format(self.device_id, command), shell=True, stdout=PIPE, stderr=STDOUT).stdout.read()
        # if output:
        #     print("adb command [ %s ], result[ %s ]" % (command, p.decode().strip('\r\n')))
        # # return p.decode().strip('\r\n')

        adb_cmd = "adb -s {} {}".format(self.device_id, command)
        out = tv.send_adb_command(adb_cmd,timeout=timeout,output=output)
        return out

    def timeout_command(self,command,timeout=5,output=False):
        """
        parameter:command 输入的完整的命令
        执行adb命令或其他命令，并在timeout时间内获取返回值
        如果timeout超时，则kill掉该命令
        注意：adb的命令不一定带关键字 shell
        如过需要打印返回值到log中，output传入参数为True
        """
        try:
            # out = Popen(command, shell=True, stdout=PIPE, stderr=PIPE)
            # out.wait(timeout)  # 可能会因为网络问题安装apk超时
            # p = out.stdout.read().decode().rstrip()
            p = tv.send_adb_command(command,timeout = timeout,output=output)
        except Exception as e:
            print("adb command [ {} ] timeout.".format(command))
            return None
        if output:
            print("adb command [ {} ], result[ {} ]".format(command, p))
        return p

    def android_version(self):
        """
        读取tv的安卓版本
        """
        value = self.send_adb_command("shell 'getprop ro.build.version.release'",output=True)
        if isinstance(value, bool):
            return False
        return int(str(value).strip().split(".")[0])

    def get_product_name(self):
        """
        读取tv的product name
        """
        value = self.send_adb_command("shell 'getprop ro.build.product'",output=True)
        print("check get product name:",value)
        if isinstance(value, bool):
            return "unknown_product_name"
        return value.strip().split()[0]    # 如果有空格，应该是读设备出错了，为了避免后续使用该字段出错，把空格去掉

    def get_build_version(self):
        """
        读取tv的软件版本
        """
        build_software_version = self.send_adb_command("shell 'getprop ro.build.software.version'",output=True)   # 海外
        build_display_id = self.send_adb_command("shell 'getprop ro.build.display.id'",output=True)               # 国内
        build_version_incremental = self.send_adb_command("shell 'getprop ro.build.version.incremental'",output=True)    # 所有项目都适用
        if isinstance(build_software_version,str) and len(build_software_version.split(".")) > 3:
            build_version = build_software_version
        elif isinstance(build_display_id,str) and " " not in build_display_id :    # 不包含空格
            build_version = build_display_id
        else:
            build_version = build_version_incremental   # 根据梁大顺说是安卓自带的字段，每个设备都一定会有这个字段
        return build_version

    def get_build_date(self):
        """
        ro.build.date
        :return:
        """
        build_date = self.send_adb_command("shell 'getprop ro.build.date'",output=True)
        if isinstance(build_date, bool):
            return False
        return build_date

    def get_screencap(self,log_path):
        """
        screncap截图，保存在当前log目录
        """
        print("get screencap")
        self.send_adb_command("shell 'screencap -p /sdcard/app.png'")
        temp_time = time.strftime("%Y%m%d-%H%M%S")
        self.adb_root()
        self.send_adb_command("pull /sdcard/app.png {}/screencap_{}.png".format(log_path, temp_time),output=True)

    def install_tvqs(self):
        """
        check tvqs这个包在不在电视上
        """
        # todo check电视上是否有这个app   # winodows系统不适配  # 这个check有毛病，明明已经卸载了，但是grep还在电视上
        for i in range(3):
            tvqs_exist = self.send_adb_command("shell 'pm list packages | grep com.xiaomi.tvqs'")
            if isinstance(tvqs_exist,str) and "com.xiaomi.tvqs" not in tvqs_exist:
                self.install_apk("apks/TvQuaSys.apk")      # 安装这个apk
            else:
                return True
        else:
            print("try to install tvqs 3 times,but still fail")
            return False

    def install_apk(self,apk_path,timeout=300):
        """
        安装单个apk
        :param apk_path: apk的路径
        :return:
        """
        print("install ",apk_path)
        cmd = "adb -s {} install -r -d -t {} &".format(self.device_id,os.path.join(apk_path))
        try:
            out = Popen(cmd,shell=True, stdout=PIPE, stderr=PIPE)
            out.wait(timeout)     # 可能会因为网络问题安装apk超时
            print(out.stdout.read())
        except Exception as e:
            print("install {} timeout.".format(apk_path))

    def get_app_version(self,targetPkg):
        app_exist = tv.send_adb_command("adb -s {} shell 'pm list package {}'".format(self.device_id,targetPkg))
        if app_exist:
            try:
                app_version = tv.send_adb_command("adb -s {} shell \"dumpsys package {} |grep versionName |head -1| sed -e 's/^[ ]*//g'\"".format(self.device_id,targetPkg))
                if "versionName" in app_version:
                    app_version = app_version.split("=")[1]
                    print("[{}] version is [{}]".format(targetPkg,app_version))
                    return app_version
                else:
                    print("[{}] version is not exist".format(targetPkg))
                    return None
            except Exception as e:
                print("[{}] version is not exist".format(targetPkg))
                return None
        else:
            print("[{}] is not exist".format(targetPkg))
            return None

    def judge_inet(self):
        """
        判断电视使用的是有线网络or无线网络
        Returns:True：有线；False：WiFi
        """
        ifconfig_res = tv.send_adb_command("adb -s {} shell ip addr | grep 'scope global wlan'".format(self.device_id),output=True)
        if isinstance(ifconfig_res,str) and "scope global wlan" in ifconfig_res:    # 无线网络
            return False
        else:
            return True

    def kill_monkey(self):
        """
        kill掉电视上已有的monkey进程
        :return:
        """
        self.send_adb_command("shell 'pkill monkey'")

    def kill_uiautomator(self):
        """
        kill掉电视上的uiautomator进程
        :return:
        """
        self.send_adb_command("shell 'pkill uiautomator'")


    def save_BugReport(self,log_path):
        """
        sava bug report
        :return:
        """
        now_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.send_adb_command("bugreport {}/bugreport_{}.zip".format(log_path,now_time))

    def PullLog(self,log_path):
        """
        pull电视上的log
        :return:
        """
        print("******************************** Pull Logs... *******************************")
        self.send_adb_command("pull /data/system/dropbox {}".format(log_path))
        self.send_adb_command("pull /data/tombstones {}".format(log_path))
        self.send_adb_command("pull /data/anr {}".format(log_path))

    def adb_reboot(self,timeout=300):
        """
        重启电视，等待adb恢复
        设置5min超时
        :return:
        """
        check_adb = self.quick_connect()
        if check_adb == False:
            print("cannot adb connect tv.....")
        for i in range(3):
            self.send_adb_command("reboot")
            stamp = time.time()
            while time.time() - stamp < 60:
                check_adb = self.check_device()
                print("check reboot adb:",check_adb)
                if check_adb == False:
                    print("reboot start")
                    reboot_time = time.time()
                    while time.time() - reboot_time < timeout:
                        check_adb = self.quick_connect()
                        if check_adb:
                            print("reboot finish")
                            return True
                    else:
                        print("reboot start,but tv can not get adb back in 5 min")
                        return False
        else:
            print("fail to send reboot cmd ")
            return False


    def Disable_Verity(self):
        grep_Verity=self.send_adb_command("disable-verity|grep -c Verity",output=True)
        if grep_Verity == 2:
            self.adb_reboot()
            self.adb_root()
            self.adb_remount()

    def get_Pid(self):
        """
        读取system server pid
        :return:
        """
        ps_state = self.send_adb_command("shell \"ps|grep system_server\"", output=True)  # 通用
        ps_state = ps_state.split(" ")
        pid_state = [p_i for p_i in ps_state if p_i != ""]   # 去掉空格符1
        return pid_state[1]

    def get_uptime(self):
        """
        读取uptime
        :return:
        """
        uptime = self.send_adb_command("shell 'cat /proc/uptime'",output=True)
        uptime = uptime.split(".")[0]
        return uptime

    def set_tvhome_test_env(self):
        """
        打开accessibility service，才能定位桌面的ui控件
        """
        print("set accessibility service")
        self.send_adb_command("shell 'setprop tvhome.test_env true'".format(self.device_id))
        time.sleep(2)
        self.send_adb_command("shell 'pkill com.mitv.tvhome'".format(self.device_id))
        time.sleep(2)

    def domestic_or_overseas(self):
        """
        判断是国内项目还是海外项目
        通过是否有appstore这个app来判断
        :return:
        """
        appstore = self.send_adb_command("shell 'pm list package | grep com.xiaomi.mitv.appstore'", output=True)
        if isinstance(appstore,str) and appstore:
            print("domestic project")
            return "domestic"
        else:
            print("overseas project")
            return "overseas"

    def gtv_or_atv(self):
        """
        判断海外的电视是atv还是gtv
        Returns:

        """
        gtv_res = self.send_adb_command("shell getprop | grep vendor.mitv.gtv",output = True)
        if isinstance(gtv_res,str) and gtv_res:
            if "true" in gtv_res:
                return "GTV"
            else:
                return "ATV"
        else:    # 读不到这个字段，也是ATV
            return "ATV"

    def tvqs_screeencap(self,screenshot_2=10,interation=1,delay_start=3,interval_time=60,rename_keyword="screencap"):
        """
        截图，每组截图后会根据截图时间戳重命名为，一组截图为2张，原始图像命名为capture_x.jpg & capture_y.jpg,重命名后为screencap_temptime-1.jpg & screencap_temptime-2.jpg
        Args:
            screenshot_2: 组内截图的时间间隔时长，默认为10秒
            interation: 截图的组数，默认为1组
            delay_start: 调用本截图方法开始截图前是否需要等待，默认为等待3秒，如果不需要等待，可以写0
            interval_time: 每组截图之间的间隔，默认为60秒。只在每组截图之间等待，最后一组截图后直接返回
            rename_keyword: 重命名的名称，默认为sceencap
        Returns:
        """
        time.sleep(delay_start)
        for i in range(interation):
            print("tvqs sceencap times:{}".format(i+1))
            self.send_adb_command('shell am broadcast -a mitv.action.debug.capture --es cmd "capture"')     # 一组图为2张，时间间隔为screenshot_2
            time.sleep(screenshot_2)  # 两张图之间的间隔时间
            self.send_adb_command('shell am broadcast -a mitv.action.debug.capture --es cmd "capture"')
            time.sleep(5)
            # todo 根据时间戳重命名
            capture_path = "storage/emulated/0/Android/data/com.xiaomi.tvqs/files"    # 截图完成之后保存的位置，自动命名为capture_x.jpg
            temp_time = time.strftime("%Y%m%d-%H%M%S")     # 根据时间戳命名
            index = 1

            tvqs_files = str(self.send_adb_command("shell ls {}".format(capture_path))).splitlines()
            for capture in tvqs_files:
                if capture.startswith("capture") and (capture.endswith(".jpg") or capture.endswith(".png")):    # 如果是以capture开头命名的，则说明是本次截的图，重命名为screencap开头
                    print("capture:{}，rename to {}_{}-{}.jpg".format(capture,rename_keyword,temp_time,index))  # 黑屏被怀疑只截了一张图，查看两张图的原命名是否是同一张
                    self.send_adb_command("shell mv {}/{} {}/{}_{}-{}.jpg".format(capture_path,capture,capture_path,rename_keyword,temp_time, index))
                    index += 1
            if i+1 == interation:    # 最后一次截图就不等待了直接返回
                return
            else:
                time.sleep(interval_time)



if __name__ == '__main__':
    device_id = "10.189.146.7"
    basic_adb = Basic_ADB(device_id)
    # print(basic_adb.gtv_or_atv())
    basic_adb.adb_root()
    # basic_adb.tvqs_screeencap(screenshot_2=5,interation=3,delay_start=0,interval_time=30,rename_keyword="mitv")
    # product_name = basic_adb.get_product_name()
    # print("product_name:",product_name)
    # print(basic_adb.adb_reboot())
    # print(time.ctime())
    # tv = TvAdb()
    # getprop_info = tv.send_adb_command("adb -s {} shell getprop >> getprop.txt".format(device_id),output=True)
    # # getprop_info = basic_adb.send_adb_command("shell getprop", output=True)
    # print(type(getprop_info))
    # # getprop_info = getprop_info.split("\n")
    # print("return",getprop_info)
    # tv_state = tv.reconnect(device_id=device_id,timeout=100)
    # print("tv_state:",tv_state)
    # if tv_state:
    #     print("yesssss")
    # if not tv_state:
    #     print("nosnsosno")

    # basic_adb.quick_connect()
    # appstore_exist = basic_adb.send_adb_command("shell 'pm list package | grep com.xiaomi.mitv.appstore'", output=True)
    # if appstore_exist :
    #     print("yessss")
    # app_3 = basic_adb.send_adb_command("shell 'pm list package -3'")
    # print(app_3)
    # print(basic_adb.get_build_version())
    # basic_adb.adb_root()
    # basic_adb.adb_connect()
    # basic_adb.get_Pid()
    # basic_adb.get_uptime()
    # basic_adb.send_adb_command('shell am broadcast -a mitv.action.debug.capture --es cmd "capture"',output=True)
    # basic_adb.timeout_command("adb connect 10.221.92.155",timeout=30,output=True)
    # targetPkg = "com.mitv.tvhome"
    # app_version = basic_adb.send_adb_command(
    # "shell \"dumpsys package {} |grep versionName |head -1| sed -e 's/^[ ]*//g'\"".format(targetPkg),output=True)
    # basic_adb.send_adb_command('shell date +%s',output=True)
    # basic_adb.send_adb_command("shell \"dumpsys dropbox --print | grep anr\"",output=True)
    # basic_adb.adb_root()
    # basic_adb.send_adb_command("shell \"ls data/tombstones/ | wc -l\"",output=True)

    # line = "script/testcases/systemapp.py:SystemAppTest.testApplicationswitch"
    # cmd = "nosetests --tests={} --tc-file config.ini --verbosity=2".format(line)
    # basic_adb.timeout_command(cmd,timeout=600,output=True)

    # print(basic_adb.quick_connect())
    # cmd = "adb -s {} shell ls -d".format(device_id)
    # basic_adb.timeout_command(cmd,output=True)
    # apk_path = "apks"
    # for apk in os.listdir(apk_path):
    #     # todo 先判断这个包在电视里有没有
    #     apk_name = apk.split(".apk")[0]
    #     print(apk_name)
    #     apk_exist = basic_adb.send_adb_command("shell pm list package | grep {}".format(apk_name), output=True)
    #     print(apk_exist)
    #     if apk_exist:
    #         print("exist")
    #     else:
    #         print("notttt")