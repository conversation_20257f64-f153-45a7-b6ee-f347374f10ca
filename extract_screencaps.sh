#!/bin/bash

# 提取所有screencaps文件夹中的文件到当前目录
# 使用方法: ./extract_screencaps.sh

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 当前目录
CURRENT_DIR=$(pwd)
EXTRACT_DIR="$CURRENT_DIR/extracted_screencaps"

# 创建提取目录
mkdir -p "$EXTRACT_DIR"

echo -e "${BLUE}=== 开始提取screencaps文件 ===${NC}"
echo -e "${YELLOW}当前目录: $CURRENT_DIR${NC}"
echo -e "${YELLOW}提取到目录: $EXTRACT_DIR${NC}"
echo ""

# 计数器
total_files=0
total_folders=0
folder_counter=1

# 查找所有screencaps文件夹
echo -e "${BLUE}正在搜索screencaps文件夹...${NC}"

# 使用find命令查找所有名为screencaps的文件夹
while IFS= read -r -d '' screencaps_dir; do
    if [ -d "$screencaps_dir" ]; then
        total_folders=$((total_folders + 1))
        echo -e "${GREEN}找到文件夹 $total_folders: $screencaps_dir${NC}"
        
        # 检查文件夹是否为空
        if [ -z "$(ls -A "$screencaps_dir")" ]; then
            echo -e "${YELLOW}  -> 文件夹为空，跳过${NC}"
            continue
        fi
        
        # 计算该文件夹中的文件数量
        file_count=$(find "$screencaps_dir" -type f | wc -l)
        echo -e "${BLUE}  -> 包含 $file_count 个文件${NC}"
        
        # 如果只有一个screencaps文件夹，直接使用screencaps作为前缀
        if [ $total_folders -eq 1 ] && [ $(find "$CURRENT_DIR" -name "screencaps" -type d | wc -l) -eq 1 ]; then
            prefix="screencaps"
        else
            prefix="screencaps${folder_counter}"
            folder_counter=$((folder_counter + 1))
        fi
        
        # 复制文件并重命名
        file_counter=1
        while IFS= read -r -d '' file; do
            if [ -f "$file" ]; then
                # 获取文件扩展名
                filename=$(basename "$file")
                extension="${filename##*.}"
                
                # 如果没有扩展名，使用原文件名
                if [ "$extension" = "$filename" ]; then
                    new_filename="${prefix}_${file_counter}_${filename}"
                else
                    new_filename="${prefix}_${file_counter}.${extension}"
                fi
                
                # 复制文件
                cp "$file" "$EXTRACT_DIR/$new_filename"
                
                if [ $? -eq 0 ]; then
                    echo -e "  ${GREEN}✓${NC} $filename -> $new_filename"
                    total_files=$((total_files + 1))
                    file_counter=$((file_counter + 1))
                else
                    echo -e "  ${RED}✗${NC} 复制失败: $filename"
                fi
            fi
        done < <(find "$screencaps_dir" -type f -print0)
        
        echo ""
    fi
done < <(find "$CURRENT_DIR" -name "screencaps" -type d -print0)

# 输出统计信息
echo -e "${BLUE}=== 提取完成 ===${NC}"
echo -e "${GREEN}总共找到 $total_folders 个screencaps文件夹${NC}"
echo -e "${GREEN}总共提取 $total_files 个文件${NC}"
echo -e "${YELLOW}所有文件已保存到: $EXTRACT_DIR${NC}"

# 显示提取目录的内容
if [ $total_files -gt 0 ]; then
    echo ""
    echo -e "${BLUE}提取的文件列表:${NC}"
    ls -la "$EXTRACT_DIR" | grep -v "^total" | while read line; do
        echo -e "  ${GREEN}$line${NC}"
    done
    
    # 按文件类型统计
    echo ""
    echo -e "${BLUE}文件类型统计:${NC}"
    cd "$EXTRACT_DIR"
    for ext in $(find . -name "*.*" -type f | sed 's/.*\.//' | sort | uniq); do
        count=$(find . -name "*.${ext}" -type f | wc -l)
        echo -e "  ${YELLOW}.$ext 文件: $count 个${NC}"
    done
    cd "$CURRENT_DIR"
else
    echo -e "${RED}没有找到任何文件！${NC}"
fi

echo ""
echo -e "${GREEN}脚本执行完成！${NC}"
