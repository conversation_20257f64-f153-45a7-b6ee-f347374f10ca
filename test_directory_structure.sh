#!/bin/bash

# 创建测试目录结构的脚本
# 用于演示extract_screencaps_folders.sh的效果

echo "创建测试目录结构..."

# 清理可能存在的测试目录
rm -rf test_structure extracted_screencaps

# 创建测试目录结构
mkdir -p test_structure

# 创建不同的测试场景
echo "创建测试场景1: test1/screencaps/"
mkdir -p test_structure/test1/screencaps
echo "测试图片1" > test_structure/test1/screencaps/image1.png
echo "测试图片2" > test_structure/test1/screencaps/image2.jpg
mkdir -p test_structure/test1/screencaps/subfolder
echo "子文件夹图片" > test_structure/test1/screencaps/subfolder/image3.png

echo "创建测试场景2: test2/screencaps/"
mkdir -p test_structure/test2/screencaps
echo "测试视频1" > test_structure/test2/screencaps/video1.mp4
echo "测试截图" > test_structure/test2/screencaps/screenshot.png

echo "创建测试场景3: project_a/screencaps/"
mkdir -p test_structure/project_a/screencaps
echo "项目截图1" > test_structure/project_a/screencaps/proj_img1.png
echo "项目截图2" > test_structure/project_a/screencaps/proj_img2.png

echo "创建测试场景4: 嵌套结构 nested/deep/test3/screencaps/"
mkdir -p test_structure/nested/deep/test3/screencaps
echo "深层截图" > test_structure/nested/deep/test3/screencaps/deep_image.png

echo "创建测试场景5: 重名测试 test1_duplicate/screencaps/"
mkdir -p test_structure/another_path/test1/screencaps
echo "重名测试图片" > test_structure/another_path/test1/screencaps/duplicate.png

echo ""
echo "测试目录结构创建完成！"
echo ""
echo "当前目录结构："
tree test_structure 2>/dev/null || find test_structure -type d | sed 's|[^/]*/|  |g'

echo ""
echo "现在运行提取脚本..."
echo "cd test_structure && ../extract_screencaps_folders.sh"

echo ""
echo "预期结果结构："
echo "extracted_screencaps/"
echo "├── test1/"
echo "│   └── screencaps/"
echo "│       ├── image1.png"
echo "│       ├── image2.jpg"
echo "│       └── subfolder/"
echo "│           └── image3.png"
echo "├── test2/"
echo "│   └── screencaps/"
echo "│       ├── video1.mp4"
echo "│       └── screenshot.png"
echo "├── project_a/"
echo "│   └── screencaps/"
echo "│       ├── proj_img1.png"
echo "│       └── proj_img2.png"
echo "├── test3/"
echo "│   └── screencaps/"
echo "│       └── deep_image.png"
echo "└── test1_2/"
echo "    └── screencaps/"
echo "        └── duplicate.png"

echo ""
echo "要测试脚本，请运行："
echo "cd test_structure"
echo "../extract_screencaps_folders.sh -d  # 预览模式"
echo "../extract_screencaps_folders.sh     # 实际执行"
