# -*- coding:utf-8 -*-

class Get_Caselist():
    def __init__(self,testtype):
        self.testtype = testtype
        self.testtype_tool = {
            1: "monkey",
            2: "stability",   # 国内稳定性测试
            14: "miplayer",  # 小米播放器测试
            17: "HDMI-stability",  # 信号源稳定性测试
            18: "Display-stability",  # display 稳定性测试
            19:"connectivity",   # connectivity 稳定性测试
            20:"stability-overseas",  # 海外稳定性测试
            21:"LiveTV",   # LiveTV 海外
            22:"smartshare",  # 投屏专项测试
            23: "photonengine",  # 光子引擎测试
            24: "SpeedUI",  # speed UI 测试
        }

    def mapping_testtype(self,casetype):
        self.casetype = casetype
        print("mapping case type:{}".format(self.casetype))
        if self.testtype == 2:
            print("=========================================================\n")
            print("                   国内稳定性测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.sta_caselist(self.casetype)
        elif self.testtype == 14:
            print("=========================================================\n")
            print("                   小米播放器测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.miplayercaselist(self.casetype)
        elif self.testtype == 17:    #
            print("=========================================================\n")
            print("                   信号源稳定性测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.hdmi_sta_caselist(self.casetype)
        elif self.testtype == 18:    #
            print("=========================================================\n")
            print("                   Display稳定性测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.display_sta_caselist(self.casetype)
        elif self.testtype == 19:    #
            print("=========================================================\n")
            print("                   Connectivity稳定性测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.connectivity_sta_caselist(self.casetype)
        elif self.testtype == 20:
            print("=========================================================\n")
            print("                   海外稳定性测试(mtbf)\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.mtbf_caselist(self.casetype)
        elif self.testtype == 21:
            print("=========================================================\n")
            print("                   海外LitvTV测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.LiveTV_caselist(self.casetype)
        elif self.testtype == 22:
            print("=========================================================\n")
            print("                   投屏专项测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.smartshare_caselist(self.casetype)
        elif self.testtype == 23:
            print("=========================================================\n")
            print("                   光子引擎测试\n")
            print("=========================================================\n")
            test_categorie,test_chname,caselist,install_apks = self.photonengine_caselist(self.casetype)
        elif self.testtype == 24:
            print("=========================================================\n")
            print("                    speed UI 测试\n")
            print("=========================================================\n")
            test_categorie, test_chname, caselist, install_apks = self.speedui_caselist(self.casetype)
        else:
            raise ValueError("无法匹配测试类型，测试退出！")

        return test_categorie,test_chname,caselist,install_apks

    def speedui_caselist(self,casetype):
        test_categorie = "speedui"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   SpeedUI测试\n")
            print("=========================================================\n")
            test_chname = "SpeedUI测试"
            caselist = "config/SpeedUI/speedui_caselist.txt"
            install_apks = "config/SpeedUI/speedui_apks.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def connectivity_sta_caselist(self,casetype):
        test_categorie = "connectivity"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   chromecast 投屏长播12小时测试\n")
            print("=========================================================\n")
            test_chname = "chromecast 投屏长播12小时测试"
            caselist = "config/connectivity/overseas_chromecast_play_caselist.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   Miracast 投屏长播 12小时测试\n")
            print("=========================================================\n")
            test_chname = "Miracast 投屏长播 12小时测试"
            caselist = "config/connectivity/overseas_miracast_play_caselist.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def mtbf_caselist(self,casetype):
        test_categorie = "stability-overseas"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   海外稳定性测试(GTV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试"
            caselist = "config/stability_overseas/mtbf_caselist.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV)"
            caselist = "config/stability_overseas/dangalP_stability_caselist.txt"
        elif casetype == 2:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV)"
            caselist = "config/stability_overseas/Tarzan_stability_caselist.txt"
        elif casetype == 3:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV)"
            caselist = "config/stability_overseas/Hermano_eu_stability_caselist.txt"
        elif casetype == 4:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV)"
            caselist = "config/stability_overseas/Hermano_la_stability_caselist.txt"
        elif casetype == 5:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV)"
            caselist = "config/stability_overseas/Machuca_stability_caselist.txt"
        elif casetype == 6:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV)"
            caselist = "config/stability_overseas/Martian_stability_caselist.txt"
        elif casetype == 7:
            print("=========================================================\n")
            print("                   海外稳定性测试(GTV盒子)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(GTV盒子)"
            caselist = "config/stability/caselist_m25.txt"
        elif casetype == 8:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV盒子)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV盒子)"
            caselist = "config/stability/caselist_m19.txt"
        elif casetype == 9:
            print("=========================================================\n")
            print("                   海外稳定性测试(ATV盒子)\n")
            print("=========================================================\n")
            test_chname = "海外稳定性测试(ATV盒子)"
            caselist = "config/stability/caselist_m24.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def LiveTV_caselist(self,casetype):
        test_categorie = "LiveTV"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   进出LiveTV测试\n")
            print("=========================================================\n")
            test_chname = "进出LiveTV测试"
            caselist = "config/LiveTV/livetv_in_and_out_caselist.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   LiveTV长播测试\n")
            print("=========================================================\n")
            test_chname = "LiveTV长播测试"
            caselist = "config/LiveTV/livetv_play_caselist.txt"
        elif casetype == 2:
            print("=========================================================\n")
            print("                   LiveTV扫台切台测试\n")
            print("=========================================================\n")
            test_chname = "LiveTV扫台切台测试"
            caselist = "config/LiveTV/livetv_switch_caselist.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie, test_chname, caselist, install_apks

    def smartshare_caselist(self,casetype):
        test_categorie = "smartshare"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   DLAN投屏成功率测试\n")
            print("=========================================================\n")
            test_chname = "DLAN投屏成功率测试"
            caselist = "config/smartshare/DLNAshare_caselist_tencent.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   Miplay投屏测试\n")
            print("=========================================================\n")
            test_chname = "Miplay投屏测试"
            caselist = "config/smartshare/Miplayshare_caselist.txt"
        elif casetype == 2:
            print("=========================================================\n")
            print("                   miracast投屏测试\n")
            print("=========================================================\n")
            test_chname = "miracast投屏测试"
            caselist = "config/smartshare/Miracastshare_caselist.txt"
        elif casetype == 3:
            print("=========================================================\n")
            print("                   海外chromecast投屏长播测试\n")
            print("=========================================================\n")
            test_chname = "海外chromecast投屏长播测试"
            caselist = "config/connectivity/overseas_chromecast_play_caselist.txt"
        elif casetype == 4:
            print("=========================================================\n")
            print("                   海外Miracast投屏长播测试\n")
            print("=========================================================\n")
            test_chname = "海外Miracast投屏长播测试"
            caselist = "config/connectivity/overseas_miracast_play_caselist.txt"
        elif casetype == 5:
            print("=========================================================\n")
            print("                   华为投屏测试\n")
            print("=========================================================\n")
            test_chname = "华为投屏测试"
            caselist = "config/smartshare/DLNAshare_caselist_tool_huawei.txt"
        elif casetype == 6:
            print("=========================================================\n")
            print("                   TCL投屏测试\n")
            print("=========================================================\n")
            test_chname = "TCL投屏测试"
            caselist = "config/smartshare/DLNAshare_caselist_tool_TCL.txt"
        elif casetype == 7:
            print("=========================================================\n")
            print("                   Hisense投屏测试\n")
            print("=========================================================\n")
            test_chname = "Hisense投屏测试"
            caselist = "config/smartshare/DLNAshare_caselist_tool_Hisense.txt"
        elif casetype == 8:
            print("=========================================================\n")
            print("                   小米Miplay投屏成功率测试\n")
            print("=========================================================\n")
            test_chname = "小米Miplay投屏成功率测试"
            caselist = "config/smartshare/Miplayshare_caselist_xiaomi.txt"
        elif casetype == 9:
            print("=========================================================\n")
            print("                   12小时全局monkey测试\n")
            print("=========================================================\n")
            test_chname = "12小时全局monkey测试"
            caselist = "config/smartshare/monkeymira.txt"
        elif casetype == 10:
            print("=========================================================\n")
            print("                   小米miracast投屏成功率测试\n")
            print("=========================================================\n")
            test_chname = "小米miracast投屏成功率测试"
            caselist = "config/smartshare/Miracastshare_caselist_xiaomi.txt"
        elif casetype == 11:
            print("=========================================================\n")
            print("                   腾讯DLNA投屏测试\n")
            print("=========================================================\n")
            test_chname = "腾讯DLNA投屏测试"
            caselist = "config/smartshare/DLNAshare_caselist_tencent.txt"
        elif casetype == 12:
            print("=========================================================\n")
            print("                   Bilibili DLNA投屏测试\n")
            print("=========================================================\n")
            test_chname = "Bilibili DLNA投屏测试"
            caselist = "config/smartshare/DLNA_caselist_bilibili.txt"
        elif casetype == 13:
            print("=========================================================\n")
            print("                   小米DLNA投屏发现率测试\n")
            print("=========================================================\n")
            test_chname = "小米DLNA投屏发现率测试"
            caselist = "config/smartshare/DLNAshare_discovery_xiaomi.txt"
        elif casetype == 14:
            print("=========================================================\n")
            print("                   小米Miplay投屏发现率测试\n")
            print("=========================================================\n")
            test_chname = "小米Miplay投屏发现率测试"
            caselist = "config/smartshare/Miplayshare_discovery_caselist.txt"
        elif casetype == 15:
            print("=========================================================\n")
            print("                   小米Miracast投屏发现率测试\n")
            print("=========================================================\n")
            test_chname = "小米Miracast投屏发现率测试"
            caselist = "config/smartshare/Miracastshare_discovery_caselist.txt"
        elif casetype == 16:
            print("=========================================================\n")
            print("                   华为DLNA发现成功率\n")
            print("=========================================================\n")
            test_chname = "华为DLNA发现成功率"
            caselist = "config/smartshare/DLNAshare_discovery_huawei.txt"
        elif casetype == 17:
            print("=========================================================\n")
            print("                   TCLDLNA发现成功率\n")
            print("=========================================================\n")
            test_chname = "TCLDLNA发现成功率"
            caselist = "config/smartshare/DLNAshare_discovery_TCL.txt"
        elif casetype == 18:
            print("=========================================================\n")
            print("                   HisenseDLNA发现成功率\n")
            print("=========================================================\n")
            test_chname = "HisenseDLNA发现成功率"
            caselist = "config/smartshare/DLNAshare_discovery_Hisense.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def photonengine_caselist(self,casetype):
        test_categorie = "photonengine"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   photonengine testcase\n")
            print("=========================================================\n")
            test_chname = "光子引擎测试"
            caselist = "config/photonengine/photonengine_caselist.txt"
            install_apks = "config/photonengine/photonengine_apks.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def display_sta_caselist(self,casetype):
        test_categorie = "Display-stability"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   Global/Local dimming稳定性测试\n")
            print("=========================================================\n")
            test_chname = "Global/Local dimming稳定性测试"
            caselist = "config/Display-stability/global_dimming.txt"
            install_apks = "config/Display-stability/global_dimming_apk.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   息屏场景下背光和屏幕显示压测\n")
            print("=========================================================\n")
            test_chname = "息屏场景下背光和屏幕显示压测"
            caselist = "config/Display-stability/screen_off.txt"
        elif casetype == 2:
            print("=========================================================\n")
            print("                   各源下图像设置遍历测试\n")
            print("=========================================================\n")
            test_chname = "各源下图像设置遍历测试"
            caselist = "config/Display-stability/img_settings.txt"
            install_apks = "config/Display-stability/img_setting_apk.txt"
        elif casetype == 3:
            print("=========================================================\n")
            print("                   海外Global/Local dimming稳定性测试\n")
            print("=========================================================\n")
            test_chname = "海外Global/Local dimming稳定性测试"
            caselist = "config/Display-stability/overseas_global_dimming.txt"
        elif casetype == 4:
            print("=========================================================\n")
            print("                   海外各源下图像设置遍历测试\n")
            print("=========================================================\n")
            test_chname = "海外各源下图像设置遍历测试"
            caselist = "config/Display-stability/overseas_img_settings.txt"
        elif casetype == 5:
            print("=========================================================\n")
            print("                   盒子不同分辨率图像设置遍历测试\n")
            print("=========================================================\n")
            test_chname = "盒子不同分辨率图像设置遍历测试"
            caselist = "config/Display-stability/img_box_resolution.txt"
        elif casetype == 6:
            print("=========================================================\n")
            print("                   显示器息屏场景下背光和屏幕显示测试\n")
            print("=========================================================\n")
            test_chname = "显示器息屏场景下背光和屏幕显示测试"
            caselist = "config/Display-stability/OM2_screen_off.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def sta_caselist(self,casetype):
        """
        根据casetype，返回caselist和安装apklist（稳定性caselist映射关系）
        :param testtype: int
        :return:
        """
        test_categorie = "stability"
        install_apks = None     # 暂时还不知道国内稳定性需要安装哪些包
        if casetype == 0:
            print("=========================================================\n")
            print("                   国内标准模式测试\n")
            print("=========================================================\n")
            test_chname = "用户场景稳定性测试"
            caselist = "config/stability/caselist_m20.txt"
            install_apks = "config/stability/m20_apk.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   桌面超终端测试\n")
            print("=========================================================\n")
            test_chname= "桌面超终端稳定性测试"
            caselist = "config/stability/caselist_superpw.txtt"
        elif casetype == 2:
            print("=========================================================\n")
            print("                    企业模式\n")
            print("=========================================================\n")
            test_chname = "企业模式稳定性测试"
            caselist = "config/stability/caselist_e20.txt"
        elif casetype == 3:
            print("=========================================================\n")
            print("                    极简模式\n")
            print("=========================================================\n")
            test_chname = "极简模式稳定性测试"
            caselist = "config/stability/caselist_simple.txt"
        elif casetype == 4:
            print("=========================================================\n")
            print("                    电子水牌\n")
            print("=========================================================\n")
            test_chname = "电子水牌稳定性测试"
            caselist = "config/stability/caselist_Waterbrand.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def hdmi_sta_caselist(self,casetype):
        """
        根据casetype，返回caselist和安装apklist（hdmi稳定性caselist映射关系）
        :param testtype: int
        :return:
        """
        test_categorie = "HDMI-stability"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   同一通路信号源切换测试\n")
            print("=========================================================\n")
            test_chname = "同一通路信号源切换测试"
            caselist = "config/HDMI-stability/same_channel_caselist.txt"
            install_apks = "config/HDMI-stability/same_channel_pkgs.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   不同通路信号源切换和系统交互测试\n")
            print("=========================================================\n")
            test_chname = "不同通路信号源切换和系统交互测试"
            caselist = "config/HDMI-stability/diff_channel_caselist.txt"
            install_apks = "config/HDMI-stability/diff_channel_pkgs.txt"
        elif casetype == 2:
            print("=========================================================\n")
            print("      最高刷新率(如4K 144hz)HDMI源进出和系统交互压力测试\n")
            print("=========================================================\n")
            test_chname = "最高刷新率(如4K 144hz)HDMI源进出和系统交互压力测试"
            caselist = "config/HDMI-stability/max_refresh_caselist.txt"
            install_apks = "config/HDMI-stability/max_refresh_pkgs.txt"
        elif casetype == 3:
            print("=========================================================\n")
            print("      次高刷新率(如4K 120hz)HDMI源进出和系统交互压力测试\n")
            print("=========================================================\n")
            test_chname = "次高刷新率(如4K 120hz)HDMI源进出和系统交互压力测试"
            caselist = "config/HDMI-stability/second_refresh_caselist.txt"
            install_apks = "config/HDMI-stability/second_refresh_pkgs.txt"
        elif casetype == 4:
            print("=========================================================\n")
            print("                    HDMI源长时间播放测试\n")
            print("=========================================================\n")
            test_chname = "HDMI源长时间播放测试"
            caselist = "config/HDMI-stability/hdmi_play_caselist.txt"
            install_apks = "config/HDMI-stability/hdmi_play_pkgs.txt"
        elif casetype == 5:
            print("=========================================================\n")
            print("                    海外切源测试\n")
            print("=========================================================\n")
            test_chname = "海外切源测试"
            caselist = "config/HDMI-stability/overseas_switch_channel_caselist.txt"
        elif casetype == 6:
            print("=========================================================\n")
            print("                    海外HDMI源长播测试\n")
            print("=========================================================\n")
            test_chname = "海外HDMI源长播测试"
            caselist = "config/HDMI-stability/overseas_hdmi_play_caselist.txt"
        elif casetype == 7:
            print("=========================================================\n")
            print("                    海外AV源长播测试\n")
            print("=========================================================\n")
            test_chname = "海外AV源长播测试"
            caselist = "config/HDMI-stability/overseas_av_play_caselist.txt"
        elif casetype == 8:
            print("=========================================================\n")
            print("                   海外同一通路信号源切换测试\n")
            print("=========================================================\n")
            test_chname = "海外同一通路信号源切换测试"
            caselist = "config/HDMI-stability/overseas_same_channel_caselist.txt"
        elif casetype == 9:
            print("=========================================================\n")
            print("                   海外不同通路信号源切换和系统交互测试\n")
            print("=========================================================\n")
            test_chname = "海外不同通路信号源切换和系统交互测试"
            caselist = "config/HDMI-stability/overseas_diff_channel_caselist.txt"
        elif casetype == 10:
            print("=========================================================\n")
            print("      海外最高刷新率(如4K 144hz)HDMI源进出和系统交互压力测试\n")
            print("=========================================================\n")
            test_chname = "海外最高刷新率(如4K 144hz)HDMI源进出和系统交互压力测试"
            caselist = "config/HDMI-stability/overseas_max_refresh_caselist.txt"
        elif casetype == 11:
            print("=========================================================\n")
            print("      海外次高刷新率(如4K 120hz)HDMI源进出和系统交互压力测试\n")
            print("=========================================================\n")
            test_chname = "海外次高刷新率(如4K 120hz)HDMI源进出和系统交互压力测试"
            caselist = "config/HDMI-stability/overseas_second_refresh_caselist.txt"
        elif casetype == 12:
            print("=========================================================\n")
            print("      国内盒子视频长播测试\n")
            print("=========================================================\n")
            test_chname = "国内盒子视频长播测试"
            caselist = "config/HDMI-stability/darkknight_longvideo.txt"
        elif casetype == 13:
            print("=========================================================\n")
            print("      显示器DP源长播测试\n")
            print("=========================================================\n")
            test_chname = "显示器DP源长播测试"
            caselist = "config/HDMI-stability/dp_play_caselist.txt"
        elif casetype == 14:
            print("=========================================================\n")
            print("      不同通路信号源本地视频播放测试\n")
            print("=========================================================\n")
            test_chname = "不同通路信号源本地视频播放测试"
            caselist = "config/HDMI-stability/hdmi_localvideo.txt"

        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks

    def miplayercaselist(self,casetype):
        """
        根据casetype，返回caselist和安装apklist（小米播放器caselist映射关系）
        :param testtype: int
        :return:
        """
        test_categorie = "miplayer"
        install_apks = None
        if casetype == 0:
            print("=========================================================\n")
            print("                   各场景下音频稳定性测试\n")
            print("=========================================================\n")
            test_chname = "各场景下音频稳定性测试"
            caselist = "config/miplayer/audio_stability_caselist.txt"
            install_apks = "config/miplayer/audio_stability_pkgs.txt"
        elif casetype == 1:
            print("=========================================================\n")
            print("                   各场景下视频稳定性测试\n")
            print("=========================================================\n")
            test_chname = "各场景下视频稳定性测试"
            caselist = "config/miplayer/video_stability_caselist.txt"
            install_apks = "config/miplayer/video_stability_pkgs.txt"
        elif casetype == 2:
            print("=========================================================\n")
            print("                 TOP16三方视频应用用户场景测试\n")
            print("=========================================================\n")
            test_chname = "TOP16三方视频应用用户场景测试"
            caselist = "config/miplayer/top16_appstore_caselist.txt"
            install_apks = "config/miplayer/video_stability_pkgs.txt"

        elif casetype == 3:
            print("=========================================================\n")
            print("                 连接蓝牙音箱播放Youtube在线视频\n")
            print("=========================================================\n")
            test_chname = "连接蓝牙音箱播放Youtube在线视频"
            caselist = "config/miplayer/bluetooth_youtube_caselist.txt"
        elif casetype == 4:
            print("=========================================================\n")
            print("                 海外各场景下音频稳定性测试\n")
            print("=========================================================\n")
            test_chname = "海外各场景下音频稳定性测试"
            caselist = "config/miplayer/overseas_audio_stability_caselist.txt"
        elif casetype == 5:
            print("=========================================================\n")
            print("                 海外各场景下视频稳定性测试\n")
            print("=========================================================\n")
            test_chname = "海外各场景下视频稳定性测试"
            caselist = "config/miplayer/overseas_video_stability_caselist.txt"
        elif casetype == 6:
            print("=========================================================\n")
            print("                 视频起播、seek、退出压测\n")
            print("=========================================================\n")
            test_chname = "视频起播、seek、退出压测"
            caselist = "config/miplayer/random_video_caselist.txt"
            install_apks = "config/miplayer/video_stability_pkgs.txt"
        elif casetype == 7:
            print("=========================================================\n")
            print("                 视频与输入源交互压测\n")
            print("=========================================================\n")
            test_chname = "视频与输入源交互压测"
            caselist = "config/miplayer/random_source_caselist.txt"
            install_apks = "config/miplayer/video_stability_pkgs.txt"
        elif casetype == 8:
            print("=========================================================\n")
            print("                 AI人声增强调节压测\n")
            print("=========================================================\n")
            test_chname = "AI人声增强调节压测"
            caselist = "config/miplayer/AI_voice_caselist.txt"
            install_apks = "config/miplayer/video_stability_pkgs.txt"
        else:
            raise ValueError("无法匹配caselist，测试退出！")
        return test_categorie,test_chname,caselist,install_apks