#! /usr/bin/env python
# -*- coding:UTF-8 -*-
import json
import sys,os,time,datetime,re
import traceback
from dot_data.upload_to_ptf import UploadToPlatformField,UploadTestData
from tvadb.tv_adb import TvAdb,TvSysData

tool_version = "2024.7.15"
jenkins_sublink = "jenkins.tv.xiaomi.srv"

def add_log(logname,writeline,output = False):
    if output == True:
        # print(datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'),logname,writeline)
        print(logname,writeline)
    log_file = open(logname,"a+",encoding='utf-8')
    log_file.write(writeline)
    log_file.write("\n")
    log_file.close()

class UploadTestInfos():
    def __init__(self,device_id,testtype,result_folder,Address,build_url,build_user,run_mode,plan_hours = 0):
        self.device_id = device_id
        self.result_folder = result_folder
        self.address = Address
        self.build_url = build_url
        self.build_user = build_user
        self.run_mode = run_mode
        self.data_fields = UploadToPlatformField(self.device_id)
        self.upload_data_to_ptf = UploadTestData(self.run_mode)
        self.running = True
        self.upload_now = True     # 关键节点立刻上报
        self.tv = TvAdb()
        self.tv_sys_data = TvSysData()
        self.testtype = testtype
        self.plan_hours = plan_hours
        self.starttest_time = time.time()

        self.testtype_map = {
            1: "monkey",
            2: "stability",
            14: "miplayer",  # 小米播放器测试
            17: "HDMI-stability",  # 信号源稳定性测试
            18: "Display-stability",  # display 稳定性测试
            19: "connectivity",  # connectivity 稳定性测试
            20: "stability-overseas",  # 海外稳定性测试
            21: "LiveTV",  # LiveTV 海外
            22: "smartshare",  # 投屏专项测试
            23: "photonengine",  # 光子引擎测试
            24: "SpeedUI",  # speed UI 测试
        }

        self.start_test_info = {
            # "tool_name": self.testtype_tool[self.testtype],  # 测试工具名称 // 必填字段
            "tool_name": "miplayertest",  # 测试工具名称 // 必填字段 ,这个测试框架都是miplayertest
            "tool_version": tool_version,  # 测试工具版本 // 必填字段
            "test_type": self.testtype,  # [int] (14, '小米播放器测试')
            "device_ip": self.device_id,  # 设备ip地址
            "jenkins_build_number": None,  # // 必填字段
            "jenkins_job_name": None,  # // 必填字段
            "factory_name": self.address,
            "owner": self.build_user,  # 任务执行人
            "available_ram": 0,  # 剩余RAM
            "available_rom": 0,  # 剩余ROM
        }

        self.log_test_info = {
            "caselist_sum": 0,  # sum(caselist) * LOOP_I * LOOP_O
            "LOOP_O":0,   # 计划大循环
            "LOOP_I":0,   # 计划小循环
            "loop_o":0,    # 当前大循环
            "loop_i":0,    # 当前小循环
            "finish_case_sum": 0,    #已完成case个数
            "temp_case_name": "temp_case_name",
            "device_status":0,      # 电视设备adb状态:[(0:正常),(1:异常)]
            "available_ram": 0,  # 剩余RAM
            "available_rom": 0,  # 剩余ROM
            "schedule":1,       # 测试进度[(0:排队中)、(1:测试中)、(2:测试报告生成)、(3:测试完成)]
            "remaining_time": 0,  # 预计剩余时间，需要计算,单位秒(int)
        }

        self.result_test_info = {
            "available_ram": 0,     # 剩余RAM
            "available_rom": 0,     # 剩余ROM
            "plan_case_sum": 0,     # 计划执行的case总数
            "finish_case_sum": 0,   # 已执行完成case总数
            "case_exetime": 0,  # case执行时长
            "bugs_info": {},        # bugs的jira信息(dict)
            "summary_info": {},  # 测试结果总结
            "cases_info":{}         # 每个case的信息(dict)
        }

        self.test_id = None

    def uploadnow_start_info(self):
        """"开始压测时电视，上报数据"""
        print("上传开始数据")
        if jenkins_sublink in self.build_url:
            build_url_info = self.build_url.split("/job/")[1]
            build_url_info = build_url_info.split("/")
            self.start_test_info["jenkins_build_number"] = build_url_info[1]
            self.start_test_info["jenkins_job_name"] = build_url_info[0]
            print(self.start_test_info)
        try:
            if self.tv.reconnect(device_id=self.device_id,timeout=60):    # 电视状态正常
                self.start_test_info["available_ram"] = self.tv_sys_data.available_ram(self.device_id)  # 需要adb
                self.start_test_info["available_rom"] = self.tv_sys_data.available_rom(self.device_id)
                self.start_datas = self.data_fields.start_field(self.start_test_info)  # 补全start_datas
            else:   # 电视状态异常
                self.start_datas = self.data_fields.start_field(self.start_test_info,default=True)  # 补全start_datas

            upload_res = self.upload_data_to_ptf.start_data(self.start_datas)
            print("upload_start_info RES:", upload_res)
            self.test_id = upload_res["data"]["test_id"]
            print("test id:", self.test_id)
            return upload_res
        except Exception as e:
            print("fail to upload start datas")
            print(traceback.print_exc())  # 定位出错语句
            print(e)

    def uploadnow_result_info(self):
        """上报压测异常数据，用result data字段上报，上报异常数据可能会还没有生成result folder"""
        print("上传结果数据")
        # todo while break ,upload result datas
        if self.result_folder and os.path.isfile(os.path.join(self.result_folder,"report.html")):
            test_result_status = 1
            fds_Link = "http://cnbj1-fds.api.xiaomi.net/mitv-autotest/"
            report_url = os.path.join(fds_Link,self.testtype_map[self.testtype],self.result_folder,"report.html")
        else:
            test_result_status = 2      # 异常结束
            report_url = "None"

        if self.result_folder and os.path.isfile(os.path.join(self.result_folder,"Report_bugs.json")):
            with open(os.path.join(self.result_folder,"Report_bugs.json")) as bugs_file:
                bugsData = json.load(bugs_file)
                self.result_test_info["bugs_info"] = bugsData

        if self.result_folder and os.path.isfile(os.path.join(self.result_folder,"summaryReport.json")):
            with open(os.path.join(self.result_folder,"summaryReport.json"),"r") as s_file:
                summaryData = json.load(s_file)
                self.result_test_info["summary_info"] = summaryData     # 测试结果总结
                self.result_test_info["case_exetime"] = summaryData["case_exetime"]
        if self.result_folder and os.path.isfile(os.path.join(self.result_folder,"resultData.json")):
            with open(os.path.join(self.result_folder,"resultData.json"),"r") as j_file:
                resultData = json.load(j_file)
                self.result_test_info["cases_info"] = resultData
        try:
            if self.tv.reconnect(device_id=self.device_id,timeout=60):
                self.result_test_info["available_ram"] = self.tv_sys_data.available_ram(self.device_id)
                self.result_test_info["available_rom"] = self.tv_sys_data.available_rom(self.device_id)
        except Exception:
            print("fail to read tv available_ram & available_rom")
        self.result_test_info["plan_case_sum"] = self.log_test_info["caselist_sum"]
        self.result_test_info["finish_case_sum"] = self.log_test_info["finish_case_sum"]
        try:
            self.result_datas = self.data_fields.finish_field(tool_status=1,test_result_status = test_result_status,log_url=report_url,data=self.result_test_info)    # log_url字段填写的是报告的fds_url
            upload_res = self.upload_data_to_ptf.finish_data(self.result_datas,self.test_id)
            print("upload_result_info RES:",upload_res)
            if self.result_folder and os.path.isfile(os.path.join(self.result_folder, "test_schedule")):
                add_log(os.path.join(self.result_folder, "test_schedule"),"TEST FINISH")
            return upload_res
        except Exception as e:
            print("fail to upload result data")
            print(traceback.print_exc())  # 定位出错语句
            pass

    def run(self):
        self.upload_log = os.path.join(self.result_folder,"upload_data.log")
        add_log(self.upload_log,"run mode:{}".format(self.run_mode))
        upload_res = self.uploadnow_start_info()       # start data
        last_time = time.time()  # 第一次上报的时间点
        add_log(self.upload_log, time.ctime() + "\n" + str(self.start_datas))
        add_log(self.upload_log, "upload res:{}".format(upload_res))

        # todo upload log datas
        while self.running:
            time.sleep(5)
            TESTTIME = 1800       # 小米播放器，一个case时长可能会需要1个小时
            plan_pkgs_sum = 0
            if os.path.isfile(os.path.join(self.result_folder, "plan_testcase")):  # install & upgrade pkg finished
                plan_pkgs = open(os.path.join(self.result_folder, "plan_testcase"))
                plan_pkgs_lines = plan_pkgs.readlines()
                for pkg in plan_pkgs_lines:
                    if pkg.startswith("#") or pkg.startswith("="):
                        continue
                    plan_pkgs_sum += 1
                plan_pkgs_sum -= 1
                LOOP_O,LOOP_I = plan_pkgs_lines[0].split()  # 第一行写的是大小loop
                self.log_test_info["caselist_sum"] = plan_pkgs_sum * int(LOOP_I) * int(LOOP_O)
                self.log_test_info["LOOP_O"] = int(LOOP_O)
                self.log_test_info["LOOP_I"] = int(LOOP_I)

            if os.path.isfile(os.path.join(self.result_folder, "test_schedule")):  # 用 isfile能动态判断某个文件是否生成
                finish_pkgs = open(os.path.join(self.result_folder, "test_schedule"))
                finish_case_lines = finish_pkgs.readlines()
                # print(finish_case_lines[-1])
                lines_count = len(finish_case_lines)
                temp = finish_case_lines[-1].split()
                start_ = temp[0] + " " + temp[1]

                # todo 从跑case的状态到生成报告的状态的改变
                status_keyword = ["END TESTCASE","Finished UpdateIssue"]
                if self.log_test_info["schedule"] == 1 and any([keyword in finish_case_lines[-1] for keyword in status_keyword]):
                    self.upload_now = True
                if "END TESTCASE" in finish_case_lines[-1]:
                    self.log_test_info["finish_case_sum"] = lines_count - 1
                    self.log_test_info["schedule"] = 2
                    self.log_test_info["temp_case_name"] = "UpdateIssue"
                    self.log_test_info["remaining_time"] = 0          # case跑完了则不再计算剩余时间了
                elif "Finished UpdateIssue" in finish_case_lines[-1]:
                    self.log_test_info["finish_case_sum"] = lines_count - 2
                    self.log_test_info["schedule"] = 2
                    self.log_test_info["temp_case_name"] = "Report"
                    self.log_test_info["remaining_time"] = 0
                elif "Finished Report" in finish_case_lines[-1]:
                    self.log_test_info["finish_case_sum"] = lines_count - 3
                    # todo stop upload log info
                    self.running = False
                    print("Report Finished,stop upload log data.")  # 然后在这里调用上传resultdate的接口
                elif "ABNORMAL EXIT" in finish_case_lines[-1]:
                    self.running = False
                    print("TEST ABNORMAL EXIT,upload resut data")
                elif "TEST FINISH" in finish_case_lines[-1]:     # 如果已经上传了结果数据，就不会在后面上传log data了
                    self.running = False
                    exit()

                elif plan_pkgs_sum != 0:  # 还没跑完case
                    self.log_test_info["schedule"] = 1  # 测试进程
                    self.log_test_info["temp_case_name"] = temp[-1]
                    self.log_test_info["finish_case_sum"] = lines_count - 1   # 已完成case数量
                    self.log_test_info["loop_o"] = temp[2].split(":")[1]
                    self.log_test_info["loop_i"] = temp[3].split(":")[1]

                    if self.plan_hours != 0:     # 如果有计划执行时长，按照开始执行时间计算剩余时长
                        self.remaining_time = int(self.plan_hours * 60 * 60 * 1.1) - (time.time() - self.starttest_time)
                        self.log_test_info["remaining_time"] = self.remaining_time if self.remaining_time > 0 else 0
                    else:
                        case_remaining_time = (plan_pkgs_sum * int(LOOP_I) * int(LOOP_O) - self.log_test_info["finish_case_sum"]) * TESTTIME    # 按照剩余case数量计算剩余测试时间
                        self.log_test_info["remaining_time"] = case_remaining_time if case_remaining_time > 0 else 0

            else:   # "test_schedule" 还没有这个文件
                self.log_test_info["schedule"] = 1  # 测试进程
                self.log_test_info["temp_case_name"] = "ENV SETUP"   # 测试前准备

                if self.plan_hours == 0:    # 如果没有计划执行时长，则是按照case数量来算
                    self.remaining_time = plan_pkgs_sum * int(LOOP_I) * int(LOOP_O) * TESTTIME
                else:
                    self.remaining_time = int(self.plan_hours * 60 * 60  * 1.1)   # 预计剩余时间，加上10%的缓冲时间
                self.log_test_info["remaining_time"] = self.remaining_time     # 预计剩余时间

            if self.running and time.time() - last_time >= self.start_datas["heartbeat_interval"] - 30:   # 提早一点点上报
                self.upload_now = True

            if self.upload_now:
                try:
                    check_device_status = self.tv.reconnect(device_id=self.device_id,timeout=60)
                    if check_device_status == False:
                        self.log_test_info["device_status"] = 1
                        self.log_test_info["available_ram"] = 0
                        self.log_test_info["available_rom"] = 0
                    elif check_device_status == True:    # 电视adb正常时才能读到这两个数据
                        self.log_test_info["device_status"] = 0
                        self.log_test_info["available_ram"] = self.tv_sys_data.available_ram(self.device_id)
                        self.log_test_info["available_rom"] = self.tv_sys_data.available_rom(self.device_id)
                    self.log_datas = self.data_fields.running_field(data=self.log_test_info, data_type=0)
                    upload_res = self.upload_data_to_ptf.running_data(self.log_datas)
                    print("upload log info RES:",upload_res)
                    add_log(self.upload_log, time.ctime() + "\n" + str(self.log_datas))
                    last_time = time.time()  # 更新上一次upload info的时间
                    self.upload_now = False
                except Exception as e:
                    pass

        # while break
        upload_res = self.uploadnow_result_info()    # 结果数据
        add_log(self.upload_log, time.ctime() + "\n" + str(self.result_datas))
        add_log(self.upload_log, "upload res:{}".format(upload_res))

    def date_delta(self,start):
        """计算当前case已经执行的时长"""
        start = datetime.datetime.strptime(start, "%Y-%m-%d %H:%M:%S")
        timeStamp_start = start.timestamp()
        timeStamp_end = int(time.time())
        return timeStamp_end - timeStamp_start

if __name__ == '__main__':
    device_id = sys.argv[1]
    testtype = sys.argv[2]
    result_folder = sys.argv[3]
    Address = sys.argv[4]
    build_url = sys.argv[5]
    build_user = sys.argv[6]
    run_mode = sys.argv[7]     # 上报数据到线上环境 or 测试环境
    plan_hours = int(sys.argv[8])
    if device_id.endswith(":5555"):
        device_id = device_id.split(":5555")[0]
    # u = UploadTestInfos(device_id,testtype,result_folder,Address,build_url,build_user,run_mode)
    # u.run()

    upload_abnormal_infos = UploadTestInfos(device_id=device_id, testtype=int(testtype), result_folder=None,
                                                 Address=Address, build_url=build_url, build_user=build_user,
                                                 run_mode=run_mode,plan_hours=plan_hours)
    upload_abnormal_infos.uploadnow_start_info()
    upload_abnormal_infos.uploadnow_result_info()


# python upload_info_ptf.py 111 freeguy_result aaa ddd ccc

