#!/usr/bin/python
# -*- coding:utf-8 -*-

import time
import sys
import os
from subprocess import PIPE, STDOUT, Popen

def timeout_command(command, timeout=30):
    """
    执行命令并返回结果
    """
    try:
        state = Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
        return out
    except Exception as e:
        print(f"命令执行失败: {e}")
        return ""

def press_power():
    """按电源键"""
    timeout_command('adb shell input keyevent KEYCODE_POWER')

def get_tv_manufacturer():
    """获取电视厂商"""
    try:
        manufacturer = timeout_command('adb shell getprop ro.product.manufacturer').strip().lower()
        print(f"电视厂商: {manufacturer}")
        return manufacturer
    except:
        return 'unknown'

def is_tcl_tv():
    """判断是否为TCL电视"""
    return 'tcl' in get_tv_manufacturer()

def test_tcl_str():
    """测试TCL电视STR功能"""
    print("=" * 50)
    print("开始测试TCL电视STR功能")
    print("=" * 50)
    
    if not is_tcl_tv():
        print("当前设备不是TCL电视，退出测试")
        return False
    
    max_retries = 5
    
    # 获取当前屏幕状态
    print("检查当前屏幕状态...")
    check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
    print(f"当前屏幕状态: {check_screen}")
    
    if 'true' in check_screen:
        print("屏幕已亮，开始熄屏测试")
        print("-" * 30)
        
        # 尝试熄屏，重试5次
        for i in range(max_retries):
            print(f"第{i+1}次尝试熄屏...")
            press_power()
            time.sleep(5)
            check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
            print(f"熄屏后状态: {check_screen}")
            
            if 'false' in check_screen:
                print(f"✅ 屏幕熄灭成功 (第{i+1}次尝试)")
                break
            else:
                print(f"❌ 屏幕熄灭失败，重试 (第{i+1}次尝试)")
        else:
            print("❌ 屏幕熄灭失败，已重试5次")
            return False
            
        print("-" * 30)
        print("熄屏成功，等待3秒后开始亮屏测试...")
        time.sleep(3)
        
        # 熄屏成功后，尝试亮屏，重试5次
        print("屏幕已熄灭，开始亮屏测试")
        for i in range(max_retries):
            print(f"第{i+1}次尝试亮屏...")
            press_power()
            time.sleep(5)
            check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
            print(f"亮屏后状态: {check_screen}")
            
            if 'true' in check_screen:
                print(f"✅ 屏幕亮屏成功 (第{i+1}次尝试)")
                return True
            else:
                print(f"❌ 屏幕亮屏失败，重试 (第{i+1}次尝试)")
        
        print("❌ 屏幕亮屏失败，已重试5次")
        return False
        
    else:
        print("屏幕已熄灭，开始亮屏测试")
        print("-" * 30)
        
        # 尝试亮屏，重试5次
        for i in range(max_retries):
            print(f"第{i+1}次尝试亮屏...")
            press_power()
            time.sleep(5)
            check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
            print(f"亮屏后状态: {check_screen}")
            
            if 'true' in check_screen:
                print(f"✅ 屏幕亮屏成功 (第{i+1}次尝试)")
                
                # 亮屏成功后，测试熄屏
                print("-" * 30)
                print("亮屏成功，等待3秒后开始熄屏测试...")
                time.sleep(3)
                
                for j in range(max_retries):
                    print(f"第{j+1}次尝试熄屏...")
                    press_power()
                    time.sleep(5)
                    check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
                    print(f"熄屏后状态: {check_screen}")
                    
                    if 'false' in check_screen:
                        print(f"✅ 屏幕熄灭成功 (第{j+1}次尝试)")
                        return True
                    else:
                        print(f"❌ 屏幕熄灭失败，重试 (第{j+1}次尝试)")
                
                print("❌ 屏幕熄灭失败，已重试5次")
                return False
                
            else:
                print(f"❌ 屏幕亮屏失败，重试 (第{i+1}次尝试)")
        
        print("❌ 屏幕亮屏失败，已重试5次")
        return False

def test_multiple_str_cycles(cycles=3):
    """测试多次STR循环"""
    print("=" * 50)
    print(f"开始测试{cycles}次STR循环")
    print("=" * 50)
    
    success_count = 0
    
    for cycle in range(cycles):
        print(f"\n🔄 第{cycle+1}次STR循环测试")
        print("=" * 30)
        
        result = test_tcl_str()
        if result:
            success_count += 1
            print(f"✅ 第{cycle+1}次STR循环成功")
        else:
            print(f"❌ 第{cycle+1}次STR循环失败")
        
        if cycle < cycles - 1:  # 不是最后一次
            print("等待10秒后进行下一次循环...")
            time.sleep(10)
    
    print("\n" + "=" * 50)
    print("STR循环测试结果统计")
    print("=" * 50)
    print(f"总测试次数: {cycles}")
    print(f"成功次数: {success_count}")
    print(f"失败次数: {cycles - success_count}")
    print(f"成功率: {(success_count/cycles)*100:.1f}%")
    
    return success_count == cycles

def check_device_connection():
    """检查设备连接"""
    print("检查设备连接...")
    devices = timeout_command('adb devices')
    print("当前连接的设备:")
    print(devices)
    
    if "4C04902902004A0B4" in devices:
        print("✅ 检测到TCL电视设备: 4C04902902004A0B4")
        return True
    else:
        print("❌ 未检测到TCL电视设备")
        return False

if __name__ == "__main__":
    print("TCL电视STR功能测试脚本")
    print("设备信息:")
    print("- TCL电视: 4C04902902004A0B4")
    print("- 手机: 711d9be")
    print()
    
    # 检查设备连接
    if not check_device_connection():
        print("请确保TCL电视已连接")
        sys.exit(1)
    
    # 设置ADB目标设备
    os.environ['ANDROID_SERIAL'] = '4C04902902004A0B4'
    
    if len(sys.argv) > 1:
        try:
            cycles = int(sys.argv[1])
            print(f"执行{cycles}次STR循环测试")
            test_multiple_str_cycles(cycles)
        except ValueError:
            print("参数错误，请输入数字")
            print("用法: python test_tcl_str.py [循环次数]")
    else:
        print("执行单次STR测试")
        result = test_tcl_str()
        if result:
            print("\n🎉 STR测试成功！")
        else:
            print("\n💥 STR测试失败！")
